package com.b2broker.b2core.tests

import com.b2broker.b2core.screens.LandingTestScreen
import com.b2broker.b2core.testdevice.Mock
import dagger.hilt.android.testing.HiltAndroidTest
import io.qameta.allure.kotlin.AllureId
import io.qameta.allure.kotlin.Epic
import io.qameta.allure.kotlin.Feature
import io.qameta.allure.kotlin.junit4.DisplayName
import io.qameta.allure.kotlin.junit4.Tag
import io.qameta.allure.kotlin.junit4.Tags
import org.junit.Test

@Mock
@Tags(Tag("Android"))
@Epic("Sign In")
@Feature("2FA google | SMS confirmation")
@HiltAndroidTest
class TwoFaFlowTests : BaseTest() {
    @Test
    @AllureId("15566")
    @DisplayName("Login with SMS confirmation Flow")
    fun testSuccessfulLoginViaSmsTfa() {
        val tfaSmsScreen = LandingTestScreen.goToSignInScreen()
            .signInTwoFaSms()

        tfaSmsScreen.inputCodeWithRedirectToHomeScreen("12345")
            .assertDisplayed()
    }

    @Test
    @AllureId("8037")
    @DisplayName("Login with Google 2FA confirmation flow")
    fun testSuccessfulLoginViaGoogleTfa() {
        val tfaGoogleScreen = LandingTestScreen.goToSignInScreen()
            .signInTwoFaGoogle()

        tfaGoogleScreen.inputCodeWithRedirectToHomeScreen("123456")
            .assertDisplayed()
    }
}
