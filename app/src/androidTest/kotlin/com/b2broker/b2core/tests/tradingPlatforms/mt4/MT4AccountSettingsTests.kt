package com.b2broker.b2core.tests.tradingPlatforms.mt4

import com.b2broker.b2core.model.platforms.PlatformType
import com.b2broker.b2core.screens.Navigator
import com.b2broker.b2core.screens.trading.platforms.mt4.Mt4TestScreen
import com.b2broker.b2core.testdevice.Mock
import com.b2broker.b2core.tests.tradingPlatforms.basePlatformsTests.BaseAccountSettingsTests
import dagger.hilt.android.testing.HiltAndroidTest
import io.qameta.allure.kotlin.AllureId
import io.qameta.allure.kotlin.Epic
import io.qameta.allure.kotlin.Feature
import io.qameta.allure.kotlin.Story
import io.qameta.allure.kotlin.junit4.DisplayName
import io.qameta.allure.kotlin.junit4.Tag
import io.qameta.allure.kotlin.junit4.Tags
import org.junit.Test

@Mock
@Tags(Tag("Android"))
@Epic("Trading")
@Feature("MT4")
@HiltAndroidTest
class MT4AccountSettingsTests : BaseAccountSettingsTests() {

    @Test
    @AllureId("61599")
    @Story("Archive")
    @DisplayName("Archive screen Error state")
    fun testTradingMT4AccountSettingsArchiveBlockIsDisplayed() {
        testTradingAccountSettingsArchiveBlockIsDisplayed(PlatformType.MetaTrader4)
    }

    @Test
    @AllureId("61602")
    @Story("Archive")
    @DisplayName("Archive Live account")
    fun testTradingMT4AccountSettingsArchiveLiveAccount() {
        testTradingAccountSettingsArchiveLiveAccount(PlatformType.MetaTrader4)
    }

    @Test
    @AllureId("61600")
    @Story("Account Settings")
    @DisplayName("Edit account name")
    fun testTradingMT4AccountSettingsChangeAccountName() {
        testTradingAccountSettingsChangeAccountName(PlatformType.MetaTrader4)
    }

    @Test
    @AllureId("61616")
    @Story("Account Settings")
    @DisplayName("Get Error State on edit name action")
    fun testTradingMT4AccountSettingsChangeAccountNameErrorHandling() {
        testTradingAccountSettingsChangeAccountNameErrorHandling(PlatformType.MetaTrader4)
    }

    override fun openPlatformScreen(platformType: PlatformType) = Navigator.goToTradingTab().openPlatformWithType<Mt4TestScreen>(platformType)
}
