package com.b2broker.b2core.tests.wallets

import com.b2broker.b2core.screens.LandingTestScreen
import com.b2broker.b2core.screens.Navigator
import com.b2broker.b2core.testdevice.Mock
import com.b2broker.b2core.tests.BaseTest
import dagger.hilt.android.testing.HiltAndroidTest
import io.qameta.allure.kotlin.AllureId
import io.qameta.allure.kotlin.Epic
import io.qameta.allure.kotlin.Feature
import io.qameta.allure.kotlin.junit4.DisplayName
import io.qameta.allure.kotlin.junit4.Tag
import io.qameta.allure.kotlin.junit4.Tags
import org.junit.Test

@Mock
@Tags(Tag("Android"))
@Epic("Wallets")
@Feature("Redirects")
@HiltAndroidTest
class RedirectsTests : BaseTest() {
    @Test
    @AllureId("38609")
    @DisplayName("Open finance screens from wallets list screen (Deposit, Withdraw, Transfer)")
    fun testOpenFinanceScreensFromWalletsScreen() {
        LandingTestScreen.signInWithWait()
        Navigator
            .goToWalletsTab()
            .financeButtons
            .goToDeposit()
            .assertDisplayed()
            .goBackToWalletsScreen()
            .financeButtons
            .goToWithdraw()
            .goBackToWalletsScreen()
            .financeButtons
            .goToTransfer()
            .assertDisplayed()
            .goBackToWalletsScreen()
            .assertDisplayed()
    }

    @Test
    @AllureId("8489")
    @DisplayName("Open finance screens from wallet details screen (Deposit, Withdraw, Transfer)")
    fun testOpenFinanceScreensFromWalletDetailsScreen() {
        val walletId = "18085"
        LandingTestScreen.signInWithWait()
        Navigator
            .goToWalletsTab()
            .openWalletWithWalletId(walletId)
            .financeButtons
            .goToDeposit()
            .assertDisplayed()
            .goBackToWalletDetailsScreen()
            .financeButtons
            .goToWithdraw()
            .goBackToWalletDetailsScreen()
            .financeButtons
            .goToTransfer()
            .assertDisplayed()
            .goBackToWalletDetailsScreen()
            .assertDisplayed()
    }
}
