package com.b2broker.b2core.tests.finance

import com.b2broker.b2core.data.CurrencyEnum
import com.b2broker.b2core.data.TransferInfoData
import com.b2broker.b2core.screens.LandingTestScreen
import com.b2broker.b2core.screens.Navigator
import com.b2broker.b2core.testdevice.Mock
import com.b2broker.b2core.tests.BaseTest
import dagger.hilt.android.testing.HiltAndroidTest
import io.qameta.allure.kotlin.AllureId
import io.qameta.allure.kotlin.Epic
import io.qameta.allure.kotlin.Feature
import io.qameta.allure.kotlin.junit4.DisplayName
import io.qameta.allure.kotlin.junit4.Tag
import io.qameta.allure.kotlin.junit4.Tags
import org.junit.Test

@Tags(Tag("Android"))
@Mock
@Epic("Finance")
@Feature("Transfer")
@HiltAndroidTest
class TransferFlowTests : BaseTest() {
    @Test
    @AllureId("35632")
    @DisplayName("Transfer full amount from wallet by selecting Amount from chips")
    fun testEnterTransferAmountWithChips() {
        val percentageAmounts = listOf("25", "50", "75", "100")
        val transferInfo = TransferInfoData(
            fromAccountId = "14830",
            toAccountId = "**********",
            transferAmount = "100",
            transferCurrency = CurrencyEnum.USD
        )

        LandingTestScreen.signInWithWait()

        val fundsScreen = Navigator.goToFinanceTab()
        val transferScreen = fundsScreen.goToTransferSection()
        transferScreen.selectToAccountLastItem()

        transferScreen.selectAndAssertPercentageAmount(percentageAmounts)

        val transferConfirmScreen = transferScreen.confirmTransfer()
        transferConfirmScreen.transferInfo.assertTransferDetails(transferInfo)

        val transferResultScreen = transferConfirmScreen.confirmTransfer()
        transferResultScreen.assertTransferStatus("Successful Transfer")
        transferResultScreen.transferInfo.assertTransferResultDetails(transferInfo)

        transferResultScreen.doneWithRedirectToTransferScreen()
    }

    @Test
    @AllureId("35633")
    @DisplayName("Transfer partial amount from wallet by entering Amount from keyboard")
    fun testEnterTransferAmount() {
        val transferInfo = TransferInfoData(
            fromAccountId = "18291",
            toAccountId = "0",
            transferAmount = "10",
            transferCurrency = CurrencyEnum.USD
        )

        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()
        transferScreen.selectToAccountLastItem()
        transferScreen.insertAmount(transferInfo.transferAmount)

        val transferConfirmScreen = transferScreen.confirmTransfer()
        transferConfirmScreen.transferInfo.assertTransferDetails(transferInfo)

        val transferResultScreen = transferConfirmScreen.confirmTransfer()
        transferResultScreen.assertTransferStatus("Transfer Pending")
        transferResultScreen.transferInfo.assertTransferResultDetails(transferInfo)

        transferResultScreen.doneWithRedirectToTransferScreen()
    }

    @Test
    @AllureId("35657")
    @DisplayName("Amount field reset when From field changed")
    fun testResetTransferAmountWhenFromFieldChanged() {
        val transferAmount = "10"
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()
        transferScreen.selectToAccountLastItem()
        transferScreen.insertAmount(transferAmount)
        transferScreen.assertAmount(transferAmount)
        transferScreen.searchAndSelectFromAccountLastItem("14830")

        transferScreen.assertAmountIsEmpty()
        transferScreen.assertConfirmButtonDisabled()
    }

    @Test
    @AllureId("35629")
    @DisplayName("\"No options\" placeholder shown when From account has no To pair")
    fun testNoOptionsInToSelectWhenNoTransferPair() {
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()

        transferScreen.assertToAccount("No options")
        transferScreen.assertAmountIsEmpty()
        transferScreen.assertAmountDisabled()
        transferScreen.assertConfirmButtonDisabled()
    }

    @Test
    @AllureId("35630")
    @DisplayName("\"No options\" placeholder shown when no From account selected")
    fun testNoOptionsInFromSelectWhenNoAccountsAvailable() {
        val homeScreen = LandingTestScreen.signIn()
        homeScreen.banners.assertDisplayed()
        homeScreen.totalBalanceWidget.assertDisplayed()
        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()

        transferScreen.assertFromAccount("No options")
        transferScreen.assertToAccount("No options")
        transferScreen.assertAmountIsEmpty()
        transferScreen.assertAmountDisabled()
        transferScreen.assertConfirmButtonDisabled()
    }

    @Test
    @AllureId("8554")
    @DisplayName("Transfer button enables when all fields filled")
    fun testConfirmButtonEnabledWhenAllFieldsAreFilled() {
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()

        transferScreen.assertFromAccount("110 USD")
        transferScreen.assertToAccount("Select Destination Account")
        transferScreen.assertAmountIsEmpty()
        transferScreen.assertConfirmButtonDisabled()

        transferScreen.searchAndSelectToAccountLastItem("14830")
        transferScreen.assertConfirmButtonDisabled()

        transferScreen.insertAmount("10")
        transferScreen.assertConfirmButtonEnabled()
    }

    @Test
    @AllureId("8555")
    @DisplayName("Validation on Amount field when Transfer amount bigger than wallet's balance")
    fun testValidationOnAmountFieldWhenAmountBiggerThanFromBalance() {
        val fromAccountId = "14830"
        val toAccountId = "********"
        val amountValidationMessage = "Should be less or equal to 0"
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()

        transferScreen.searchAndSelectFromAccountLastItem(fromAccountId)
        transferScreen.searchAndSelectToAccountLastItem(toAccountId)
        transferScreen.insertAmount("100")

        transferScreen.assertAmountValidation(amountValidationMessage)
    }

    @Test
    @AllureId("35766")
    @DisplayName("Validations on Amount field")
    fun testValidationsOnAmountField() {
        val toAccountId = "14830"
        val amountZeroValidationMessage = "Should be more or equal to 0"
        val transferInfo = TransferInfoData(
            fromAccountId = "********",
            toAccountId = "14830",
            transferAmount = "10",
            transferCurrency = CurrencyEnum.USD
        )
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab().goToTransferSection()

        transferScreen.searchAndSelectToAccountLastItem(toAccountId)
        transferScreen.searchAndSelectToAccountLastItem(toAccountId)
        transferScreen.insertAmount("0")
        transferScreen.assertAmountValidation(amountZeroValidationMessage)

        transferScreen.replaceAmount(transferInfo.transferAmount)
        val transferDetailsScreen = transferScreen.confirmTransfer()

        transferDetailsScreen.transferInfo.assertTransferDetails(transferInfo)
    }

    @Test
    @AllureId("35783")
    @DisplayName("Correct redirect to Transfer screen from Wallets")
    fun testCorrectRedirectFromWalletsScreen() {
        LandingTestScreen.signInWithWait()

        Navigator
            .goToWalletsTab()
            .financeButtons
            .goToTransfer()
            .assertDisplayed()
    }

    @Test
    @AllureId("29956")
    @DisplayName("Transfer screen unavailable when disabled in admin panel")
    fun testTransferSectionNotDisplayedWhenDisabledInAdminPanel() {
        LandingTestScreen.signInWithWait()

        val transferScreen = Navigator.goToFinanceTab()

        transferScreen.assertTransferSectionNotDisplayed()
    }
}
