package com.b2broker.b2core.tests.tradingPlatforms.mt5

import com.b2broker.b2core.model.platforms.PlatformType
import com.b2broker.b2core.screens.Navigator
import com.b2broker.b2core.screens.trading.platforms.mt5.Mt5TestScreen
import com.b2broker.b2core.testdevice.Mock
import com.b2broker.b2core.tests.tradingPlatforms.basePlatformsTests.BaseFlowTests
import dagger.hilt.android.testing.HiltAndroidTest
import io.qameta.allure.kotlin.AllureId
import io.qameta.allure.kotlin.Epic
import io.qameta.allure.kotlin.Feature
import io.qameta.allure.kotlin.Story
import io.qameta.allure.kotlin.junit4.DisplayName
import io.qameta.allure.kotlin.junit4.Tag
import io.qameta.allure.kotlin.junit4.Tags
import org.junit.Ignore
import org.junit.Test

@Mock
@Tags(Tag("Android"))
@Epic("Trading")
@Feature("MT5")
@HiltAndroidTest
class Mt5FlowTests : BaseFlowTests() {

    @Test
    @AllureId("58108")
    @Story("Platform screen")
    @DisplayName("Error blank screen")
    fun testShowErrorStateWhenAccountListFailsToLoad() {
        testShowErrorStateWhenAccountListFailsToLoad(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58326")
    @Story("Platform screen")
    @DisplayName("Empty blank screen when no accounts")
    fun testTradingShowEmptyPlatformStateWhenNoAccounts() {
        testTradingShowEmptyPlatformStateWhenNoAccounts(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58276")
    @Story("Platform screen")
    @DisplayName("Account tile details is displayed")
    fun testTradingPlatformTileWithAccountDetailsDisplayed() {
        testTradingPlatformTileWithAccountDetailsDisplayed(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58274")
    @Story("Platform screen")
    @DisplayName("Trade button displayed on the account tile")
    fun testTradingPlatformTileTradeButtonDisplayed() {
        testTradingPlatformTileTradeButtonDisplayed(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58275")
    @Story("Platform screen")
    @DisplayName("Trade button is not displayed on the account tile")
    fun testTradingPlatformTileTradeButtonNotDisplayed() {
        testTradingPlatformTileTradeButtonNotDisplayed(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58280")
    @Story("Platform screen")
    @DisplayName("Permissions | Deposit button")
    fun testTradingPlatformTileDepositButtonIsDisabledOnDemoAccount() {
        testTradingPlatformTileDepositButtonIsDisabledOnDemoAccount(PlatformType.MetaTrader5, "1910720")
    }

    @Test
    @AllureId("58230")
    @Story("Add new Account")
    @DisplayName("Account created via 'Create new Account' button")
    fun testTradingPlatformCreateAccountViaCreateNewAccountButton() {
        testTradingPlatformCreateAccountViaCreateNewAccountButton(
            platformType = PlatformType.MetaTrader5,
            currency = currency,
            isAgreement = false,
        )
    }

    @Test
    @AllureId("58266")
    @Story("Add new Account")
    @DisplayName("Customer Agreement checkbox displayed if agreementLink not null")
    fun testTradingPlatformCreateAccountAgreementCheckboxIsDisplayed() {
        testTradingPlatformCreateAccountAgreementCheckboxIsDisplayed(PlatformType.MetaTrader5, currency)
    }

    @Test
    @AllureId("58862")
    @Story("Add new Account")
    @DisplayName("Empty blank screen: No Products available to create an account")
    fun testTradingPlatformCreateAccountNoProductsToCreateNewAccount() {
        testTradingPlatformCreateAccountNoProductsToCreateNewAccount(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58864")
    @Story("Add new Account")
    @DisplayName("Error blank screen on currencies request failure")
    fun testTradingPlatformCreateAccountErrorStateOnCurrenciesRequest() {
        testTradingPlatformCreateAccountErrorStateOnCurrenciesRequest(PlatformType.MetaTrader5)
    }

    @Test
    @AllureId("58228")
    @Story("Add new Account")
    @DisplayName("Demo account is created via 'Plus' button")
    fun testTradingPlatformCreateDemoAccountViaPlusButton() {
        testTradingPlatformCreateDemoAccountViaPlusButton(
            platformType = PlatformType.MetaTrader5,
            accountType = "Netting",
            isAgreement = false,
        )
    }

    @Test
    @AllureId("58268")
    @Story("Add new Account")
    @DisplayName("Customer Agreement checkbox is not displayed if agreementLink null")
    fun testTradingCreateAccountCustomerAgreementCheckboxIsNotDisplayed() {
        testTradingCreateAccountCustomerAgreementCheckboxIsNotDisplayed(PlatformType.MetaTrader5, currency)
    }

    @Test
    @AllureId("58233")
    @Story("Add new Account")
    @DisplayName("Error: Limit on accounts reached")
    @Ignore("Fix the test in https://b2btech.atlassian.net/browse/PAN-34616")
    fun testTradingCreateAccountLimitOnAccountsReached() {
        testTradingCreateAccountLimitOnAccountsReached(PlatformType.MetaTrader5, currency)
    }

    override fun openPlatformScreen(platformType: PlatformType) = Navigator.goToTradingTab().openPlatformWithType<Mt5TestScreen>(platformType)
}
