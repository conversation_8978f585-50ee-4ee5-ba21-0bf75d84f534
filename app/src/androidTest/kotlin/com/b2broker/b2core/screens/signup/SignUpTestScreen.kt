package com.b2broker.b2core.screens.signup

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.b2broker.b2core.auth.signup.form.RegisterFormScreenTags.DYNAMIC_FIELDS
import com.b2broker.b2core.screens.ConfirmTestModal
import com.b2broker.b2core.screens.LandingTestScreen
import com.b2broker.b2core.screens.base.ConfirmScreenTestBase
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.screens.base.DynamicFields
import com.b2broker.b2core.screens.homescreen.HomeTestScreen
import com.b2broker.b2core.screens.tfa.TfaEmailTestScreen
import com.b2broker.b2core.screens.tfa.TfaSmsTestScreen
import com.b2broker.b2core.testdevice.utils.assertDisplayed

class SignUpTestScreen : ConfirmScreenTestBase(), Displayable {
    val dynamicFields = DynamicFields()
    private val confirmModal = ConfirmTestModal()

    fun confirmWithRedirectToEmptyScreen(): AdvancedStepTestScreen {
        return step("Confirm registration with redirect to Advanced step screen") {
            confirm { AdvancedStepTestScreen() }
        }
    }

    fun confirmWithRedirectToVerificationScreen(): VerificationStepTestScreen {
        return step("Confirm registration with redirect to Verification step screen") {
            confirm { VerificationStepTestScreen() }
        }
    }

    fun confirmWithRedirectToHomeScreen(): HomeTestScreen {
        return step("Confirm registration with redirect to Home screen") {
            confirm { HomeTestScreen }
        }
    }

    fun confirmWithRedirectToSmsTfa(): TfaSmsTestScreen {
        return step("Confirm registration with redirect to SMS 2FA screen") {
            confirm { TfaSmsTestScreen }
        }
    }

    fun confirmWithRedirectToEmailTfa(): TfaEmailTestScreen {
        return step("Confirm registration with redirect to Email 2FA screen") {
            super.confirm { TfaEmailTestScreen }
        }
    }

    fun goBackToRegistrationTypeSelection(): SignUpSelectionTestScreen {
        return goBack {
            confirmModal.confirm { SignUpSelectionTestScreen() }
        }
    }

    fun goBackToLandingScreen(): LandingTestScreen {
        return goBack {
            confirmModal.confirm { LandingTestScreen }
        }
    }

    override fun assertDisplayed(): SignUpTestScreen {
        step("Assert that Sign Up screen is displayed") {
            hasTestTag(DYNAMIC_FIELDS).assertDisplayed()
            assertConfirmButtonDisplayed()
            assertBackButtonDisplayed()
        }
        return this
    }
}
