package com.b2broker.b2core.screens.base.elements

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.atiurin.ultron.extensions.assertIsSelected
import com.b2broker.b2core.designsystem.component.tabs.B2TabTestTags.getB2TabTestTagByTitle
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.testdevice.utils.assertDisplayed
import com.b2broker.b2core.testdevice.utils.click

class B2TestTabs(tabsTestTag: String) : Displayable {
    private val tabs = hasTestTag(tabsTestTag)

    fun selectTab(name: String) {
        step("Select tab with name: $name") {
            val tab = hasTestTag(getB2TabTestTagByTitle(name))
            tab.assertDisplayed()
            tab.click()
            tab.assertIsSelected()
        }
    }

    override fun assertDisplayed(): B2TestTabs {
        step("Assert that Tabs are displayed") { tabs.assertDisplayed() }
        return this
    }
}
