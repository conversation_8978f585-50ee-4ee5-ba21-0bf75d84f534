package com.b2broker.b2core.screens.verification

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.screens.base.elements.TextValueTestField
import com.b2broker.b2core.testdevice.utils.assertDisplayed
import com.b2broker.b2core.testdevice.utils.assertNotDisplayed
import com.b2broker.b2core.testdevice.utils.assertTextContains
import com.b2broker.b2core.testdevice.utils.click
import com.b2broker.b2core.verification.verification.sections.VerificationLevelSectionTestTags.VERIFICATION_AVATAR
import com.b2broker.b2core.verification.verification.sections.VerificationLevelSectionTestTags.VERIFICATION_LEVEL
import com.b2broker.b2core.verification.verification.sections.VerificationLevelSectionTestTags.VERIFICATION_UPGRADE_BUTTON

object VerificationLevelTestBlock : Displayable {
    private val avatar = hasTestTag(VERIFICATION_AVATAR)
    private val verificationLevelValue = hasTestTag(VERIFICATION_LEVEL)
    private val depositLimitValue = TextValueTestField("Daily Deposit")
    private val withdrawLimitValue = TextValueTestField("Daily Withdraw")
    private val transferLimitValue = TextValueTestField("Transfer Min")
    private val autoWithdrawLimitValue = TextValueTestField("Auto Withdraw")
    private val upgradeButton = hasTestTag(VERIFICATION_UPGRADE_BUTTON)

    fun upgradeVerificationLevel(): VerificationLevelTestBlock {
        step("Upgrade Verification Level") { upgradeButton.click() }
        return this
    }

    fun assertUpgradeButtonIsDisplayed(): VerificationLevelTestBlock {
        step("Assert that Upgrade button is displayed") {
            upgradeButton.assertDisplayed()
        }
        return this
    }

    fun assertUpgradeButtonIsNotDisplayed(): VerificationLevelTestBlock {
        step("Assert that Upgrade button is not displayed") {
            upgradeButton.assertNotDisplayed()
        }
        return this
    }

    fun assertVerificationLevel(verificationLevel: String): VerificationLevelTestBlock {
        step("Assert that Verification Level equals to: $verificationLevel") {
            verificationLevelValue.assertTextContains(verificationLevel)
        }
        return this
    }

    fun assertVerificationDetails(
        depositLimit: String,
        withdrawLimit: String,
        transferLimit: String,
        autoWithdrawLimit: String
    ) {
        step(
            """
            Assert that:
            - Daily Deposit limit equals to: $depositLimit
            - Daily Withdraw limit equals to: $withdrawLimit
            - Transfer Min limit equals to: $transferLimit
            - Auto Withdraw limit equals to: $autoWithdrawLimit
            """.trimIndent()
        ) {
            depositLimitValue.assertValue(depositLimit)
            withdrawLimitValue.assertValue(withdrawLimit)
            transferLimitValue.assertValue(transferLimit)
            autoWithdrawLimitValue.assertValue(autoWithdrawLimit)
        }
    }

    override fun assertDisplayed(): VerificationLevelTestBlock {
        step("Assert that Verification Level block is displayed") {
            avatar.assertDisplayed()
            verificationLevelValue.assertDisplayed()
            depositLimitValue.assertDisplayed()
            withdrawLimitValue.assertDisplayed()
            transferLimitValue.assertDisplayed()
            autoWithdrawLimitValue.assertDisplayed()
        }
        return this
    }
}
