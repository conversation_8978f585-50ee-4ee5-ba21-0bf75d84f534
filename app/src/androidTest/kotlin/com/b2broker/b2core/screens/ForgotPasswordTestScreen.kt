package com.b2broker.b2core.screens

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.atiurin.ultron.extensions.typeText
import com.b2broker.b2core.screens.base.ConfirmScreenTestBase
import com.b2broker.b2core.screens.tfa.TfaEmailTestScreen
import com.b2broker.b2core.testdevice.utils.assertTextContains

object ForgotPasswordTestScreen : ConfirmScreenTestBase() {
    private val emailInput = hasTestTag("ValueInput")

    fun resetPassword(email: String, confirmationCode: String, newPassword: String = "123456As@"): SignInTestScreen {
        step("Go through Forgot password flow:") {
            fillInEmailAndProceed(email)
                .inputCodeWithRedirectToResetPassword(confirmationCode)
                .inputNewPasswordAndReturnToSignInScreen(newPassword)
        }
        return SignInTestScreen
    }

    fun enterEmail(email: String): ForgotPasswordTestScreen {
        step("Enter email: $email") { emailInput.typeText(email) }
        return this
    }

    fun goBackToSignInScreen(): SignInTestScreen {
        return goBack { SignInTestScreen }
    }

    fun assertEmailFieldValidation(message: String): ForgotPasswordTestScreen {
        step("Assert that email field have validation: $message") {
            emailInput.assertTextContains(message)
        }
        return this
    }

    fun assertEmailFieldValue(value: String): ForgotPasswordTestScreen {
        step("Assert that email field contains value: $value") {
            emailInput.assertTextContains(value)
        }
        return this
    }

    fun fillInEmailAndProceed(email: String): TfaEmailTestScreen {
        step("Fill in Email field with value: $email and click on Continue button") {
            enterEmail(email)
        }
        return confirm { TfaEmailTestScreen }
    }

    fun confirmWithValidation() = confirm { ForgotPasswordTestScreen }
}
