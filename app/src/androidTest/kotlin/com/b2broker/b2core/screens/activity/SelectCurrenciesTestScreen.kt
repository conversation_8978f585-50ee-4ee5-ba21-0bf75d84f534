package com.b2broker.b2core.screens.activity

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.b2broker.b2core.designsystem.component.topbar.B2TopBarTestTags.CROSS_ICON
import com.b2broker.b2core.profile.ProfileScreenTestTags.CONFIRM_BUTTON
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.screens.base.SearchAndSelectTestScreen
import com.b2broker.b2core.testdevice.utils.assertDisplayed
import com.b2broker.b2core.testdevice.utils.click

object SelectCurrenciesTestScreen : SearchAndSelectTestScreen("B2SearchScreenItemList"), Displayable {
    private val confirmButton = hasTestTag(CONFIRM_BUTTON)
    private val clearButton = hasTestTag(CROSS_ICON)

    fun apply(): ActivityTestScreen {
        step("Press on Apply button") { confirmButton.click() }
        return ActivityTestScreen
    }

    fun clickOnClearButton() = step("Clear applied filter by Currency") {
        clearButton.click()
    }

    override fun assertDisplayed(): SearchAndSelectTestScreen {
        step("Assert that Search and select screen is displayed") {
            super.assertDisplayed()
            confirmButton.assertDisplayed()
            clearButton.assertDisplayed()
        }
        return this
    }
}
