package com.b2broker.b2core.screens.trading.platforms

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.atiurin.ultron.core.compose.list.composeList
import com.atiurin.ultron.extensions.assertTextContains
import com.b2broker.b2core.data.AccountDetailsTabsEnum
import com.b2broker.b2core.data.OrderDetailsEnum
import com.b2broker.b2core.data.TransactionTypeTest
import com.b2broker.b2core.designsystem.component.listitem.LazyListItemPosition
import com.b2broker.b2core.designsystem.component.tabs.B2TabTestTags.getB2TabTestTagByTitle
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.screens.base.elements.B2TestChips
import com.b2broker.b2core.screens.base.elements.B2TestTabs
import com.b2broker.b2core.screens.base.elements.ListItem
import com.b2broker.b2core.screens.base.multistate.MultiStateTestScreen
import com.b2broker.b2core.screens.finance.deposit.DepositTestScreen
import com.b2broker.b2core.screens.finance.transfer.TransferTestScreen
import com.b2broker.b2core.screens.finance.withdraw.WithdrawTestScreen
import com.b2broker.b2core.testdevice.utils.assertDisplayed
import com.b2broker.b2core.testdevice.utils.assertNotDisplayed
import com.b2broker.b2core.testdevice.utils.click
import com.b2broker.b2core.trader.platform.components.TradingPlatformAccountInfoTestTags.ITEM_ICON_COPY
import com.b2broker.b2core.trader.platform.deals.components.TraderPlatformDealDetailsTestTags.getDealDetailParamTestTagByName
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.ACCOUNT_DETAILS_ROOT
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.DEALS_HISTORY
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.TOP_BAR_ICON_FAVOURITE
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.TOP_BAR_ICON_SETTINGS
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.TRADER_PLATFORM_ACCOUNT_DETAILS_TABS
import com.b2broker.b2core.trader.platform.details.TraderPlatformAccountDetailsScreenTestTags.TRADE_BUTTON
import com.b2broker.b2core.trader.platform.details.components.OpenPositionsTabTestTags.OPEN_POSITIONS_LIST
import com.b2broker.b2core.trader.platform.details.components.StatisticsTabTestTags.CHART
import com.b2broker.b2core.trader.platform.details.components.StatisticsTabTestTags.FILTER_CHART_RANGE
import com.b2broker.b2core.trader.platform.details.components.getTraderOperationButtonTestTagByName

object AccountDetailsBaseTestScreen : PlatformBaseTestScreen(), Displayable, MultiStateTestScreen {
    private val itemIconCopy = hasTestTag(ITEM_ICON_COPY)
    private val accountDetailsTabs = B2TestTabs(TRADER_PLATFORM_ACCOUNT_DETAILS_TABS)
    private val openPositionsTab = hasTestTag(getB2TabTestTagByTitle(AccountDetailsTabsEnum.OPEN_POSITIONS.tabName))
    private val statisticsTab = hasTestTag(getB2TabTestTagByTitle(AccountDetailsTabsEnum.STATISTICS.tabName))
    private val statisticsFilterChips = B2TestChips()
    private val statisticsFilterRangeValue = hasTestTag(FILTER_CHART_RANGE)
    private val statisticsChart = hasTestTag(CHART)
    private val accountDetailsRoot = hasTestTag(ACCOUNT_DETAILS_ROOT)
    private val dealsHistoryButton = hasTestTag(DEALS_HISTORY)
    private val topBarIconFavourite = hasTestTag(TOP_BAR_ICON_FAVOURITE)
    private val topBarIconSettings = hasTestTag(TOP_BAR_ICON_SETTINGS)
    private val tradeButton = hasTestTag(TRADE_BUTTON)
    private val withdrawButton = hasTestTag(getPaymentButton(TransactionTypeTest.WITHDRAWAL.transactionName))
    private val depositButton = hasTestTag(getPaymentButton(TransactionTypeTest.DEPOSIT.transactionName))
    private val transferButton = hasTestTag(getPaymentButton(TransactionTypeTest.TRANSFER.transactionName))
    private val openPositionsList = composeList(
        hasTestTag(OPEN_POSITIONS_LIST),
        positionPropertyKey = LazyListItemPosition,
    )

    fun openAccountSettings(): AccountSettingsBaseTestScreen {
        step("Open account settings") {
            topBarIconSettings.click()
        }
        return AccountSettingsBaseTestScreen
    }

    fun addToFavorite(): AccountDetailsBaseTestScreen {
        step("Add an account to favorites") {
            topBarIconFavourite.click()
        }
        return this
    }

    fun copyAccountID(): AccountDetailsBaseTestScreen {
        step("Copy account ID") {
            itemIconCopy.click()
        }
        return this
    }

    fun clickOnWithdrawButton(): WithdrawTestScreen {
        step("Click on the Withdraw button from account details") {
            withdrawButton.click()
        }
        return WithdrawTestScreen
    }

    fun clickOnDepositButton(): DepositTestScreen {
        step("Click on the Deposit button from account details") {
            depositButton.click()
        }
        return DepositTestScreen
    }

    fun clickOnTransferButton(): TransferTestScreen {
        step("Click on the Transfer button from account details") {
            transferButton.click()
        }
        return TransferTestScreen
    }

    fun selectAccountDetailsTab(name: AccountDetailsTabsEnum): AccountDetailsBaseTestScreen {
        accountDetailsTabs.selectTab(name.tabName)
        return this
    }

    fun selectStatisticsRangeFilterByTag(filterName: String): AccountDetailsBaseTestScreen {
        step("Click on filter with name $filterName") {
            statisticsFilterChips.clickOnItemWithLabel(filterName)
        }
        return this
    }

    fun clickOnShowDealsHistoryButton(): AccountDealsHistoryBaseTestScreen {
        step("Click on 'Show Deals History' button") {
            dealsHistoryButton.click()
        }
        return AccountDealsHistoryBaseTestScreen
    }

    fun clickOnDealByIndex(index: Int): AccountDetailsBaseTestScreen {
        step("Click on open position at index $index in the Open Positions list.") {
            val item = openPositionsList.getItem<ListItem>(index)
            item.click()
        }
        return this
    }

    fun assertOrderDetailsDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that order details are displayed") {
            OrderDetailsEnum.entries.forEach { value ->
                hasTestTag(getDealDetailParamTestTagByName(value.fieldName)).assertDisplayed()
            }
        }
        return this
    }

    fun assertItemOnIndexIsNotExist(index: Int): AccountDetailsBaseTestScreen {
        step("Assert that item with index $index is not displayed") {
            val item = openPositionsList.getItem<ListItem>(index)
            item.assertIsNotDisplayed()
        }
        return this
    }

    fun assertTradeButtonDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that the trade button is displayed") {
            tradeButton.assertDisplayed()
        }
        return this
    }

    fun assertTradeButtonNotDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that the trade button is NOT displayed") {
            tradeButton.assertNotDisplayed()
        }
        return this
    }

    override fun assertDisplayed(): AccountDetailsBaseTestScreen {
        step(
            """
            Assert all essential trading screen elements are displayed:
            - Open Positions Tab
            - Deals History Tab
            - Account Details Section
            - Favorite Icon (Top Bar)
            - Settings Icon (Top Bar)
            - Copy Item Icon
            - Trade Button
            - Deposit Button
            - Withdraw Button
            - Transfer Button
            """.trimIndent(),
        ) {
            openPositionsTab.assertDisplayed()
            dealsHistoryButton.assertDisplayed()
            accountDetailsRoot.assertDisplayed()
            topBarIconFavourite.assertDisplayed()
            topBarIconSettings.assertDisplayed()
            itemIconCopy.assertDisplayed()
            tradeButton.assertDisplayed()
            depositButton.assertDisplayed()
            withdrawButton.assertDisplayed()
            transferButton.assertDisplayed()
        }
        return this
    }

    fun assertOpenPositionsTabIsNotDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that there is no Open Positions tab for Demo account") {
            dealsHistoryButton.assertNotDisplayed()
            openPositionsTab.assertNotDisplayed()
        }
        return this
    }

    fun assertOpenPositionsTabIsDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that Open Positions tab is Displayed") {
            dealsHistoryButton.assertDisplayed()
            openPositionsTab.assertDisplayed()
        }
        return this
    }

    fun assertStatisticsTabIsDisplayed(): AccountDetailsBaseTestScreen {
        step("Assert that Statistics tab is Displayed") {
            statisticsFilterChips.assertDisplayed()
            statisticsTab.assertDisplayed()
            statisticsFilterRangeValue.assertDisplayed()
            statisticsChart.assertDisplayed()
        }
        return this
    }

    fun assertFilterDateRangeIsDisplayed(dateRange: String): AccountDetailsBaseTestScreen {
        step("Assert that filtered date range $dateRange is displayed") {
            statisticsFilterRangeValue.assertTextContains(dateRange)
            statisticsChart.assertDisplayed()
        }
        return this
    }

    private fun getPaymentButton(buttonName: String): String {
        return getTraderOperationButtonTestTagByName(buttonName)
    }
}
