package com.b2broker.b2core.screens

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.atiurin.ultron.core.compose.list.UltronComposeList
import com.atiurin.ultron.page.Screen
import com.b2broker.b2core.screens.base.Displayable
import com.b2broker.b2core.testdevice.utils.assertDisplayed
import com.b2broker.b2core.testdevice.utils.click
import com.b2broker.b2core.testdevice.utils.inputText

object CountryCodeSelectTestScreen : Screen<CountryCodeSelectTestScreen>(), Displayable {
    private val searchInput = hasTestTag("SearchTextField")
    private val countryCodeList = UltronComposeList(hasTestTag("B2SearchScreenItemList"))
    private val confirmButton = hasTestTag("ConfirmButton")

    fun searchAndSelectCountry(countryName: String) {
        step("Search and select country: $countryName") {
            searchInput.inputText(countryName)
            countryCodeList.firstVisibleItem().click()
            confirmButton.click()
        }
    }

    override fun assertDisplayed(): CountryCodeSelectTestScreen {
        step("Country code select screen is displayed") {
            searchInput.assertDisplayed()
            countryCodeList.assertDisplayed()
            confirmButton.assertDisplayed()
        }
        return this
    }
}
