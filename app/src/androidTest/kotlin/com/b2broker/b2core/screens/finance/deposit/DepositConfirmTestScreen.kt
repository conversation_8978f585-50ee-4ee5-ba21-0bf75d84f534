package com.b2broker.b2core.screens.finance.deposit

import androidx.compose.ui.test.hasTestTag
import com.atiurin.ultron.allure.step.step
import com.atiurin.ultron.extensions.assertTextContains
import com.b2broker.b2core.funds.deposit.done.DepositDoneScreenTestTags.CONFIRM_BUTTON
import com.b2broker.b2core.funds.deposit.done.DepositDoneScreenTestTags.DEPOSIT_QR_CONTENT
import com.b2broker.b2core.funds.deposit.done.DepositDoneScreenTestTags.DONE_CONTENT_TABLE
import com.b2broker.b2core.funds.deposit.done.DepositDoneScreenTestTags.GO_TO_ACTIVITY_BUTTON
import com.b2broker.b2core.funds.deposit.done.DepositDoneScreenTestTags.TABLE_CONTENT
import com.b2broker.b2core.funds.deposit.done.component.DepositDonePageTestTags.DESCRIPTION
import com.b2broker.b2core.screens.base.ConfirmScreenTestBase
import com.b2broker.b2core.screens.base.elements.StatusTestBlock
import com.b2broker.b2core.screens.base.elements.WebViewInteractionHelper
import com.b2broker.b2core.testdevice.utils.assertDisplayed

object DepositConfirmTestScreen : ConfirmScreenTestBase() {
    private val depositDoneTable = hasTestTag(TABLE_CONTENT)
    private val depositQrContent = hasTestTag(DEPOSIT_QR_CONTENT)
    private val addressField = hasTestTag(DONE_CONTENT_TABLE)
    private val depositDonePageDescription = hasTestTag(DESCRIPTION)
    private val depositDoneButton = hasTestTag(CONFIRM_BUTTON)
    private val depositGoToActivityButton = hasTestTag(GO_TO_ACTIVITY_BUTTON)
    private val depositStatus = StatusTestBlock
    private val webViewHelper = WebViewInteractionHelper

    fun assertAddressFieldDisplayed(): DepositConfirmTestScreen {
        step("Assert that Address field is displayed") {
            addressField.assertDisplayed()
        }
        return this
    }

    fun assertDoneTable(): DepositConfirmTestScreen {
        step("Assert that Address field is displayed") {
            depositDoneTable.assertDisplayed()
        }
        return this
    }

    fun assertQrContent(): DepositConfirmTestScreen {
        step("Assert that QR-code is displayed") {
            depositQrContent.assertDisplayed()
        }
        return this
    }

    fun assertMessage(message: String): DepositConfirmTestScreen {
        step("Assert that content or mobile_content message is displayed") {
            depositDonePageDescription.assertTextContains(message)
        }
        return this
    }

    fun assertDoneButtonIsDisplayed(): DepositConfirmTestScreen {
        step("Assert that Done button is displayed") {
            depositDoneButton.assertDisplayed()
        }
        return this
    }

    fun assertGoToActivityButtonIsDisplayed(): DepositConfirmTestScreen {
        step("Assert that Go to Activity button is displayed") {
            depositGoToActivityButton.assertDisplayed()
        }
        return this
    }

    fun done(): DepositTestScreen {
        return step("Press Done button") { confirm { DepositTestScreen } }
    }

    fun assertDepositStatus(depositStatusMessage: String): DepositConfirmTestScreen {
        step("Assert deposit status: $depositStatusMessage") {
            depositStatus.assertStatusMessage(depositStatusMessage)
        }
        return this
    }

    fun waitWebViewAndTapBackButton(): DepositConfirmTestScreen {
        step("Wait for Browser page is visible and click back button") {
            webViewHelper.waitForBrowserPageAndClickBack()
        }
        return this
    }
}
