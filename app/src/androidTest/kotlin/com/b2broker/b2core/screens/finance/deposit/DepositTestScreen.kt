package com.b2broker.b2core.screens.finance.deposit

import com.atiurin.ultron.allure.step.step
import com.b2broker.b2core.data.CurrencyEnum
import com.b2broker.b2core.data.DepositInfoTestData
import com.b2broker.b2core.funds.deposit.DepositScreenTestTags.SELECT_ACCOUNT_TO
import com.b2broker.b2core.funds.deposit.DepositScreenTestTags.SELECT_CURRENCY
import com.b2broker.b2core.funds.deposit.DepositScreenTestTags.SELECT_PAYMENT_METHOD
import com.b2broker.b2core.screens.base.BaseTransactionTestScreen
import com.b2broker.b2core.testdevice.utils.assertTextContains

object DepositTestScreen : BaseTransactionTestScreen(
    accountSelectTestTag = SELECT_ACCOUNT_TO,
    currencySelectTestTag = SELECT_CURRENCY,
    transactionMethodSelectTestTag = SELECT_PAYMENT_METHOD,
) {
    fun searchAndSelectToAccount(toAccount: CurrencyEnum): DepositTestScreen {
        step("Search and select To Account equal to $toAccount") { searchAndSelectAccount(toAccount) }
        return this
    }

    override fun searchAndSelectCurrency(currency: CurrencyEnum) {
        step("Search and select Currency equal to $currency") {
            super.searchAndSelectCurrency(currency)
            waitForCurrencyUpdated(currency.alphabeticCode)
        }
    }

    fun assertToAccount(toAccount: CurrencyEnum): DepositTestScreen {
        assertAccount(toAccount)
        return this
    }

    fun confirmDeposit(): DepositDetailsTestScreen {
        return step("Confirm Deposit") { confirm { DepositDetailsTestScreen } }
    }

    fun selectDepositDetails(depositInfo: DepositInfoTestData): DepositTestScreen {
        step(
            """
        Select deposit details:
        - Account: ${depositInfo.fromAccount}
        - Currency: ${depositInfo.currency}
        - Method: ${depositInfo.method}
            """.trimIndent()
        ) {
            searchAndSelectCurrency(depositInfo.currency)
            searchAndSelectPaymentMethod(depositInfo.method)
        }
        return this
    }

    fun assertDepositDetails(depositInfo: DepositInfoTestData): DepositTestScreen {
        step(
            """
        Assert deposit details:
        - Account: ${depositInfo.fromAccount}
        - Currency: ${depositInfo.currency}
        - Method: ${depositInfo.method}
            """.trimIndent()
        ) {
            assertAllFields(depositInfo.fromAccount, depositInfo.currency, depositInfo.method)
        }
        return this
    }

    private fun waitForCurrencyUpdated(currency: String) {
        step("Wait for currency select to be updated") {
            currencySelect.assertTextContains(currency)
        }
    }

    override fun assertDisplayed(): DepositTestScreen {
        step("Assert that Deposit screen is displayed") { super.assertDisplayed() }
        return this
    }
}
