package com.b2broker.b2core.data.screenState

object TransferTestScreenStateBuilder {
    fun createTransferScreenState(
        withFromAccount: String = "Select destination account",
        withToAccount: String = "Select destination account",
        withAmount: String = "Amount (USD)",
    ): String {
        return """
Node
 |-Node
    |-Node
       |-Node
       |  |-Node
       |  | Text = '[From]'
       |  |-Node
       |  | Text = '[$withFromAccount]'
       |  |-Node
       |  | Text = '[To]'
       |  |-Node
       |  | Text = '[$withToAccount]'
       |  |-Node
       |  | EditableText = ''
       |  | IsEditable = 'false'
       |  | Text = '[$withAmount]'
       |  | [Disabled]
       |  |-Node
       |  |  |-Node
       |  |  | Text = '[25%]'
       |  |  | [Disabled]
       |  |  |-Node
       |  |  | Text = '[50%]'
       |  |  | [Disabled]
       |  |  |-Node
       |  |  | Text = '[75%]'
       |  |  | [Disabled]
       |  |  |-Node
       |  |    Text = '[100%]'
       |  |    [Disabled]
       |  |-Node
       |  |-Node
       |  |-Node
       |    Text = '[Transfer can only be carried out between accounts of the same currency. Only those balances where this is possible will be shown here]'
       |-Node
       |  |-Node
       |  |-Node
       |  | Text = '[Transfer]'
       |  |-Node
       |-Node
         Role = 'Button'
         Text = '[Continue]'
         [Disabled]
        """.trimIndent()
    }
}
