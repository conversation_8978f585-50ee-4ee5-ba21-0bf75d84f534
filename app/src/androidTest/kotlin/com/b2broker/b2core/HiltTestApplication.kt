package com.b2broker.b2core

import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import cafe.adriel.voyager.core.registry.ScreenRegistry
import com.b2broker.b2core.navigation.RouteApplier
import dagger.hilt.android.EarlyEntryPoints

open class HiltTestApplication : BaseApp() {

    override val workManagerConfiguration: Configuration by lazy {
        Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
    }

    override lateinit var workerFactory: HiltWorkerFactory

    override lateinit var routeAppliers: Set<RouteApplier>

    override fun onCreate() {
        super.onCreate()

        val entryPoint = EarlyEntryPoints
            .get(this, TestApplicationEntryPoint::class.java)

        workerFactory = entryPoint.provideHiltWorkerFactory()
        routeAppliers = entryPoint.provideRouteAppliers()

        ScreenRegistry {
            for (applyRoute in routeAppliers) {
                applyRoute()
            }
        }
    }
}
