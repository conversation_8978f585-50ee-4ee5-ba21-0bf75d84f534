{"log": {"creator": {"name": "<PERSON><PERSON>", "version": "4.0.0"}, "entries": [{"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [], "headersSize": 0, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 0, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/signin/wizard"}, "response": {"bodySize": 190, "content": {"mimeType": "application/json", "size": 190, "text": "{\"code\":200,\"data\":{\"recaptcha\":{\"enabled\":false},\"is_qr_available\":true,\"is_password_recovery_available\":true},\"done\":false,\"uuid\":\"5a34337b-bd35-4f7f-a870-ff95e503fb83\",\"workflow\":\"login\"}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:16 GMT"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 217, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 407}, "startedDateTime": "2024-04-15T11:27:16.169Z", "time": 381, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 381}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 595, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 595, "url": "https://api.testmobile-stable.b2broker.tech/api/v2/my/permissions"}, "response": {"bodySize": 204, "content": {"mimeType": "application/json", "size": 204, "text": "[{\"name\":\"verification\",\"isEnabled\":true},{\"name\":\"converter\",\"isEnabled\":true},{\"name\":\"deposits\",\"isEnabled\":true},{\"name\":\"withdrawals\",\"isEnabled\":true},{\"name\":\"internal_transfers\",\"isEnabled\":true}]"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 29 Jul 2024 04:20:18 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "595"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 473}, "startedDateTime": "2024-07-29T13:20:13.408Z", "time": 803, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 803}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 595, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 595, "url": "https://api.testmobile-stable.b2broker.tech/api/v2/my/password/changes"}, "response": {"bodySize": 441, "content": {"mimeType": "application/json", "size": 441, "text": "{\"total\":6,\"data\":[{\"id\":2073,\"type\":\"changed\",\"createTime\":\"2024-07-25T10:27:00+00:00\"},{\"id\":2072,\"type\":\"changed\",\"createTime\":\"2024-07-25T10:26:13+00:00\"},{\"id\":2071,\"type\":\"restored\",\"createTime\":\"2024-07-25T10:26:06+00:00\"},{\"id\":2001,\"type\":\"changed\",\"createTime\":\"2024-05-29T05:01:50+00:00\"},{\"id\":2000,\"type\":\"changed\",\"createTime\":\"2024-05-29T04:59:50+00:00\"},{\"id\":1999,\"type\":\"changed\",\"createTime\":\"2024-05-29T04:44:09+00:00\"}]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 29 Jul 2024 04:20:23 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "589"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 710}, "startedDateTime": "2024-07-29T13:20:19.172Z", "time": 510, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 510}}, {"cache": {}, "request": {"bodySize": 235, "cookies": [], "headers": [], "headersSize": 0, "httpVersion": "h2", "method": "POST", "postData": {"mimeType": "application/json; charset=utf-8", "text": "{\"uuid\":\"5a34337b-bd35-4f7f-a870-ff95e503fb83\",\"email\":\"<EMAIL>\",\"password\":\"Test123@\",\"device_fingerprint\":\"eyJkZXZpY2VfdHlwZSI6Im1vYmlsZSIsInBsYXRmb3JtIjoiQW5kcm9pZCIsIndlYl90aW1lem9uZSI6IkFzaWEvVG9reW8ifQ==\"}"}, "queryString": [], "totalSize": 235, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/signin"}, "response": {"bodySize": 1479, "content": {"mimeType": "application/json", "size": 1479, "text": "{\"code\":202,\"data\":{\"accessToken\":{\"token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************.LfjwIgPhXse81Ot7MykgeuOl2FINiiFmfjedgWH3Q7Aaea0xXkrxc5VeH_mUJ4IbijVzhY9SlGfgP18uiKlKzlxZF5_Rksw7j168RWcyHsB5vKGCGQ_IAwXusXnWPC4wFa0nCqre1HG9Nh0q-MynUZr4wenUTjUd-jKkNCsTOYexgEkWTf0DizZNMYt2q_D0Kw8wx-KpOrwt-Tbima2_x6PJodbT0MYVyI11IAEIZZg3QB7lDm-docoZx-g02bXRqE3PJdyP5EUlCAD34SXbaP8I1eR_HkFauVyQqnmTMWsGWLyfKfVzDNQrwKMRwsIUXv7XZmjQuHKNAibKVlqhEw\",\"createdAt\":\"2024-04-15T02:27:17+00:00\",\"expiresAt\":\"2024-04-15T02:47:17+00:00\"},\"refreshToken\":{\"token\":\"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"createdAt\":\"2024-04-15T02:27:17+00:00\",\"expiresAt\":\"2024-05-15T02:27:17+00:00\"},\"tfaProviders\":[]},\"done\":true,\"uuid\":\"5a34337b-bd35-4f7f-a870-ff95e503fb83\",\"workflow\":\"Terminate\"}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:17 GMT"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 207, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 1686}, "startedDateTime": "2024-04-15T11:27:16.556Z", "time": 538, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 538}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 595, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "limit", "value": "5"}, {"name": "offset", "value": "0"}, {"name": "filter[type]", "value": "unknown"}, {"name": "sort_order", "value": "desc"}, {"name": "sort_by", "value": "createTime"}], "totalSize": 595, "url": "https://api.testmobile-stable.b2broker.tech/api/v2/my/transactions?limit=5&offset=0&filter[type]=unknown&sort_order=desc&sort_by=createTime"}, "response": {"bodySize": 21, "content": {"mimeType": "application/json", "size": 21, "text": "{\"total\":0,\"data\":[]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 29 Jul 2024 04:20:18 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "593"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 290}, "startedDateTime": "2024-07-29T13:20:14.219Z", "time": 490, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 490}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/platforms"}, "response": {"bodySize": 462, "content": {"mimeType": "application/json", "size": 462, "text": "[{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},{\"id\":2,\"name\":\"\\u041c\\u04224\",\"caption\":\"\\u041c\\u04224\",\"type\":\"MetaTrader4\"},{\"id\":3,\"name\":\"MT5\",\"caption\":\"MT5\",\"type\":\"MetaTrader5\"},{\"id\":6,\"name\":\"OneZero\",\"caption\":\"OneZero\",\"type\":\"OneZero\"},{\"id\":7,\"name\":\"test\",\"caption\":\"test\",\"type\":\"MetaTrader4\"},{\"id\":9,\"name\":\"B2Trader\",\"caption\":\"B2Trader\",\"type\":\"B2Trader\"},{\"id\":11,\"name\":\"BBP\",\"caption\":\"BBP\",\"type\":\"B2TraderBrokeragePlatform\"}]"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:17 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "599"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 731}, "startedDateTime": "2024-04-15T11:27:17.162Z", "time": 323, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 323}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/accounts?limit=1000&offset=0"}, "response": {"bodySize": 23295, "content": {"mimeType": "application/json", "size": 23295, "text": "{\"total\":14,\"data\":[{\"accountId\":19039,\"accountNumber\":\"14805\",\"archive\":false,\"caption\":\"USD\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":3,\"name\":\"Fiat\",\"priority\":2,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":202,\"statement\":{\"availableBalance\":\"834.790000000000000000\",\"currentBalance\":\"834.790000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:04+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-01-16T08:38:30+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"productId\":2,\"rights\":255,\"leverage\":0,\"productCurrency\":{\"id\":1,\"caption\":\"USD\",\"product\":{\"id\":2,\"type\":\"personal\",\"name\":\"ewallet_usd\",\"group\":{\"id\":3,\"caption\":\"Fiat\",\"description\":\"Fiat\",\"type\":\"default\",\"priority\":2,\"createTime\":\"2018-12-25T17:28:55+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":1,\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"priority\":202,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19040,\"accountNumber\":\"14806\",\"archive\":false,\"caption\":\"BTC\",\"currency\":{\"alphabeticCode\":\"BTC\",\"name\":\"Bitcoin\",\"numericCode\":1000,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":2,\"name\":\"Coins\",\"priority\":4,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":101,\"statement\":{\"availableBalance\":\"1.000000000000000000\",\"currentBalance\":\"1.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:04+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-01-16T08:38:30+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"productId\":4,\"rights\":253,\"leverage\":0,\"productCurrency\":{\"id\":3,\"caption\":\"BTC\",\"product\":{\"id\":4,\"type\":\"personal\",\"name\":\"ewallet_btc\",\"group\":{\"id\":2,\"caption\":\"Coins\",\"description\":\"Wallet\",\"type\":\"default\",\"priority\":4,\"createTime\":\"2018-02-20T16:45:34+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"BTC\",\"name\":\"Bitcoin\",\"numericCode\":1000,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":2,\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"priority\":101,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19041,\"accountNumber\":\"14807\",\"archive\":false,\"caption\":\"14807\",\"currency\":{\"alphabeticCode\":\"TUSD\",\"name\":\"TrueUSD\",\"numericCode\":2022,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":3,\"name\":\"Fiat\",\"priority\":2,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":202,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:06+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-01-16T08:38:30+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"visible\",\"withdrawal\"],\"productId\":2,\"rights\":157,\"leverage\":0,\"productCurrency\":{\"id\":30,\"caption\":\"\",\"product\":{\"id\":2,\"type\":\"personal\",\"name\":\"ewallet_usd\",\"group\":{\"id\":3,\"caption\":\"Fiat\",\"description\":\"Fiat\",\"type\":\"default\",\"priority\":2,\"createTime\":\"2018-12-25T17:28:55+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"TUSD\",\"name\":\"TrueUSD\",\"numericCode\":2022,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":1,\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"visible\",\"withdrawal\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":null}},{\"accountId\":19042,\"accountNumber\":\"14808\",\"archive\":false,\"caption\":\"14808\",\"currency\":{\"alphabeticCode\":\"USDC\",\"name\":\"USD Coin\",\"numericCode\":2024,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":3,\"name\":\"Fiat\",\"priority\":2,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":202,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:06+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-01-16T08:38:30+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"productId\":2,\"rights\":253,\"leverage\":0,\"productCurrency\":{\"id\":31,\"caption\":\"\",\"product\":{\"id\":2,\"type\":\"personal\",\"name\":\"ewallet_usd\",\"group\":{\"id\":3,\"caption\":\"Fiat\",\"description\":\"Fiat\",\"type\":\"default\",\"priority\":2,\"createTime\":\"2018-12-25T17:28:55+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USDC\",\"name\":\"USD Coin\",\"numericCode\":2024,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":1,\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":null}},{\"accountId\":19043,\"accountNumber\":\"**********\",\"archive\":false,\"caption\":\"\\u041c\\u04224_USD\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":7,\"name\":\"Payment Account\",\"priority\":7,\"type\":\"payment\"},\"platform\":{\"id\":2,\"name\":\"\\u041c\\u04224\",\"caption\":\"\\u041c\\u04224\",\"type\":\"MetaTrader4\"},\"priority\":220,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:06+00:00\"},\"type\":\"trade\",\"createTime\":\"2024-01-16T08:39:30+00:00\",\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"productId\":34,\"rights\":115,\"leverage\":1,\"productCurrency\":{\"id\":17,\"caption\":\"\\u041c\\u04224_USD\",\"product\":{\"id\":34,\"type\":\"trade\",\"name\":\"\\u041c\\u04224_USD\",\"group\":{\"id\":7,\"caption\":\"Payment Account\",\"description\":\"Payment Account\",\"type\":\"payment\",\"priority\":7,\"createTime\":\"2021-08-12T15:59:23+00:00\",\"updateTime\":\"2021-08-12T15:59:23+00:00\"},\"platform\":{\"id\":2,\"caption\":\"\\u041c\\u04224\",\"shortCaption\":\"\\u041c\\u04224\",\"name\":\"\\u041c\\u04224\",\"class\":\"MetaTrader4\",\"isDemo\":true}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[\"0.00\"],\"leverages\":[\"1\",\"2\",\"3\"]},\"platformGroups\":[\"GRPUSD5\"],\"isHedgingEnabled\":true,\"maxAccounts\":4,\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"priority\":220,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19044,\"accountNumber\":\"********\",\"archive\":false,\"caption\":\"\\u041c\\u04225_USD\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":6,\"name\":\"Trade\",\"priority\":6,\"type\":\"default\"},\"platform\":{\"id\":3,\"name\":\"MT5\",\"caption\":\"MT5\",\"type\":\"MetaTrader5\"},\"priority\":221,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"trade\",\"createTime\":\"2024-01-16T08:39:30+00:00\",\"permissions\":[\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"productId\":35,\"rights\":249,\"leverage\":56,\"productCurrency\":{\"id\":18,\"caption\":\"\\u041c\\u04225_USD\",\"product\":{\"id\":35,\"type\":\"trade\",\"name\":\"\\u041c\\u04225_Live\",\"group\":{\"id\":6,\"caption\":\"Trade\",\"description\":\"Trade\",\"type\":\"default\",\"priority\":6,\"createTime\":\"2021-03-16T15:24:42+00:00\",\"updateTime\":\"2023-09-08T15:20:06+00:00\"},\"platform\":{\"id\":3,\"caption\":\"MT5\",\"shortCaption\":\"\",\"name\":\"MT5\",\"class\":\"MetaTrader5\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[\"0.00\"],\"leverages\":[\"56\",\"777\",\"46\"]},\"platformGroups\":[\"real\\\\Group-Standard-USD-B\"],\"isHedgingEnabled\":false,\"maxAccounts\":7,\"permissions\":[\"enabled\",\"exchange\",\"transferDeposit\",\"transferWithdrawal\",\"visible\",\"withdrawal\"],\"priority\":221,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19045,\"accountNumber\":\"********\",\"archive\":false,\"caption\":\"MT5_USD_Demo\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":6,\"name\":\"Trade\",\"priority\":6,\"type\":\"default\"},\"platform\":{\"id\":3,\"name\":\"MT5\",\"caption\":\"MT5\",\"type\":\"MetaTrader5\"},\"priority\":223,\"statement\":{\"availableBalance\":\"40000.000000000000000000\",\"currentBalance\":\"40000.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"40000.000000000000000000\",\"freeMargin\":\"40000.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"demo\",\"createTime\":\"2024-01-16T08:39:31+00:00\",\"permissions\":[\"enabled\",\"trade\",\"visible\"],\"productId\":37,\"rights\":19,\"leverage\":1,\"productCurrency\":{\"id\":20,\"caption\":\"MT5_USD_Demo\",\"product\":{\"id\":37,\"type\":\"demo\",\"name\":\"MT5_Demo\",\"group\":{\"id\":6,\"caption\":\"Trade\",\"description\":\"Trade\",\"type\":\"default\",\"priority\":6,\"createTime\":\"2021-03-16T15:24:42+00:00\",\"updateTime\":\"2023-09-08T15:20:06+00:00\"},\"platform\":{\"id\":3,\"caption\":\"MT5\",\"shortCaption\":\"\",\"name\":\"MT5\",\"class\":\"MetaTrader5\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[\"40000.00\"],\"leverages\":[\"1\"]},\"platformGroups\":[\"real\\\\Group-Standard-USD-B\"],\"isHedgingEnabled\":false,\"maxAccounts\":4,\"permissions\":[\"enabled\",\"trade\",\"visible\"],\"priority\":223,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19070,\"accountNumber\":\"3018953\",\"archive\":false,\"caption\":\"3018953\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":6,\"name\":\"Trade\",\"priority\":6,\"type\":\"default\"},\"platform\":{\"id\":8,\"name\":\"CTrader\",\"caption\":\"CTrader\",\"type\":\"cTrader\"},\"priority\":251,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"trade\",\"createTime\":\"2024-02-27T08:26:03+00:00\",\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"productId\":65,\"rights\":115,\"leverage\":1,\"productCurrency\":{\"id\":51,\"caption\":\"\",\"product\":{\"id\":65,\"type\":\"trade\",\"name\":\"CTrader_USD\",\"group\":{\"id\":6,\"caption\":\"Trade\",\"description\":\"Trade\",\"type\":\"default\",\"priority\":6,\"createTime\":\"2021-03-16T15:24:42+00:00\",\"updateTime\":\"2023-09-08T15:20:06+00:00\"},\"platform\":{\"id\":8,\"caption\":\"CTrader\",\"shortCaption\":\"\",\"name\":\"CTrader\",\"class\":\"cTrader\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[\"0.00\"],\"leverages\":[\"1\"]},\"platformGroups\":[\"B2Broker\"],\"isHedgingEnabled\":true,\"maxAccounts\":3,\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"https:\\/\\/www.b2broker.com\"}},{\"accountId\":19071,\"accountNumber\":\"3018954\",\"archive\":false,\"caption\":\"3018954\",\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":6,\"name\":\"Trade\",\"priority\":6,\"type\":\"default\"},\"platform\":{\"id\":8,\"name\":\"CTrader\",\"caption\":\"CTrader\",\"type\":\"cTrader\"},\"priority\":252,\"statement\":{\"availableBalance\":\"50000.000000000000000000\",\"currentBalance\":\"50000.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"50000.000000000000000000\",\"freeMargin\":\"50000.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"demo\",\"createTime\":\"2024-02-27T08:26:03+00:00\",\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"productId\":66,\"rights\":115,\"leverage\":1,\"productCurrency\":{\"id\":52,\"caption\":\"\",\"product\":{\"id\":66,\"type\":\"demo\",\"name\":\"CTrader_USD_Demo\",\"group\":{\"id\":6,\"caption\":\"Trade\",\"description\":\"Trade\",\"type\":\"default\",\"priority\":6,\"createTime\":\"2021-03-16T15:24:42+00:00\",\"updateTime\":\"2023-09-08T15:20:06+00:00\"},\"platform\":{\"id\":8,\"caption\":\"CTrader\",\"shortCaption\":\"\",\"name\":\"CTrader\",\"class\":\"cTrader\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USD\",\"name\":\"USD\",\"numericCode\":840,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[\"50000.00\"],\"leverages\":[\"1\"]},\"platformGroups\":[\"B2Broker\"],\"isHedgingEnabled\":true,\"maxAccounts\":2,\"permissions\":[\"enabled\",\"trade\",\"transferDeposit\",\"transferWithdrawal\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"https:\\/\\/www.b2broker.com\"}},{\"accountId\":19111,\"accountNumber\":\"14831\",\"archive\":false,\"caption\":\"JPY\",\"currency\":{\"alphabeticCode\":\"JPY\",\"name\":\"Japanese yen\",\"numericCode\":392,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":3,\"name\":\"Fiat\",\"priority\":2,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":242,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-04-03T04:49:33+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"trade\",\"transferDeposit\",\"visible\",\"withdrawal\"],\"productId\":56,\"rights\":191,\"leverage\":0,\"productCurrency\":{\"id\":42,\"caption\":\"JPY\",\"product\":{\"id\":56,\"type\":\"personal\",\"name\":\"JPY\",\"group\":{\"id\":3,\"caption\":\"Fiat\",\"description\":\"Fiat\",\"type\":\"default\",\"priority\":2,\"createTime\":\"2018-12-25T17:28:55+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"JPY\",\"name\":\"Japanese yen\",\"numericCode\":392,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":1,\"permissions\":[\"deposit\",\"enabled\",\"exchange\",\"trade\",\"transferDeposit\",\"visible\",\"withdrawal\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19112,\"accountNumber\":\"14832\",\"archive\":false,\"caption\":\"CNY\",\"currency\":{\"alphabeticCode\":\"CNY\",\"name\":\"Chinese yuan\",\"numericCode\":156,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":3,\"name\":\"Fiat\",\"priority\":2,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":246,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-04-03T04:49:38+00:00\",\"permissions\":[\"enabled\",\"visible\"],\"productId\":60,\"rights\":17,\"leverage\":0,\"productCurrency\":{\"id\":46,\"caption\":\"CNY\",\"product\":{\"id\":60,\"type\":\"personal\",\"name\":\"CNY\",\"group\":{\"id\":3,\"caption\":\"Fiat\",\"description\":\"Fiat\",\"type\":\"default\",\"priority\":2,\"createTime\":\"2018-12-25T17:28:55+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"CNY\",\"name\":\"Chinese yuan\",\"numericCode\":156,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":1,\"permissions\":[\"enabled\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19113,\"accountNumber\":\"14833\",\"archive\":false,\"caption\":\"14833\",\"currency\":{\"alphabeticCode\":\"USDT\",\"name\":\"Tether\",\"numericCode\":2005,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":2,\"name\":\"Coins\",\"priority\":4,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":255,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-04-03T04:49:46+00:00\",\"permissions\":[\"deposit\",\"enabled\",\"visible\"],\"productId\":71,\"rights\":21,\"leverage\":0,\"productCurrency\":{\"id\":62,\"caption\":\"\",\"product\":{\"id\":71,\"type\":\"personal\",\"name\":\"USDT\",\"group\":{\"id\":2,\"caption\":\"Coins\",\"description\":\"Wallet\",\"type\":\"default\",\"priority\":4,\"createTime\":\"2018-02-20T16:45:34+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"USDT\",\"name\":\"Tether\",\"numericCode\":2005,\"minorUnit\":8,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":3,\"permissions\":[\"deposit\",\"enabled\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19114,\"accountNumber\":\"14834\",\"archive\":false,\"caption\":\"AAVE\",\"currency\":{\"alphabeticCode\":\"AAVE\",\"name\":\"Aave\",\"numericCode\":2964,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":5,\"name\":\"Tokens\",\"priority\":5,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":250,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-04-03T04:50:00+00:00\",\"permissions\":[\"enabled\",\"visible\"],\"productId\":64,\"rights\":17,\"leverage\":0,\"productCurrency\":{\"id\":50,\"caption\":\"AAVE\",\"product\":{\"id\":64,\"type\":\"personal\",\"name\":\"Aave\",\"group\":{\"id\":5,\"caption\":\"Tokens\",\"description\":\"Tokens\",\"type\":\"default\",\"priority\":5,\"createTime\":\"2018-12-25T17:29:27+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"AAVE\",\"name\":\"Aave\",\"numericCode\":2964,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":5,\"permissions\":[\"enabled\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"\"}},{\"accountId\":19115,\"accountNumber\":\"14835\",\"archive\":false,\"caption\":\"AAVE\",\"currency\":{\"alphabeticCode\":\"AAVE\",\"name\":\"Aave\",\"numericCode\":2964,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"favourite\":true,\"group\":{\"id\":5,\"name\":\"Tokens\",\"priority\":5,\"type\":\"default\"},\"platform\":{\"id\":1,\"name\":\"ew\",\"caption\":\"eWallet\",\"type\":\"personal\"},\"priority\":250,\"statement\":{\"availableBalance\":\"0.000000000000000000\",\"currentBalance\":\"0.000000000000000000\",\"credit\":\"0.000000000000000000\",\"equity\":\"0.000000000000000000\",\"freeMargin\":\"0.000000000000000000\",\"hold\":\"0.000000000000000000\",\"margin\":\"0.000000000000000000\",\"marginLevel\":\"0.000000000000000000\",\"pnl\":\"0.000000000000000000\",\"updateTime\":\"2024-04-15T02:26:07+00:00\"},\"type\":\"personal\",\"createTime\":\"2024-04-03T07:33:52+00:00\",\"permissions\":[\"enabled\",\"visible\"],\"productId\":64,\"rights\":17,\"leverage\":0,\"productCurrency\":{\"id\":50,\"caption\":\"AAVE\",\"product\":{\"id\":64,\"type\":\"personal\",\"name\":\"Aave\",\"group\":{\"id\":5,\"caption\":\"Tokens\",\"description\":\"Tokens\",\"type\":\"default\",\"priority\":5,\"createTime\":\"2018-12-25T17:29:27+00:00\",\"updateTime\":\"2021-03-11T08:50:56+00:00\"},\"platform\":{\"id\":1,\"caption\":\"eWallet\",\"shortCaption\":\"\",\"name\":\"ew\",\"class\":\"personal\",\"isDemo\":false}},\"currency\":{\"alphabeticCode\":\"AAVE\",\"name\":\"Aave\",\"numericCode\":2964,\"minorUnit\":2,\"hasDestinationTagOrMemo\":false},\"factory\":1,\"variants\":{\"startAmounts\":[],\"leverages\":[\"1\"]},\"platformGroups\":[\"eWallet\"],\"isHedgingEnabled\":false,\"maxAccounts\":5,\"permissions\":[\"enabled\",\"visible\"],\"priority\":0,\"linkInfo\":\"\",\"agreementLink\":\"\"}}]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:17 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "596"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 23564}, "startedDateTime": "2024-04-15T11:27:17.162Z", "time": 660, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 660}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "sort_order", "value": "asc"}, {"name": "sort_by", "value": "priority"}, {"name": "filter[type]", "value": "mobile"}, {"name": "filter[enabled]", "value": "1"}], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/banners?sort_order=asc&sort_by=priority&filter[type]=mobile&filter[enabled]=1"}, "response": {"bodySize": 3890, "content": {"mimeType": "application/json", "size": 3890, "text": "{\"total\":4,\"data\":[{\"id\":11,\"backgroundLinkLight\":null,\"backgroundLinkDark\":null,\"buttonLight\":\"\",\"buttonDark\":null,\"buttonLink\":\"https:\\/\\/www.apple.com\",\"captionLight\":\"Mobile 2\",\"captionDark\":\"Mobile 2\",\"data\":{\"vertical_align\":\"bottom\",\"horizontal_align\":\"left\",\"padding\":\"25.0\",\"image_url_light\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_b2core.png\",\"image_url_dark\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_b2core.png\",\"preview_enabled\":false},\"enabled\":true,\"mobileText\":\"qwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe aqwqw qwe new qwe qwe qwe qwe qwe qwe a\",\"mobileTitle\":\"Test title\",\"mobileSubTitle\":\"Test subtitlewwwwww\",\"mobileButtonTitle\":\"Button test title\",\"priority\":1,\"textLight\":\"\",\"textDark\":null,\"type\":\"mobile\",\"url\":\"\\/banner-mobile\",\"resources\":[],\"createdAt\":\"2022-11-15T12:16:26+00:00\",\"createdBy\":\"<EMAIL>\"},{\"id\":16,\"backgroundLinkLight\":null,\"backgroundLinkDark\":null,\"buttonLight\":\"\",\"buttonDark\":null,\"buttonLink\":\"https:\\/\\/testmobile-my.b2broker.tech\",\"captionLight\":\"banner test\",\"captionDark\":\"banner test\",\"data\":{\"vertical_align\":\"middle\",\"horizontal_align\":\"left\",\"padding\":\"15.0\",\"image_url_light\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_b2core.png\",\"image_url_dark\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_b2core.png\",\"preview_enabled\":false},\"enabled\":true,\"mobileText\":\"test\",\"mobileTitle\":\"My banner title test long long tesxt  title test long long tesxt\",\"mobileSubTitle\":\"My banner subtitle test long long text subtitle test long long text\",\"mobileButtonTitle\":\"test\",\"priority\":1,\"textLight\":\"\",\"textDark\":null,\"type\":\"mobile\",\"url\":\"\\/dashboard-mobile\",\"resources\":[],\"createdAt\":\"2023-01-13T09:33:52+00:00\",\"createdBy\":\"<EMAIL>\"},{\"id\":13,\"backgroundLinkLight\":null,\"backgroundLinkDark\":null,\"buttonLight\":\"\",\"buttonDark\":null,\"buttonLink\":\"https:\\/\\/testmobile-my.b2broker.tech\",\"captionLight\":\"mobile test\",\"captionDark\":\"mobile test\",\"data\":{\"vertical_align\":\"middle\",\"horizontal_align\":\"left\",\"padding\":\"24.0\",\"image_url_light\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_turnkey.png\",\"image_url_dark\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_turnkey.png\",\"preview_enabled\":true},\"enabled\":true,\"mobileText\":\"test test test test\",\"mobileTitle\":\"B2Broker 2022\",\"mobileSubTitle\":\"Subtitle Banner 22\",\"mobileButtonTitle\":\"test\",\"priority\":2,\"textLight\":\"\",\"textDark\":null,\"type\":\"mobile\",\"url\":\"\\/dashboard-mobile\",\"resources\":[],\"createdAt\":\"2022-11-17T11:02:04+00:00\",\"createdBy\":\"<EMAIL>\"},{\"id\":10,\"backgroundLinkLight\":null,\"backgroundLinkDark\":null,\"buttonLight\":\"\",\"buttonDark\":null,\"buttonLink\":\"https:\\/\\/www.google.com\",\"captionLight\":\"Mobile Banner\",\"captionDark\":\"Mobile Banner\",\"data\":{\"vertical_align\":\"bottom\",\"horizontal_align\":\"left\",\"padding\":\"20.0\",\"image_url_light\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_turnkey.png\",\"image_url_dark\":\"https:\\/\\/clients-data-flexdns.s3.eu-west-2.amazonaws.com\\/banner_turnkey.png\",\"preview_enabled\":false},\"enabled\":true,\"mobileText\":\"Preview text \",\"mobileTitle\":\"Title\",\"mobileSubTitle\":\"Subtitle\",\"mobileButtonTitle\":\"Button title\",\"priority\":3,\"textLight\":\"\",\"textDark\":null,\"type\":\"mobile\",\"url\":\"\\/dashboard-mobile\",\"resources\":[],\"createdAt\":\"2022-11-14T14:36:30+00:00\",\"createdBy\":\"<EMAIL>\"}]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:17 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "598"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 4159}, "startedDateTime": "2024-04-15T11:27:17.163Z", "time": 422, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 422}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [], "headersSize": 0, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 0, "url": "https://api.testmobile-stable.b2broker.tech/api/v2/my/profile"}, "response": {"bodySize": 10325, "content": {"mimeType": "application/json", "size": 10325, "text": "{\"id\":1050,\"email\":\"<EMAIL>\",\"locale\":\"en_US\",\"status\":\"active\",\"nickname\":\"TylerNoMeta7095\",\"photo\":null,\"maskedEmail\":\"a***************@********.com\",\"clientUiConfig\":{\"showId\":true,\"allowChangeUserPic\":true,\"allowChangeNickname\":true,\"allowShowEmail\":true},\"info\":{\"firstName\":\"<PERSON>\",\"lastName\":\"No Meta\",\"middleName\":\"\",\"birthday\":\"1992-01-05T00:00:00+00:00\"},\"phone\":{\"id\":1301,\"phone\":\"+66842228822\",\"ext\":null,\"type\":null,\"confirm\":false,\"default\":false,\"maskedPhone\":\"+66 84 *** **22\"},\"phones\":[{\"id\":1301,\"phone\":\"+66842228822\",\"ext\":null,\"type\":null,\"confirm\":false,\"default\":false,\"maskedPhone\":\"+66 84 *** **22\"}],\"addresses\":[{\"type\":1,\"city\":\"\",\"state\":\"\",\"subdivision\":\"\",\"postalCode\":\"\",\"street\":\"\",\"streetNumber\":\"\",\"address\":\"\",\"options\":[]}],\"type\":{\"id\":1,\"default\":true,\"enabled\":true,\"name\":\"Individual\",\"group\":\"individual\",\"wizard\":false},\"country\":{\"countryId\":660,\"alpha2Code\":\"AI\",\"alpha3Code\":\"AIA\",\"countryName\":\"Anguilla\",\"callingCode\":\"126\",\"numericCode\":\"660\",\"enabled\":true},\"verificationLevel\":{\"id\":1,\"index\":0,\"name\":\"Level 0\",\"caption\":\"Level 0\",\"localizedCaption\":\"Level 0\",\"description\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"localizedDescription\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"mailDescription\":\"\",\"mobileDescription\":\"\",\"localizedMobileDescription\":\"\",\"nextLevel\":2,\"roleId\":1,\"wizard\":\"B2B\\\\TCA\\\\Verification\\\\Wizards\\\\DocumentsWizard\",\"default\":true,\"visible\":true,\"limits\":{\"total\":1,\"data\":[{\"id\":197,\"currencyCode\":840,\"dailyDeposit\":\"10.000000000000000000\",\"dailyWithdrawal\":\"-1.000000000000000000\",\"dailyBuy\":\"0.000000000000000000\",\"dailySell\":\"0.000000000000000000\",\"dailyInternalTransfer\":\"0.000000000000000000\",\"monthlyWithdrawal\":\"-1.000000000000000000\",\"monthlyBuy\":\"0.000000000000000000\",\"monthlySell\":\"0.000000000000000000\",\"autoWithdrawal\":\"0.000000000000000000\",\"transferMin\":\"10.000000000000000000\"}]},\"options\":{\"document_groups\":\"id_proof\"}},\"lastLoginTime\":\"2024-10-04T06:06:42+00:00\",\"createTime\":\"2024-08-15T07:21:11+00:00\",\"updateTime\":\"2024-10-04T06:06:42+00:00\",\"limits\":{\"maxDemoTradingAccounts\":null,\"maxLiveTradingAccounts\":null}}"}, "cookies": [], "headers": [{"name": "Server", "value": "openresty"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Vary", "value": "Accept-Encoding"}, {"value": "no-cache, no-store, private", "name": "Cache-Control"}, {"name": "Date", "value": "Fri, 04 Oct 2024 06:06:44 GMT"}, {"name": "X-RateLimit-Limit", "value": "600"}, {"name": "X-RateLimit-Remaining", "value": "587"}, {"name": "X-API-Version", "value": "2.1.0"}, {"value": "SAMEORIGIN", "name": "X-Frame-Options"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"value": "nosniff", "name": "X-Content-Type-Options"}, {"name": "X-Synthetic-Test", "value": "true"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}], "headersSize": 483, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 10808}, "startedDateTime": "2024-10-04T06:06:43.407Z", "time": "75724603", "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": "75724603"}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v1/verification/wizard/get"}, "response": {"bodySize": 134, "content": {"mimeType": "application/json", "size": 134, "text": "{\"status\":200,\"data\":{\"wizard\":\"B2B\\\\TCA\\\\Verification\\\\Wizards\\\\DocumentsWizard\"},\"modules\":[],\"meta\":{\"status\":200,\"behaviours\":[]}}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:19 GMT"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 195, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 329}, "startedDateTime": "2024-04-15T11:27:19.194Z", "time": 791, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 791}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "deep", "value": "1"}], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/menus?deep=1"}, "response": {"bodySize": 3717, "content": {"mimeType": "application/json", "size": 3717, "text": "[{\"id\":1,\"name\":\"general\",\"caption\":\"General\",\"new\":false,\"priority\":1,\"items\":[{\"id\":2,\"name\":\"dashboard\",\"caption\":\"Dashboard\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":3,\"name\":\"wallets\",\"caption\":\"Wallets\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":4,\"name\":\"b2trader\",\"caption\":\"B2Trader\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":5,\"name\":\"platforms\",\"caption\":\"Platforms\",\"new\":false,\"priority\":4,\"items\":[{\"id\":47,\"name\":\"oz_pxm\",\"caption\":\"OZ\\\\PXM\",\"new\":false,\"priority\":0,\"items\":[]},{\"id\":6,\"name\":\"mt4\",\"caption\":\"MT4\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":7,\"name\":\"mt5\",\"caption\":\"MT5\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":51,\"name\":\"ctrader\",\"caption\":\"cTrader\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":55,\"name\":\"bbp\",\"caption\":\"B2Trader Brokerage Platform\",\"new\":false,\"priority\":7,\"items\":[]}]},{\"id\":8,\"name\":\"funds\",\"caption\":\"Funds\",\"new\":false,\"priority\":5,\"items\":[{\"id\":9,\"name\":\"deposit\",\"caption\":\"Deposit\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":10,\"name\":\"transfer\",\"caption\":\"Transfer\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":11,\"name\":\"withdraw\",\"caption\":\"Withdraw\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":12,\"name\":\"internal_transfer\",\"caption\":\"Internal Transfer\",\"new\":false,\"priority\":4,\"items\":[]}]},{\"id\":13,\"name\":\"exchange\",\"caption\":\"Exchange\",\"new\":false,\"priority\":6,\"items\":[]},{\"id\":14,\"name\":\"history\",\"caption\":\"History\",\"new\":false,\"priority\":7,\"items\":[{\"id\":15,\"name\":\"transactions\",\"caption\":\"Transactions\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":16,\"name\":\"analytics\",\"caption\":\"Analytics\",\"new\":false,\"priority\":2,\"items\":[]}]},{\"id\":17,\"name\":\"bonuses\",\"caption\":\"Bonuses\",\"new\":false,\"priority\":8,\"items\":[]},{\"id\":21,\"name\":\"profile\",\"caption\":\"Profile\",\"new\":false,\"priority\":10,\"items\":[{\"id\":22,\"name\":\"settings\",\"caption\":\"Settings\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":23,\"name\":\"verification\",\"caption\":\"Verification\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":28,\"name\":\"finance\",\"caption\":\"Finance\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":24,\"name\":\"security\",\"caption\":\"Security\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":25,\"name\":\"api_key_management\",\"caption\":\"API Key Management\",\"new\":false,\"priority\":5,\"items\":[]}]},{\"id\":26,\"name\":\"helpdesk\",\"caption\":\"Helpdesk\",\"new\":false,\"priority\":11,\"items\":[]},{\"id\":29,\"name\":\"b2copy\",\"caption\":\"B2Copy\",\"new\":false,\"priority\":13,\"items\":[{\"id\":30,\"name\":\"copy_trading\",\"caption\":\"Copy Trading\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":31,\"name\":\"leaderboard\",\"caption\":\"Leaderboard\",\"new\":false,\"priority\":2,\"items\":[]}]},{\"id\":32,\"name\":\"new_ib_room\",\"caption\":\"New IB Room\",\"new\":false,\"priority\":14,\"items\":[{\"id\":33,\"name\":\"partner_board\",\"caption\":\"Dashboard\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":34,\"name\":\"promo\",\"caption\":\"Promo\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":35,\"name\":\"promo_links\",\"caption\":\"Promo links\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":36,\"name\":\"promo_banners\",\"caption\":\"Promo banners\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":37,\"name\":\"reports\",\"caption\":\"Reports\",\"new\":false,\"priority\":5,\"items\":[]},{\"id\":38,\"name\":\"reports_acquisitions\",\"caption\":\"Reports acquisitions\",\"new\":false,\"priority\":6,\"items\":[]},{\"id\":39,\"name\":\"reports_client_list\",\"caption\":\"Reports client-list\",\"new\":false,\"priority\":7,\"items\":[]},{\"id\":40,\"name\":\"reports_accounts\",\"caption\":\"Reports accounts\",\"new\":false,\"priority\":8,\"items\":[]},{\"id\":41,\"name\":\"reports_rewards\",\"caption\":\"Reports rewards\",\"new\":false,\"priority\":12,\"items\":[]},{\"id\":42,\"name\":\"reports_transactions\",\"caption\":\"Reports transactions\",\"new\":false,\"priority\":13,\"items\":[]}]},{\"id\":48,\"name\":\"new_pamm\",\"caption\":\"PAMM\",\"new\":false,\"priority\":17,\"items\":[]}]}]"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Thu, 18 Apr 2024 07:04:31 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "592"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 3986}, "startedDateTime": "2024-04-18T16:04:30.901Z", "time": 489, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 489}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "deep", "value": "1"}], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/menus?deep=1"}, "response": {"bodySize": 3717, "content": {"mimeType": "application/json", "size": 3717, "text": "[{\"id\":1,\"name\":\"general\",\"caption\":\"General\",\"new\":false,\"priority\":1,\"items\":[{\"id\":2,\"name\":\"dashboard\",\"caption\":\"Dashboard\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":3,\"name\":\"wallets\",\"caption\":\"Wallets\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":4,\"name\":\"b2trader\",\"caption\":\"B2Trader\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":5,\"name\":\"platforms\",\"caption\":\"Platforms\",\"new\":false,\"priority\":4,\"items\":[{\"id\":47,\"name\":\"oz_pxm\",\"caption\":\"OZ\\\\PXM\",\"new\":false,\"priority\":0,\"items\":[]},{\"id\":6,\"name\":\"mt4\",\"caption\":\"MT4\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":7,\"name\":\"mt5\",\"caption\":\"MT5\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":51,\"name\":\"ctrader\",\"caption\":\"cTrader\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":55,\"name\":\"bbp\",\"caption\":\"B2Trader Brokerage Platform\",\"new\":false,\"priority\":7,\"items\":[]}]},{\"id\":8,\"name\":\"funds\",\"caption\":\"Funds\",\"new\":false,\"priority\":5,\"items\":[{\"id\":9,\"name\":\"deposit\",\"caption\":\"Deposit\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":10,\"name\":\"transfer\",\"caption\":\"Transfer\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":11,\"name\":\"withdraw\",\"caption\":\"Withdraw\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":12,\"name\":\"internal_transfer\",\"caption\":\"Internal Transfer\",\"new\":false,\"priority\":4,\"items\":[]}]},{\"id\":13,\"name\":\"exchange\",\"caption\":\"Exchange\",\"new\":false,\"priority\":6,\"items\":[]},{\"id\":14,\"name\":\"history\",\"caption\":\"History\",\"new\":false,\"priority\":7,\"items\":[{\"id\":15,\"name\":\"transactions\",\"caption\":\"Transactions\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":16,\"name\":\"analytics\",\"caption\":\"Analytics\",\"new\":false,\"priority\":2,\"items\":[]}]},{\"id\":17,\"name\":\"bonuses\",\"caption\":\"Bonuses\",\"new\":false,\"priority\":8,\"items\":[]},{\"id\":21,\"name\":\"profile\",\"caption\":\"Profile\",\"new\":false,\"priority\":10,\"items\":[{\"id\":22,\"name\":\"settings\",\"caption\":\"Settings\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":23,\"name\":\"verification\",\"caption\":\"Verification\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":28,\"name\":\"finance\",\"caption\":\"Finance\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":24,\"name\":\"security\",\"caption\":\"Security\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":25,\"name\":\"api_key_management\",\"caption\":\"API Key Management\",\"new\":false,\"priority\":5,\"items\":[]}]},{\"id\":26,\"name\":\"helpdesk\",\"caption\":\"Helpdesk\",\"new\":false,\"priority\":11,\"items\":[]},{\"id\":29,\"name\":\"b2copy\",\"caption\":\"B2Copy\",\"new\":false,\"priority\":13,\"items\":[{\"id\":30,\"name\":\"copy_trading\",\"caption\":\"Copy Trading\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":31,\"name\":\"leaderboard\",\"caption\":\"Leaderboard\",\"new\":false,\"priority\":2,\"items\":[]}]},{\"id\":32,\"name\":\"new_ib_room\",\"caption\":\"New IB Room\",\"new\":false,\"priority\":14,\"items\":[{\"id\":33,\"name\":\"partner_board\",\"caption\":\"Dashboard\",\"new\":false,\"priority\":1,\"items\":[]},{\"id\":34,\"name\":\"promo\",\"caption\":\"Promo\",\"new\":false,\"priority\":2,\"items\":[]},{\"id\":35,\"name\":\"promo_links\",\"caption\":\"Promo links\",\"new\":false,\"priority\":3,\"items\":[]},{\"id\":36,\"name\":\"promo_banners\",\"caption\":\"Promo banners\",\"new\":false,\"priority\":4,\"items\":[]},{\"id\":37,\"name\":\"reports\",\"caption\":\"Reports\",\"new\":false,\"priority\":5,\"items\":[]},{\"id\":38,\"name\":\"reports_acquisitions\",\"caption\":\"Reports acquisitions\",\"new\":false,\"priority\":6,\"items\":[]},{\"id\":39,\"name\":\"reports_client_list\",\"caption\":\"Reports client-list\",\"new\":false,\"priority\":7,\"items\":[]},{\"id\":40,\"name\":\"reports_accounts\",\"caption\":\"Reports accounts\",\"new\":false,\"priority\":8,\"items\":[]},{\"id\":41,\"name\":\"reports_rewards\",\"caption\":\"Reports rewards\",\"new\":false,\"priority\":12,\"items\":[]},{\"id\":42,\"name\":\"reports_transactions\",\"caption\":\"Reports transactions\",\"new\":false,\"priority\":13,\"items\":[]}]},{\"id\":48,\"name\":\"new_pamm\",\"caption\":\"PAMM\",\"new\":false,\"priority\":17,\"items\":[]}]}]"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Thu, 18 Apr 2024 07:04:31 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "592"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 3986}, "startedDateTime": "2024-04-18T16:04:30.901Z", "time": 489, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 489}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/profile"}, "response": {"bodySize": 10158, "content": {"mimeType": "application/json", "size": 10158, "text": "{\"id\":779,\"email\":\"<EMAIL>\",\"locale\":\"en_US\",\"status\":\"active\",\"nickname\":null,\"photo\":\"f112b8e5b91b54b2cae39f67fe8877ad.jpeg\",\"maskedEmail\":\"v*****************@********.com\",\"clientUiConfig\":{\"showId\":true,\"allowChangeUserPic\":true,\"allowChangeNickname\":true,\"allowShowEmail\":true},\"info\":{\"firstName\":\"\",\"lastName\":\"\",\"middleName\":\"\",\"birthday\":null},\"phone\":{\"id\":826,\"phone\":\"+79501234567\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"****** ***-**-67\"},\"phones\":[{\"id\":826,\"phone\":\"+79501234567\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"****** ***-**-67\"},{\"id\":824,\"phone\":\"+817090407440\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"+81 70-9***-**40\"}],\"addresses\":[],\"type\":{\"id\":1,\"default\":true,\"enabled\":true,\"name\":\"Individual\",\"group\":\"individual\",\"wizard\":false},\"country\":null,\"verificationLevel\":{\"id\":1,\"index\":0,\"name\":\"Level 0\",\"description\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"localizedDescription\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"mailDescription\":\"\",\"mobileDescription\":\"\",\"localizedMobileDescription\":\"\",\"nextLevel\":2,\"roleId\":1,\"wizard\":\"B2B\\\\TCA\\\\Verification\\\\Wizards\\\\DocumentsWizard\",\"default\":false,\"visible\":true,\"sustainable\":false,\"limits\":{\"total\":1,\"data\":[{\"id\":109,\"currencyCode\":840,\"dailyDeposit\":\"-1.000000000000000000\",\"dailyWithdrawal\":\"-1.000000000000000000\",\"dailyBuy\":\"0.000000000000000000\",\"dailySell\":\"0.000000000000000000\",\"dailyInternalTransfer\":\"0.000000000000000000\",\"monthlyWithdrawal\":\"-1.000000000000000000\",\"monthlyBuy\":\"0.000000000000000000\",\"monthlySell\":\"0.000000000000000000\",\"autoWithdrawal\":\"0.000000000000000000\",\"transferMin\":\"10.000000000000000000\"}]},\"options\":{\"document_groups\":\"corporate\"}},\"lastLoginTime\":\"2024-04-15T02:27:17+00:00\",\"createTime\":\"2024-01-16T08:38:29+00:00\",\"updateTime\":\"2024-04-15T02:27:17+00:00\",\"limits\":{\"maxDemoTradingAccounts\":null,\"maxLiveTradingAccounts\":null}}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:19 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "595"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 10427}, "startedDateTime": "2024-04-15T11:27:19.193Z", "time": 805, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 805}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 595, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "component", "value": "core"}, {"name": "lang", "value": "en"}], "totalSize": 595, "url": "https://api.testmobile-stable.b2broker.tech/api/v1/translations?component=core&lang=en"}, "response": {"bodySize": 321096, "content": {"mimeType": "application/json", "size": 321096, "text": "{\"status\":200,\"data\":{\"nvestmentPlatform.Common.FeeList.JoiningFee.Subtitle\":\"USD/Subscription\",\"InvestmentPlatform.Common.FeeList.JoiningFee.Subtitle\":\"USD/Subscription\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.PammAccountCreated\":\"Related investment account was successfully created\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.PammInvestorAccountDescription\":\"You as a money manager can make a deposit to PAMM Master as one of investors via investment account. This new account is already connected to your new master account and ready to use.\",\"InvestmentPlatform.TransactionRequestStatus.Unspecified\":\"Unspecified\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.BalanceAfterLabel\":\"Balance after withdrawal\",\"InvestmentPlatform.AccountStatistics.Fees.Title\":\"Fees\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.Month\":\"1 month\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.Month18\":\"18 months\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.SixMonths\":\"6 months\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.ThreeMonths\":\"3 months\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.Week\":\"1 week\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Period.Year\":\"12 months\",\"InvestmentPlatform.AccountStatistics.MonthlyReturn.Title\":\"Monthly Return\",\"InvestmentPlatform.AccountStatistics.Overview.ActiveSince\":\"Active Since\",\"InvestmentPlatform.AccountStatistics.Overview.Equity [currency]\":\"Equity, {{currency}}\",\"InvestmentPlatform.AccountStatistics.Overview.MaxDrawdown\":\"Max. Drawdown\",\"InvestmentPlatform.AccountStatistics.Overview.Return\":\"Return\",\"InvestmentPlatform.AccountStatistics.Overview.Risk\":\"Risk\",\"InvestmentPlatform.AccountStatistics.Overview.Score\":\"Score\",\"InvestmentPlatform.AccountStatistics.Overview.Title\":\"Overview\",\"InvestmentPlatform.AccountStatistics.Overview.Trades\":\"Trades\",\"Auth.Login.Wizard.ViewMetatrader.Standalone.SignInAgreementCheckboxText\":\"\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.FeeIDFollower\":\"Fee ID, Follower\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.FeeIDMaster\":\"Fee ID, Master\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.FeeSettings\":\"Fee Settings\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.Follower\":\"Filter By Login\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.Order.ASC\":\"Asc\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.Order.DESC\":\"Desc\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.Order.LabelText\":\"Order\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.SortBy.Amount\":\"Amount\",\"Correspondent IBAN\":\"Correspondent IBAN\",\"Country\":\"Country\",\"History.Transactions.AdditionalInfoDialog.AccountId\":\"Account ID\",\"History.Transactions.AdditionalInfoDialog.Button.Ok\":\"ok\",\"History.Transactions.AdditionalInfoDialog.ClientId\":\"Client ID\",\"History.Transactions.AdditionalInfoDialog.ClientName\":\"Client Name\",\"History.Transactions.AdditionalInfoDialog.Title\":\"Title\",\"How much funds you intend to deposit monthly\":\"How much funds you intend to deposit monthly\",\"I am an adult\":\"I am an adult\",\"I am not US, Iran or North Korea resident\":\"I am not US, Iran or North Korea resident\",\"I am not a US citizen\":\"I am not a US citizen\",\"IBAN\":\"IBAN\",\"ID type\":\"ID Type\",\"InternalTransfer.SelectPlatform.Cancel\":\"Cancel\",\"PageTitle.Security\":\"Security_test\",\"PageTitle.Settings\":\"Settings_test\",\"InvestmentPlatform.AccountDetails.Subscriptions.Filter.ShowInactive\":\"Show Inactive\",\"InvestmentPlatform.AccountDetails.Subscriptions.NoDataMessage\":\"No active subscriptions\",\"InvestmentPlatform.AccountDetails.Subscriptions.ResubscribeError\":\"An error occurred while resubscribing\",\"InvestmentPlatform.AccountDetails.Subscriptions.SuccessfullyUnsubscribed\":\"Successfully Unsubscribed\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.AccountName\":\"Account name\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.AllocationMethod\":\"Allocation Method\",\"Platforms.PrimeXm.AccountParam.Margin\":\"Margin\",\"Platforms.PrimeXm.AccountParam.MarginLevel\":\"Margin Level\",\"Platforms.PrimeXm.AccountParam.UnrealizedPnL\":\"PnL\",\"Platforms.PrimeXm.CardTitle\":\"PXM\",\"Platforms.PrimeXm.Link1.Label\":\"PrimeXm Link1\",\"Platforms.PrimeXm.Link2.Label\":\"PrimeXm Link2\",\"TradingBoard.Widgets.PlaceOrderMaxi.OrderRejected\":\"OrderRejected\",\"Auth.Login.Wizard.Standalone.LoginView.LoginLabel\":\"Login\",\"Auth.Login.Wizard.Standalone.LoginView.InvestmentPlatformSelectLabel\":\"Platform\",\"Auth.Login.Wizard.ViewSideLogin.TokenAuthError\":\"\",\"Android.ctrader\":\"cTrader\",\"Auth.Login.Wizard.View.HowToChangePassport\":\"\",\"Auth.Login.Wizard.View.SupportTitle\":\"\",\"Auth.Login.Wizard.View.TradingAccount\":\"\",\"Auth.Login.Wizard.View.TradingAccountDef\":\"\",\"24h Change\":\"24 Hour Change \",\"24h High\":\"24 Hour High\",\"B2Core.Shared.UiKit.InterfaceSwitcherPanel.Description\":\"The latest version of Trading Room is now available. Try new features with modern design and workflows.\",\"B2Core.Shared.UiKit.InterfaceSwitcherPanel.GoToNewInterface\":\"Go to New Interface\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AccountType\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AccountType.IisBoHint\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AllowBrokerUseFunds\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.HasOtherIis\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.IisInfoMessage\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.Notification\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.Options\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.OtherIisAgreement\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.OtherIisBrokerageReport\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.OtherIisClosureConfirmation\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.OtherIisHolderName\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.OtherIisStatement\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.SignDocumentsBySmsCode\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.Title\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.Continue\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.FileName\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.InfoMessage\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.InfoTitle\":\"\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.SuccessText\":\"\",\"Common.Widgets.WarpInstrumentsCatalog.Title\":\"\",\"Common.Widgets.WarpPortfolio.Title\":\"\",\"Common.Widgets.WarpTransactionsList.Title\":\"\",\"Common.Widgets.tabs.Title\":\"\",\"Core.Form.Errors.After [date]\":\"\",\"Core.Form.Errors.AfterOrEqual [date]\":\"\",\"Core.Form.Errors.Before [date]\":\"\",\"Core.Form.Errors.BeforeOrEqual [date]\":\"\",\"Core.Form.Errors.EndDateInvalid\":\"\",\"Core.Form.Errors.MaxDateInvalid\":\"\",\"Core.Form.Errors.StartDateInvalid\":\"\",\"Core.Pagination.FirstPageLabel\":\"\",\"Core.Pagination.ItemsPerPageLabel\":\"\",\"Core.Pagination.LastPageLabel\":\"\",\"Core.Pagination.NextPageLabel\":\"\",\"Core.Pagination.PreviousPageLabel\":\"\",\"Core.Ui.AppFileInput.Title\":\"\",\"CurrencyTranslationKey\":\"\",\"Dashboard.Wdgets.Pbsr.Analytics.Services.Analytics.Bond\":\"\",\"Dashboard.Wdgets.Pbsr.Analytics.Services.Analytics.Currency\":\"\",\"Dashboard.Wdgets.Pbsr.Analytics.Services.Analytics.EverythingElse\":\"\",\"Dashboard.Wdgets.Pbsr.Analytics.Services.Analytics.MonetaryAssets\":\"\",\"Dashboard.Wdgets.Pbsr.Analytics.Services.Analytics.Stock\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.Assets\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.AssetsGroupsAmount\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.Currency\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.NoAccountsHint\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.NoAccountsTitle\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.NoAssets\":\"\",\"Dashboard.Widgets.Pbsr.Analytics.Sectors\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.AccountInfo.GoToAccount\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.AccountInfo.HiddenAccount\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.AccountInfo.Hide\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.AccountInfo.Rename\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.AccountInfo.Show\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.NoAccounts\":\"\",\"Dashboard.Widgets.Pbsr.MyBriefcase.Total\":\"\",\"Dashboard.Widgets.Pbsr.OperationsList.GoToSection\":\"\",\"Dashboard.Widgets.Pbsr.OperationsList.TradeEventsEmptyMessage\":\"\",\"Dashboard.Widgets.Pbsr.SuggestionsComponent.GoToSuggestions\":\"\",\"Dashboard.Widgets.Pbsr.SuggestionsComponent.Message\":\"\",\"Dashboard.Widgets.Pbsr.TransactionList.GoToSection\":\"\",\"Dashboard.Widgets.Pbsr.TransactionList.TradeEventsEmptyMessage\":\"\",\"Dashboard.Widgets.orderHistory.Title\":\"orderHistory\",\"EnrolmentAndTransfers.Account.BrokerAccount.Label [caption]\":\"\",\"EnrolmentAndTransfers.Account.Iis.Label [caption]\":\"\",\"EnrolmentAndTransfers.Deposit.AllowedIisAmountTitle\":\"Deposit\",\"EnrolmentAndTransfers.Deposit.IisHint\":\"Deposit\",\"EnrolmentAndTransfers.Deposit.Title\":\"Deposit\",\"EnrolmentAndTransfers.DepositAccountSelect.Label\":\"Deposit\",\"EnrolmentAndTransfers.Pages.PbsrDeposit.ErrorDepositResult\":\"\",\"EnrolmentAndTransfers.Pages.PbsrDeposit.SuccessDepositResult\":\"\",\"EnrolmentAndTransfers.Pages.PbsrTransfer.Balance\":\"\",\"EnrolmentAndTransfers.Pages.PbsrTransfer.TransferAccountSelectPlaceholder\":\"\",\"EnrolmentAndTransfers.Pages.PbsrTransfer.TransferAccountSelectTitle\":\"\",\"EnrolmentAndTransfers.Pages.PbsrTransfer.TransferAccountSelectToLabel\":\"\",\"EnrolmentAndTransfers.Pages.PbsrTransfer.TransferButton\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.AccountSelectPlaceholder\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.AccountTransferLabel\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.Balance\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.ButtonProceed\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.DescriptionInfo\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.IisWarningMessage\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.NewPatternName\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.PatternsPlaceholder\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.PatternsSaveDetails\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.SelectGroupCurrencyTitle\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.SelectMethodTitle\":\"\",\"EnrolmentAndTransfers.Pages.PbsrWithdraw.Wizard.PbsrWithdrawView.SelectTypeTitle\":\"\",\"EnrolmentAndTransfers.SelectMethod.Title\":\"\",\"EnrolmentAndTransfers.SelectType.Title\":\"\",\"EnrolmentAndTransfers.Snackbar.RequestAccepted\":\"\",\"EnrolmentAndTransfers.Snackbar.Success\":\"\",\"EnrolmentAndTransfers.TransferAccountSelect.From.Label\":\"\",\"EnrolmentAndTransfers.Withdraw.Success.Message\":\"\",\"EnrolmentAndTransfers.Withdraw.Title\":\"\",\"EnrolmentAndTransfers.Withdraw.ValidateException.Message\":\"\",\"EnrolmentAndTransfers.WithdrawAccountSelect.SelectGroupCurrency.Title\":\"\",\"Eqwire.Account.AccountCreate\":\"\",\"Eqwire.Account.Detail.NotFound\":\"\",\"Eqwire.Account.FooterSignature\":\"\",\"Eqwire.Account.FooterSignature.EU\":\"\",\"Eqwire.Account.FooterSignature.UK\":\"\",\"Eqwire.Beneficiaries.SuccessfulUpdated\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.Amount\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.Balance\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.Cancel\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.Confirm\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.EmptyListMessage\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.PayeeName\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.PaymentDetailsLabel\":\"\",\"Eqwire.Consent.Components.ShareInformationConfirm.Footer\":\"\",\"Eqwire.Consent.Components.ShareInformationConfirm.SelectAccountsLabel\":\"\",\"Eqwire.Consent.Components.ShareInformationConfirm.SuccessConfirmMessage\":\"\",\"Eqwire.Consent.Components.ShareInformationConfirm.Title\":\"\",\"Eqwire.Consent.Const.Permission.ReadAccountsBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadAccountsDetail\":\"\",\"Eqwire.Consent.Const.Permission.ReadBalances\":\"\",\"Eqwire.Consent.Const.Permission.ReadBeneficiariesBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadBeneficiariesDetail\":\"\",\"Eqwire.Consent.Const.Permission.ReadDirectDebits\":\"\",\"Eqwire.Consent.Const.Permission.ReadOffers\":\"\",\"Eqwire.Consent.Const.Permission.ReadPAN\":\"\",\"Eqwire.Consent.Const.Permission.ReadParty\":\"\",\"Eqwire.Consent.Const.Permission.ReadPartyPSU\":\"\",\"Eqwire.Consent.Const.Permission.ReadProducts\":\"\",\"Eqwire.Consent.Const.Permission.ReadScheduledPaymentsBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadScheduledPaymentsDetail\":\"\",\"Eqwire.Consent.Const.Permission.ReadStandingOrdersBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadStandingOrdersDetail\":\"\",\"Eqwire.Consent.Const.Permission.ReadStatementsBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadStatementsDetail\":\"\",\"Eqwire.Consent.Const.Permission.ReadTransactionsBasic\":\"\",\"Eqwire.Consent.Const.Permission.ReadTransactionsCredits\":\"\",\"Eqwire.Consent.Const.Permission.ReadTransactionsDebits\":\"\",\"Eqwire.Consent.Const.Permission.ReadTransactionsDetail\":\"\",\"Eqwire.Consent.Const.Scope.AccountAccess\":\"\",\"Eqwire.Consent.Const.Scope.BeneficiaryAccess\":\"\",\"Eqwire.Consent.Const.Scope.FundConfirmationAccess\":\"\",\"Eqwire.Consent.Const.Scope.PaymentAccess\":\"\",\"Eqwire.Consent.Const.Status.AuthorisationDeclined\":\"\",\"Eqwire.Consent.Const.Status.Authorised\":\"\",\"Eqwire.Consent.Const.Status.AwaitingAuthorisation\":\"\",\"Eqwire.Consent.Const.Status.AwaitingExchange\":\"\",\"Eqwire.Consent.Const.Status.Consumed\":\"\",\"Eqwire.Consent.Const.Status.Expired\":\"\",\"Eqwire.Consent.Const.Status.Revoked\":\"\",\"Eqwire.Consent.Guards.ConsentAccess.WrongUrlMessage\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.AccountNumber\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Amount\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Balance\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Bic\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Cancel\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Confirm\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Currency\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Description\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.DestinationAccount\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Iban\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Name\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.PaymentReference\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.SortCode\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.SourceAccount\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Title\":\"\",\"Eqwire.PaymentsAndTransfers.ConfirmPaymentDialog.Type\":\"\",\"Eqwire.PaymentsAndTransfers.DestinationAccount\":\"\",\"Eqwire.PaymentsAndTransfers.MakeTransfer.SuccessMessage'\":\"\",\"EqwireAccounts.EqwireAccountDetail.AccountNumber\":\"\",\"EqwireAccounts.EqwireAccountDetail.AddNewFunds\":\"\",\"EqwireAccounts.EqwireAccountDetail.Alias\":\"\",\"EqwireAccounts.EqwireAccountDetail.AvailableBalance\":\"\",\"EqwireAccounts.EqwireAccountDetail.Bic\":\"\",\"EqwireAccounts.EqwireAccountDetail.CurrentBalance\":\"\",\"EqwireAccounts.EqwireAccountDetail.Filters\":\"\",\"EqwireAccounts.EqwireAccountDetail.HelpDesk\":\"\",\"EqwireAccounts.EqwireAccountDetail.Iban\":\"\",\"EqwireAccounts.EqwireAccountDetail.IsBlockedAccount\":\"\",\"EqwireAccounts.EqwireAccountDetail.PaymentOrTransfer\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CorporateActionCosts\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CorporateActionParticipationFee\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CurrencyConversion\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.DepositoryFeesOfOtherDepositories\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.DepositoryFeesOfOwnDepository\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.FixedBrokerageCommission\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IncomeFromFinancialInstruments\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IncomeWriteOffOfCouponsToFromBrokerageAccount\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IncomeWriteOffProfitsOnFinancialInstrument\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IndirectExpensesOfCurrencyMarket\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IndirectExpensesOfDerivativesMarket\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.IndirectExpensesOfStockMarket\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.LoanCrediting\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.LoanRefund\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.LoanedSecuritiesCrediting\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.MarginTradingFee\":\"\",\"In progress\":\"\",\"Increase level\":\"\",\"Layouts.FullLayout.PbsSignDocumentsNotification.ApplyButtonText\":\"\",\"Layouts.FullLayout.PbsSignDocumentsNotification.Message\":\"\",\"Layouts.FullLayout.PbsSignDocumentsNotification.Title\":\"\",\"Layouts.FullLayout.PbsUpgradeLevelNotification.ApplyButtonText\":\"\",\"Layouts.FullLayout.PbsUpgradeLevelNotification.Message\":\"\",\"Layouts.FullLayout.PbsUpgradeLevelNotification.Title\":\"\",\"Layouts.PbsFooter.FirstInfoBlock\":\"\",\"Layouts.PbsFooter.FirstMessageTitle\":\"\",\"Layouts.PbsFooter.FourthMessageTitle\":\"\",\"Layouts.PbsFooter.SecondInfoBlock\":\"\",\"Layouts.PbsFooter.SecondMessageTitle\":\"\",\"Layouts.PbsFooter.ThirdMessageTitle\":\"\",\"MacOS.match_trader\":\"\",\"MacOS.mt4\":\"MacOS.mt4\",\"MacOS.mt5\":\"MacOS.mt5\",\"Mainland China\":\"Mainland China\",\"Manually\":\"\",\"Menu_Briefcase\":\"\",\"Menu_EqwireAccounts\":\"\",\"Menu_EqwireBeneficiaries\":\"\",\"Menu_EqwirePaymentsAndTransfers\":\"\",\"Menu_ExternalLink2\":\"\",\"Menu_ExternalLink3\":\"\",\"Menu_ExternalLink4\":\"\",\"Menu_ExternalLink5\":\"\",\"Menu_MarginalAccounts\":\"Marginal Accounts\",\"Menu_PammNew\":\"\",\"Menu_PammNew_AccountList\":\"\",\"Menu_PammNew_Leaderboard\":\"\",\"Menu_Pbsr_BrokerageAccountEventsHistory\":\"\",\"Menu_Pbsr_BrokerageOrders\":\"\",\"Menu_Pbsr_Catalog\":\"\",\"Menu_Pbsr_Deposit\":\"\",\"Menu_Pbsr_Documents\":\"\",\"Menu_Pbsr_EnrolmentAndTransfers\":\"\",\"Menu_Pbsr_EventsAndAssignments\":\"\",\"Menu_Pbsr_NewsAndOverviews\":\"\",\"Menu_Pbsr_OperationsAndApplications\":\"\",\"Menu_Pbsr_OperationsAndOrders\":\"\",\"Menu_Pbsr_ReportsAndCertificates\":\"\",\"Menu_Pbsr_TradeIdeas\":\"\",\"Menu_Pbsr_Transfer\":\"\",\"Menu_Pbsr_Withdraw\":\"\",\"Menu_Pbsr_WithdrawalOrders\":\"\",\"Main settings\":\"Main settings\",\"Menu_Trade_Moex\":\"\",\"Moex.Widgets.PbsAnalytics.Title\":\"\",\"Moex.Widgets.PbsMyBriefcase.Title\":\"\",\"Moex.Widgets.PbsSuggestions.Title\":\"\",\"Moex.Widgets.PbsTransactionsList.Title\":\"\",\"Moex.Widgets.Support.Title\":\"\",\"Moex.Widgets.Walkthrough.Title\":\"\",\"Moex.Widgets.WarpInstrumentsCatalog.Title\":\"\",\"MoexOrder.Active\":\"\",\"MoexOrder.Cancelled\":\"\",\"MoexOrder.Filled\":\"\",\"MoexOrder.Rejected\":\"\",\"NewsAndOverviews.Title\":\"\",\"On test\":\"\",\"Menu_Trade_Common\":\"Common\",\"Menu_Trading\":\"Trading\",\"Profile.PersonalInfo.Contact.Title\":\"\",\"Profile.PersonalInfo.Documents.Title\":\"\",\"Profile.PersonalInfo.DocumentsAndAddresses.AdditionalDataInn\":\"\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderModify\":\"OrderModify\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderReplay\":\"\",\"info_description_transfer\":\"This is the payment message !\",\"Wallet.Details.Overview.Balance\":\"Balance\",\"Wallet.Details.Overview.Hold\":\"Hold\",\"Wallet.Details.Overview.Title\":\"Title\",\"Wallet.Details.Title\":\"Title\",\"Wallet.Details.TransactionsHistory.Title\":\"Transactions\",\"Wallet.WalletList.Card.Available\":\"Available\",\"Wallet.WalletList.Card.Balance\":\"Balance\",\"Wallet.WalletList.Card.CopyId.ErrorMessage\":\"Error Message\",\"Wallet.WalletList.Card.CopyId.SuccessMessage\":\"Wallet Added Successfully\",\"Wallet.WalletList.Card.Equity\":\"Equity\",\"Wallet.WalletList.Card.FreeMargin\":\"Free Margin\",\"Wallet.WalletList.Card.MarginLevel\":\"Margin Level\",\"Wallet.WalletList.Card.OnHold\":\"On hold\",\"Wallet.WalletList.Card.PL\":\"P/L\",\"Wallet.WalletList.Card.Total\":\"Total\",\"Wallet.WalletList.Card.UsedMargin\":\"Margin\",\"Wallet.WalletList.Card.WalletId [id]\":\"ID: {{id}}\",\"Wallet.WalletList.Card.WalletMenu.Details\":\"Details\",\"Wallet.WalletList.HideZeroBalances\":\"Hide Zero Balances\",\"Wallet.WalletList.Tabs.All\":\"All\",\"Wallet.Wizards.AddWallet.AccountsLimitReached [product]\":\"Accounts limit reached\",\"Wallet.Wizards.AddWallet.Button.Back\":\"Back\",\"Wallet.Wizards.AddWallet.EmptyListMessage\":\"Empty List\",\"Wallet.Wizards.AddWallet.SearchLabel\":\"Search\",\"Wallet.Wizards.AddWallet.SuccessMessage\":\"Success\",\"Wallet.Wizards.List.Button.AddCurrency\":\"Add Currency\",\"Wallet.Wizards.List.EmptyFilterResultMessage\":\"EmptyFilterResultMessage\",\"Wallet.Wizards.List.EmptyListMessage\":\"Empty Wallets List\",\"Wallet.Wizards.List.Filters.SortLabel\":\"Sort by\",\"Wallet.Wizards.List.HideBalances\":\"Hide Balances\",\"Wallet.Wizards.List.SearchLabel\":\"Search\",\"Wallet.Wizards.List.ShowBalances\":\"Show Balances\",\"Wallet.Wizards.List.Sort.Label.Balance.Asc\":\"Balance ↑\",\"Wallet.Wizards.List.Sort.Label.Balance.Desc\":\"Balance ↓\",\"Wallet.Wizards.List.Sort.Label.Default\":\"Default\",\"Wallet.Wizards.List.Sort.Label.Name.Asc\":\"Name ↑\",\"Wallet.Wizards.List.Sort.Label.Name.Desc\":\"Name ↓\",\"Wallets\":\"Wallets\",\"Web\":\"Web\",\"Web.ctrader\":\"cTrader\",\"Web.dxtrade\":\"Web dxtrade\",\"Web.match_trader\":\"Web\",\"Web.mt4\":\"Web.mt4\",\"Web.mt5\":\"Web.mt5\",\"Welcome into <strong>{project_name}</strong>\":\"Welcome into <strong>QA World</strong>\",\"What can we help you with?\":\"What can we help you with?\",\"Widget unavailable now\":\"Widget unavailable now\",\"Windows\":\"Windows\",\"Windows.ctrader\":\"cTrader\",\"Windows.dxtrade\":\"Windows dxtrade\",\"Windows.mt4\":\"123123\",\"Windows.mt5\":\"Windows.mt5\",\"Withdraw\":\"Withdraw\",\"Withdraw amount\":\"Withdraw amount\",\"Withdraw to\":\"Withdraw to\",\"Withdraw.FeeLevels.HighFeeDescription\":\"Priority transaction processing through high fees.\",\"Withdraw.FeeLevels.LowFeeDescription\":\"Economy mode when speed does not matter.\",\"Withdraw.FeeLevels.MediumFeeDescription\":\"Optimum processing speed for a reasonable fee.\",\"WithdrawalPatterns.DeleteDialog.Body\":\"Body\",\"WithdrawalPatterns.DeleteDialog.Cancel\":\"Cancel\",\"WithdrawalPatterns.DeleteDialog.Confirm\":\"Confirm\",\"WithdrawalPatterns.DeleteDialog.Title\":\"Title\",\"WithdrawalPatterns.DetailsDialog.Apply\":\"Apply\",\"WithdrawalPatterns.DetailsDialog.Close\":\"Close\",\"WithdrawalPatterns.DetailsDialog.Fields.SavedAt\":\"Saved At\",\"WithdrawalPatterns.DetailsDialog.Title\":\"Title\",\"WithdrawalPatterns.NoPatterns\":\"No Patterns\",\"WithdrawalPatterns.PatternRemoved\":\"Pattern Removed\",\"WithdrawalPatterns.Table.Delete\":\"Delete\",\"WithdrawalPatterns.Table.Name\":\"Name\",\"WithdrawalPatterns.Table.RemovePattern\":\"Remove Pattern\",\"WithdrawalPatterns.Table.SavedAt\":\"Saved At\",\"WithdrawalPatterns.Title\":\"Withdrawal Patterns\",\"Wizard.CodeConfirmation.Error.TooManyTries\":\"Too Many Tries\",\"Wizard.Manager.UserLockedMessage\":\"User is Locked\",\"Working\":\"Working\",\"YYYY\":\"YYYY\",\"Year\":\"Year\",\"Yes\":\"Yes\",\"You can't create an account until previous request is not approved\":\"You can't create an account until previous request is not approved\\t\",\"You have Successful Exchanged\":\"You have Successful Exchanged\",\"You have no access to internal transfer\":\"You have no access to internal transfer\\t\",\"You have no accounts\":\"You have no accounts\",\"You have no accounts to exchange\":\"You have no accounts to exchange\",\"You have no accounts to transfer\":\"You have no accounts to transfer\",\"You have no any messages\":\"You have no new messages\\t\",\"You have not had Accounts yet\":\"You have not had Accounts yet\",\"You have not had Demo Accounts yet\":\"You have not had Demo Accounts yet\",\"You have not had Live Accounts yet\":\"You have not had Live Accounts yet\",\"You have reached the limit\":\"You have reached the limit\",\"You will be logged off in\":\"You will be logged off in\",\"Your Demo Accounts\":\"Your Demo Accounts\",\"Your Deposit operation was not successful\":\"Your Deposit operation was not successful\",\"Your Live Accounts\":\"Your Live Accounts\",\"Your Tier Fee: {{value}}%\":\"Your Tier Fee: {{value}}\",\"Your Tier: {tier}\":\"Your Tier: {{tier}}\\t\",\"Your application is being processed\":\"Your application is being processed\",\"Your deposit failed\":\"Your deposit failed\",\"Your deposit was successful\":\"Your deposit was successful\",\"Your reply\":\"Your reply\",\"InvestmentPlatform.AccountDetails.Settings.Presence.Message\":\"The visibility status identifies if an account is visible or hidden on the Leaderboard.\",\"InvestmentPlatform.AccountDetails.Settings.Presence.Status\":\"Status\",\"Your request processing\":\"Your request processing\",\"Your verification level\":\"Your verification level\",\"Your verification status is\":\"Your verification status is\",\"ZIP code\":\"ZIP code\",\"advLogoUrl\":\"https://b2bx.exchange/\",\"bonus_status_active\":\"Active\",\"bonus_status_all\":\"All\",\"bonus_status_completed\":\"Completed\",\"bonus_status_error\":\"Error\",\"bonus_status_expired\":\"Expired\\n\",\"bonus_status_hold\":\"hold\",\"bonus_status_pending\":\"Pending\",\"button.Buy\":\"Buy\",\"Auth.Login.Wizard.ViewMetatrader.CardContent.SignIn\":\"Sign in\",\"button.Sell\":\"Sell\",\"code was sent to your email\":\"code was sent to your email\",\"code was sent via SMS\":\"code was sent via SMS\",\"code was sent via SMS on your phone\":\"code was sent via SMS on your phone\",\"commission.max\":\"Maximum Commission\",\"commission.min\":\"Minimum Commission\",\"InvestmentPlatform.AccountDetails.Settings.Presence.StatusPrivate\":\"Hidden\",\"InvestmentPlatform.AccountDetails.Settings.Presence.StatusPublic\":\"Visible\",\"confirm\":\"Сonfirm\",\"InvestmentPlatform.AccountList.Sort.Name.Desc\":\"Name ↓\",\"InvestmentPlatform.AccountDetails.Settings.Presence.Title\":\"Privacy\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Button.AddNew\":\"+ Add new offer\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.ChangeOfferButton\":\"Change offer\",\"InvestmentPlatform.AddInvestmentAccountWizard.LoginPasswordStep.Description\":\"Enter account ID and password of existing trading account\",\"InvestmentPlatform.AddMasterAccountWizard.SetupFeesPlan.Step.Description\":\"Set up Fee Plan for your Master account\",\"InvestmentPlatform.Common.FeeList.JoiningFee.Title\":\"Joining Fee\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.ConfirmationDialog.SubmitButtonLabel\":\"Delete\",\"InvestmentPlatform.AccountDetails.Subscriptions.ConfirmUnsubscriptionDialog.Message\":\"This action cannot be undone. Click the \\\"Unsubscribe\\\" button to unsubscribe from the master account and stop copying positions from this account.<br/><br/>All currently open positions on your investment account that have been copied from this master account will be closed.<br/><br/>Prior to unsubscription, you will be charged the fees defined in the Fee Plan for this subscription.\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.ConfirmationDialog.Body [name]\":\"Are you sure you want to delete \\\"{{name}}\\\"?\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.ConfirmationDialog.CancelButtonLabel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.ConfirmationDialog.Title\":\"Delete promo offer\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.CopyToClipboardTooltip\":\"Copy to clipboard\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.ApplyButton\":\"Apply\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.CancelButton\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.NameLabel\":\"Name\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.PromoCodeLabel\":\"Promo сode\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.SpecifyConditionsDescription\":\"Will be available for users with promo code\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.SpecifyConditionsTitle\":\"Specify conditions\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.TitleAdd\":\"Add new offer\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.TitleEdit\":\"Change promo offer\",\"InvestmentPlatform.Common.SubscribeDialog.Message.UnavailableAccounts\":\"You have no accounts for deposit\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Title\":\"Promo Offers\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.ButtonChangeTime\":\"Change time\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.FreezeTime\":\"Freeze time\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.FreezeTime.Placeholder\":\"{{value}} min\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.FreezeTimeInMin [value]\":\"{{value}} min\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.Message\":\"All requests for deposit and withdrawal which will be sent during that period before rollover will be executed in next rollover\",\"InvestmentPlatform.AccountDetails.Settings.RolloverFreezeTime.Title\":\"Freeze Time for rollover\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.ButtonChangeType\":\"Change Type\",\"InvestmentPlatform.AccountList.Sort.Balance.Asc\":\"Balance ↑\",\"currency_code\":\"Currency Code\",\"days\":\"days\",\"decline\":\"Decline\",\"default\":\"default\",\"destination_tag\":\"Destination Tag\",\"done\":\"Done\",\"doubleClick.disclaimer.button.cancel\":\"Cancel\",\"doubleClick.disclaimer.button.ok\":\"Proceed\",\"doubleClick.disclaimer.text\":\"Double-click trading will allow you to place new orders, cancel working ones and close positions without confirmation with just DOUBLE CLICK.\\n<br><br>\\nSINGLE CLICK will still open New Order or Confirmation window before placing a new order and will open a Confirmation window before closing a position or canceling a working order.\",\"email2\":\"\\n\",\"errors.age {{value}}\":\"User must be over {{value}} y.o.\",\"errors.array\":\"Must enter at least one value\",\"errors.date\":\"Invalid date\",\"errors.digits\":\"Error Digits\",\"errors.email\":\"Invalid email\",\"errors.file\":\"file\",\"errors.filters.to\":\"Filter Validation Error\",\"errors.in\":\"Select one of the suggested values\",\"errors.incorrect\":\"Invalid value\",\"errors.integer\":\"Field must contain only numbers\",\"errors.invalidCardHolderName\":\"Invalid Cardholders's Name\",\"errors.invalidCardNumber\":\"Invalid Card Number\",\"errors.max {{value}}\":\"Value cannot exceed {{value}} characters\",\"errors.maxFileSize {{value}}\":\"Maximum File Size is {{value}} Mb\",\"errors.maxFilesAmount {{value}}\":\"Maximum File Amount is {{value}}\",\"errors.maxLength {{value}}\":\"Maximum length is {{value}}\",\"errors.maxValue {{value}}\":\"Value cannot exceed {{value}}\",\"errors.mimetypes {{value}}\":\"File type is not supported, allowed types are {{value}}\",\"errors.min {{value}}\":\"Value can not be less than {{value}} characters\",\"errors.minLength {{value}}\":\"Minimum length is {{value}}\",\"errors.minValue {{value}}\":\"Value cannot be less {{value}}\",\"errors.mustUseValueFromAutosuggest\":\"Select one of the suggested values\",\"errors.noConfigForField\":\"There is no required field in form. Please contact administrator\",\"errors.notZero\":\"Value is zero\",\"errors.numeric\":\"Value is non-numeric\",\"errors.phone\":\"Invalid phone\",\"errors.postalcode\":\"Invalid postal code\",\"errors.required\":\"Required\",\"errors.requiredLength {{value}}\":\"Field must contain {{value}} characters\",\"errors.requiredValue\":\"It is a required value.\",\"InvestmentPlatform.Common.FeeList.JoiningFee.Description\":\"A fixed amount paid on subscription\",\"InvestmentPlatform.AccountDetails.Settings.PromoOffers.Popup.AddButton\":\"Add\",\"InvestmentPlatform.Common.FeeList.JoiningFee.Error.MaxValue [value]\":\"Max. joining fee - {{value}}USD\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.Message\":\"All Deposit and Withdrawal requests from investors are executed during rollover.\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.RolloverHours.Others [count]\":\"+ {{count}} others\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.RolloverHours.Placeholder\":\"Rollover hours\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.RolloverType.Manual\":\"Manual\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.RolloverType.Scheduled\":\"Scheduled\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.Title\":\"Rollover Schedule\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.TypeOfRollover\":\"Type of rollover\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.TypeOfRollover.Placeholder\":\"Type of rollover\",\"InvestmentPlatform.AccountDetails.Settings.RolloverSchedule.Update.Success\":\"Rollover schedule was successfully updated\",\"InvestmentPlatform.AccountDetails.Settings.SaveCancelButtons.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Settings.SaveCancelButtons.Save\":\"Save\",\"InvestmentPlatform.AccountDetails.Settings.Strategy.ButtonEditDescription\":\"Edit Description\",\"InvestmentPlatform.AccountDetails.Settings.Strategy.ExampleStrategy\":\"Example Strategy\",\"InvestmentPlatform.AccountDetails.Settings.Strategy.Message\":\"Strategy description will be shown on the Statistics page of your Master account. Describe your strategy and let your potential followers learn more about your trading style.\",\"B2Copy.CopyTrading.WalletAccount.Login\":\"Login\",\"InvestmentPlatform.AccountDetails.Settings.Strategy.Placeholder\":\"Type your strategy description here\",\"InvestmentPlatform.AccountDetails.Settings.Strategy.Title\":\"Strategy Description\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ButtonAddCode\":\"Add subscription code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ButtonDelete\":\"Delete code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ButtonGenerateCode\":\"Generate code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Button.Delete\":\"Delete\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Button.Generate\":\"Generate\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Header.Delete\":\"Delete subscription code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Header.Generate\":\"New subscription code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Message.Delete\":\"Are you sure you want to delete subscription code?\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ConfirmDialog.Message.Generate\":\"Are you sure you want to generate new subscription code?\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.CopyCode.Hint\":\"Copy code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.CopyCode.Message\":\"Code has been successfully copied\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.InputLabel\":\"Subscription code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.Message\":\"Set a subscription code to allow search for hidden accounts and subscription to all accounts (visible and hidden) only with this code.\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCodeGeneratedAndCopied\":\"The subscription code has been successfully generated and copied to clipboard\",\"InvestmentPlatform.AccountDetails.Settings.Title\":\"Settings\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenu.EditValues\":\"Edit Values\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenu.Subscribe\":\"Subscribe\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenu.Unsubscribe\":\"Unsubscribe\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenu.ViewFees\":\"View Fee Plan\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenu.ViewPaidFees\":\"View Paid Fees\",\"InvestmentPlatform.AccountDetails.Subscriptions.ActionMenuButton.Action\":\"Action\",\"InvestmentPlatform.AccountDetails.Subscriptions.ConfirmUnsubscriptionDialog.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Subscriptions.ConfirmUnsubscriptionDialog.Proceed\":\"Unsubscribe\",\"InvestmentPlatform.AccountDetails.Subscriptions.ConfirmUnsubscriptionDialog.Title\":\"Confirm Unsubscription\",\"InvestmentPlatform.AccountDetails.Subscriptions.Currency\":\"Currency\",\"InvestmentPlatform.AccountDetails.Subscriptions.Edit.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.Subscriptions.Edit.Save\":\"Save\",\"InvestmentPlatform.AccountDetails.Subscriptions.Equity\":\"Equity\",\"InvestmentPlatform.AccountDetails.Subscriptions.FeesPaid\":\"Paid Fees\",\"InvestmentPlatform.AccountList.AccountTypeLabel.B2Copy.Follower\":\"Investment\",\"InvestmentPlatform.AccountList.AccountTypeLabel.B2Copy.Master\":\"Master\",\"InvestmentPlatform.AccountList.AccountTypeLabel.Mam.Follower\":\"Investment\",\"InvestmentPlatform.AccountList.AccountTypeLabel.Mam.Master\":\"Master\",\"InvestmentPlatform.AccountList.AccountTypeLabel.Pamm.Follower\":\"Investment\",\"InvestmentPlatform.AccountList.AccountTypeLabel.Pamm.Master\":\"Master\",\"InvestmentPlatform.AccountList.Components.AccountCreateCard.CreateNewFollowerAccount\":\"Create New\\nInvestment Account\",\"InvestmentPlatform.AccountList.Components.AccountCreateCard.CreateNewMasterAccount\":\"Create New\\nMaster Account\",\"InvestmentPlatform.AccountList.Components.AccountCreateCard.WarningMessage.AccountOpeningLimitReached\":\"You’ve reached the limit for the number of accounts that you can create at this time. To create a new account, please archive one of your existing accounts.\",\"InvestmentPlatform.AccountList.CreateForm.AccountIsBeingCreatedMessage\":\"Account is being created. Please wait...\",\"InvestmentPlatform.AccountList.Sort.Balance.Desc\":\"Balance ↓\",\"InvestmentPlatform.AccountList.Sort.Default\":\"Default\",\"InvestmentPlatform.AccountList.Sort.Name.Asc\":\"Name ↑\",\"InvestmentPlatform.AccountList.WalletAccount.Balance [alpha]\":\"Balance, {{alpha}}\",\"InvestmentPlatform.AccountList.WalletAccount.Equity [alpha]\":\"Equity, {{alpha}}\",\"InvestmentPlatform.AccountList.WalletAccount.Login\":\"Login\",\"InvestmentPlatform.AccountList.WalletAccount.Title\":\"Wallet Account\",\"InvestmentPlatform.AccountStatistics.AccountNotFound\":\"Account not found\",\"InvestmentPlatform.AccountStatistics.BalanceEquityChartTooltip.Balance\":\"Balance\",\"InvestmentPlatform.AccountStatistics.BalanceEquityChartTooltip.Equity\":\"Equity\",\"InvestmentPlatform.AccountStatistics.Deals.NoDataMessage\":\"There are no deals to show\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Comment\":\"Comment\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Commission\":\"Commission\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.DealId\":\"Deal ID\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Entry\":\"Entry\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Lot\":\"Lot\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.MasterAccountId\":\"Master Account ID\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.MasterPositionId\":\"Master Position ID\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.PositionId\":\"Position ID\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Price\":\"Price\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Profit\":\"Profit\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Swap\":\"Swap\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Symbol\":\"Symbol\",\"InvestmentPlatform.AccountStatistics.Deals.TableHeader.Time\":\"Time\",\"InvestmentPlatform.AccountStatistics.Overview.ScoreTooltip\":\"The account rating is determined based on the number of points awarded to the account. The points are awarded according to various trading parameters, such as Return calculated for different periods, Max. drawdown, etc.\",\"InvestmentPlatform.AccountStatistics.Positions.Close\":\"Close position\",\"InvestmentPlatform.AccountStatistics.Positions.ClosePosition.Success\":\"Position was successfully closed\",\"InvestmentPlatform.AccountStatistics.Positions.ClosePosition.SymbolClosed\":\"Trading session for the symbol is closed\",\"Auth.Login.Wizard.ViewMetatrader.CardContent.Subtitle\":\"with your existing MetaTrader account credentials\",\"Auth.Login.Wizard.ViewMetatrader.CardHeader.Title\":\"Don’t have account yet? Create a new one now!\",\"Auth.Login.Wizard.ViewMetatrader.RestoreLink.Label\":\"Forgot your password?\",\"Auth.Login.Wizard.ViewMetatrader.SubmitButton.Label\":\"Sign in\",\"B2Copy.AddMasterAccountWizard.Step2.Nickname.Template [userId]\":\"New user {{userId}}\",\"B2Copy.CopyTrading.AccountDetails.Positions.NoDataMessage\":\"No available positions to display\",\"B2Copy.CopyTrading.AccountDetails.Title\":\"Account Details\",\"B2Copy.CopyTrading.AccountGroupDescription.Follower.MT4\":\"Investment accounts MT4\",\"B2Copy.CopyTrading.AccountGroupDescription.Follower.MT5\":\"Investment accounts MT5\",\"B2Copy.CopyTrading.AccountGroupDescription.Master.MT4\":\"MT4\",\"B2Copy.CopyTrading.AccountGroupDescription.Master.MT5\":\"MT5\",\"B2Copy.CopyTrading.AccountGroupTitle.Follower\":\"Investment\",\"B2Copy.CopyTrading.AccountGroupTitle.Master\":\"Master\",\"B2Copy.CopyTrading.DefaultPageTitle\":\"My Accounts\",\"B2Copy.CopyTrading.Details.Title.FeePayment\":\"Fee Payments\",\"B2Copy.CopyTrading.Details.Title.Settings\":\"Settings\",\"B2Copy.CopyTrading.Details.Title.Statistics\":\"Statistics\",\"B2Copy.CopyTrading.Details.Title.Subscriptions\":\"Subscriptions\",\"B2Copy.CopyTrading.Details.Title.TradingReport\":\"Trading Report\",\"B2Copy.Leaderboard.AUM\":\"AUM, USD\",\"B2Copy.Leaderboard.Followers\":\"Followers\",\"B2Copy.Leaderboard.Profit7d\":\"Return (7d)\",\"B2Copy.Leaderboard.ProfitAll\":\"Return (Total)\",\"B2Copy.Leaderboard.Score\":\"Score\",\"B2Copy.Leaderboard.Title\":\"Leaderboard\",\"B2Copy.Leaderboard.TraderAccount.LessThanDaysCount [daysCount]\":\"<{{daysCount}} Days\",\"B2Copy.Leaderboard.TraderAccount.MoreThanDaysCount [daysCount]\":\">{{daysCount}} Days\",\"B2Copy.Leaderboard.TraderAccount.MoreThanOneYearCount [yearsCount]\":\">{{yearsCount}} Year\",\"B2Copy.Leaderboard.TraderAccount.MoreThanYearsCount [yearsCount]\":\">{{yearsCount}} Years\",\"B2Copy.Leaderboard.TraderAccountView.Risk\":\"Risk\",\"B2Copy.Leaderboard.TraderDetails.FollowersChart.Followers\":\"Followers\",\"B2Copy.Leaderboard.TraderDetails.MaxDailyLoss\":\"Max Daily Loss\",\"B2Copy.Leaderboard.TraderDetails.MaxDailyProfit\":\"Max Daily Profit\",\"B2Copy.Leaderboard.TraderDetails.RealizedPl [currency]\":\"Realized PnL, {{currency}}\",\"B2Copy.Leaderboard.TraderDetails.Title\":\"Account Statistics\",\"B2Copy.Shared.TraderStatistics.Followers.AUM\":\"AUM\",\"B2Copy.Shared.TraderStatistics.Followers.Followers\":\"Followers\",\"B2Copy.Shared.TraderStatistics.Followers.FollowersLastWeek\":\"Followers Last Week\",\"B2Copy.Shared.TraderStatistics.Followers.Title\":\"Followers\",\"B2Copy.Shared.TraderStatistics.Positions.TimeDuration.Days\":\"d\",\"B2Copy.Shared.TraderStatistics.Positions.TimeDuration.Hours\":\"h\",\"B2Copy.Shared.TraderStatistics.Positions.TimeDuration.Minutes\":\"m\",\"B2Copy.SubscriptionStatus.Pause\":\"Pause\",\"InvestmentPlatform.AccountDetails.Error.AccountNotFound\":\"Account not found\",\"InvestmentPlatform.AccountDetails.FeePayments.Title\":\"Transactions\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Amount\":\"Amount\",\"InvestmentPlatform.AccountDetails.Subscriptions.Filter.HideInactive\":\"Hide Inactive\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.SortBy.Date\":\"Date\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.SortBy.FollowerLogin\":\"Login\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Filters.SortBy.LabelText\":\"Sort By\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.FromFollower\":\"From Follower\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.NoDataMessage\":\"No transactions yet\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Time\":\"Created\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.ToMaster\":\"To Master\",\"InvestmentPlatform.AccountDetails.FeePayments.TransactionsTable.Type\":\"Fee Type\",\"InvestmentPlatform.AccountDetails.OpenedPositions.Title\":\"Open Positions\",\"InvestmentPlatform.AccountDetails.Overview.AUM\":\"AUM\",\"InvestmentPlatform.AccountDetails.Overview.Balance\":\"Balance\",\"InvestmentPlatform.AccountDetails.Overview.Equity\":\"Equity\",\"InvestmentPlatform.AccountDetails.Overview.FeesReceived\":\"Received Fees\",\"InvestmentPlatform.AccountDetails.Overview.FloatingPL\":\"Floating PnL\",\"InvestmentPlatform.AccountDetails.Overview.Followers\":\"Followers\",\"InvestmentPlatform.AccountDetails.Overview.PaidFees\":\"Paid Fees\",\"InvestmentPlatform.AccountDetails.Overview.RealizedPL\":\"Realized PnL\",\"InvestmentPlatform.AccountDetails.Overview.Title\":\"Overview\",\"InvestmentPlatform.AccountDetails.PammRequests.Button.ExecuteAllRequests\":\"Execute All Requests\",\"InvestmentPlatform.AccountDetails.PammRequests.CancelRequest.Success\":\"Request was successfully canceled\",\"InvestmentPlatform.AccountDetails.PammRequests.ConfirmationDialog.CancelRequest.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.PammRequests.ConfirmationDialog.CancelRequest.Button.Proceed\":\"Cancel Request\",\"InvestmentPlatform.AccountDetails.PammRequests.ConfirmationDialog.CancelRequest.Description\":\"Are you sure you want to cancel request?\",\"InvestmentPlatform.AccountDetails.PammRequests.ConfirmationDialog.CancelRequest.Title\":\"Confirmation\",\"InvestmentPlatform.AccountDetails.PammRequests.ExecuteAllRequest.Success\":\"Requests were successfully executed\",\"InvestmentPlatform.AccountDetails.PammRequests.ExecuteRequest.Success\":\"Request was successfully executed\",\"InvestmentPlatform.AccountDetails.PammRequests.NoDataMessage\":\"There are no requests\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Amount\":\"Amount\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Button.Execute\":\"Execute\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Created\":\"Created\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Currency\":\"Currency\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.ExecutionTime\":\"Execution Time\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.From\":\"From\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Status\":\"Status\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.TicketId\":\"Ticket ID\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Trigger\":\"Trigger\",\"InvestmentPlatform.AccountDetails.PammRequests.Table.Type\":\"Type\",\"InvestmentPlatform.AccountDetails.PammRequests.Tabs.ExecutedRequest\":\"Executed Requests\",\"InvestmentPlatform.AccountDetails.PammRequests.Tabs.PendingRequest\":\"Pending Requests\",\"InvestmentPlatform.AccountDetails.Settings.ClosedPositionLimit.ButtonChangeNumber\":\"Change number\",\"InvestmentPlatform.AccountDetails.Settings.ClosedPositionLimit.InputLabel\":\"Closed positions to display\",\"InvestmentPlatform.AccountDetails.Settings.ClosedPositionLimit.Message\":\"Only selected number of last closed positions is displayed in Trading History\",\"InvestmentPlatform.AccountDetails.Settings.ClosedPositionLimit.Title\":\"Trading History Visibility\",\"InvestmentPlatform.AccountDetails.Settings.Fees.ButtonChangeFees\":\"Change Fee Plan\",\"InvestmentPlatform.AccountDetails.Settings.Fees.Title\":\"Fee Plan\",\"InvestmentPlatform.AccountDetails.Settings.MinimumDeposit.ButtonChangeAmount\":\"Change Amount\",\"InvestmentPlatform.AccountDetails.Settings.MinimumDeposit.Label\":\"Min Deposit\",\"InvestmentPlatform.AccountDetails.Settings.MinimumDeposit.Message\":\"The minimum balance that investors must have on their investment accounts to be able to subscribe to your master account. This is also the minimum balance that must be kept on investment accounts after withdrawing funds to keep the subscriptions.\",\"InvestmentPlatform.AccountDetails.Settings.MinimumDeposit.Title\":\"Minimum Deposit\",\"InvestmentPlatform.AccountDetails.Settings.Presence.ButtonChangeStatus\":\"Change Status\",\"InvestmentPlatform.AccountStatistics.Positions.ClosePrice\":\"Close price\",\"InvestmentPlatform.AccountStatistics.Positions.CloseTime\":\"Close Time\",\"InvestmentPlatform.AccountStatistics.Positions.Commission\":\"Commission\",\"InvestmentPlatform.AccountStatistics.Positions.Detach\":\"Detach position\",\"InvestmentPlatform.AccountStatistics.Positions.Duration\":\"Duration\",\"InvestmentPlatform.AccountStatistics.Positions.InvestorAccountId\":\"Investor Account Id\",\"InvestmentPlatform.AccountStatistics.Positions.MasterAccountId\":\"Master Account Id\",\"InvestmentPlatform.AccountStatistics.Positions.MasterPositionId\":\"Master Position Id\",\"InvestmentPlatform.AccountStatistics.Positions.NoDataMessage\":\"There is no history to show\",\"InvestmentPlatform.AccountStatistics.Positions.OpenPrice\":\"Open Price\",\"InvestmentPlatform.AccountStatistics.Positions.OpenTime\":\"Open Time\",\"InvestmentPlatform.AccountStatistics.Positions.PositionId\":\"Position Id\",\"InvestmentPlatform.AccountStatistics.Positions.Profit\":\"Profit\",\"InvestmentPlatform.AccountStatistics.Positions.Size\":\"Size\",\"InvestmentPlatform.AccountStatistics.Positions.Swap\":\"Swap\",\"InvestmentPlatform.AccountStatistics.Positions.Symbol\":\"Symbol\",\"InvestmentPlatform.AccountStatistics.ProfitFactor.Loss\":\"Loss\",\"InvestmentPlatform.AccountStatistics.ProfitFactor.Profit\":\"Profit\",\"InvestmentPlatform.AccountStatistics.ProfitFactor.Title\":\"Profit Factor\",\"InvestmentPlatform.AccountStatistics.ReturnChartTooltip.Return\":\"Return, %\",\"InvestmentPlatform.AccountStatistics.Tabs.BalanceEquityChart\":\"Balance/Equity Chart\",\"InvestmentPlatform.AccountStatistics.Tabs.OpenedPositions\":\"Open Positions\",\"InvestmentPlatform.AccountStatistics.Tabs.Return\":\"Return, %\",\"InvestmentPlatform.AccountStatistics.Tabs.StrategyDescription\":\"Strategy Description\",\"InvestmentPlatform.AccountStatistics.Tabs.TradingHistory\":\"Trading History\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.AvgTradesPerWeek\":\"Avg. Trades per week\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.AvgTradeTime\":\"Avg. trade time (hours)\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.BuySellTrades\":\"Buy / Sell Trades\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.ConsecutiveLosses\":\"Consecutive Losses\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.ConsecutiveWins\":\"Consecutive Wins\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.EmptyChart.Subtitle\":\"instruments\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.EmptyChart.Title\":\"0\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.InstrumentsCount [count]\":\"{{count}}\\ninstruments\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.NumberOfTrades\":\"Number of trades\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.Other\":\"Others\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.ProfitableWeeks\":\"Profitable Weeks\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.Title\":\"Trading Statistics\",\"InvestmentPlatform.AccountStatistics.TradingStatistics.WinningLosingTrades\":\"Winning / Losing Trades\",\"InvestmentPlatform.AddInvestmentAccountWizard.LoginPasswordStep.AccountId.Placeholder\":\"Account ID\",\"InvestmentPlatform.AddInvestmentAccountWizard.LoginPasswordStep.CancelButtonLabel\":\"Cancel\",\"InvestmentPlatform.AddInvestmentAccountWizard.LoginPasswordStep.Password.Placeholder\":\"Password\",\"InvestmentPlatform.AddInvestmentAccountWizard.LoginPasswordStep.SubmitButtonLabel\":\"Proceed\",\"InvestmentPlatform.AddInvestmentAccountWizard.SummaryScreen.Title\":\"Congratulations\",\"InvestmentPlatform.AddInvestmentAccountWizard.Title [accountType]\":\"Create new {{accountType}} account\",\"InvestmentPlatform.AddMasterAccountWizard.Actions.Close\":\"Close\",\"InvestmentPlatform.AddMasterAccountWizard.Actions.Proceed\":\"Proceed\",\"InvestmentPlatform.AddMasterAccountWizard.Actions.Skip\":\"Skip\",\"InvestmentPlatform.AddMasterAccountWizard.CreateForm.Step.CancelButtonLabel\":\"Cancel\",\"InvestmentPlatform.AddMasterAccountWizard.CreateForm.Step.Description\":\"Select platform, currency and leverage\",\"InvestmentPlatform.AddMasterAccountWizard.CreateForm.Step.SubmitButtonLabel\":\"Proceed\",\"InvestmentPlatform.AddMasterAccountWizard.NameAndDescription.Step.Description\":\"Enter strategy name and strategy description\",\"InvestmentPlatform.AddMasterAccountWizard.NicknameAndAvatar.Step.Description\":\"Add your account picture and nickname\",\"InvestmentPlatform.AddMasterAccountWizard.Step [stepNumber]\":\"Step {{stepNumber}}\",\"InvestmentPlatform.AddMasterAccountWizard.Step1.AccountName.Placeholder\":\"Strategy name\",\"InvestmentPlatform.AddMasterAccountWizard.Step1.AccountName.Template [accountId]\":\"Master account #{{accountId}}\",\"InvestmentPlatform.AddMasterAccountWizard.Step1.StrategyDescription.Label\":\"Strategy Description (Optional)\",\"InvestmentPlatform.AddMasterAccountWizard.Step2.Avatar.Error\":\"The new avatar can’t be uploaded until the previous request is approved. You can proceed to the next step and upload the avatar later.\",\"InvestmentPlatform.AddMasterAccountWizard.Step2.Nickname.Template [userId]\":\"Client #{{userId}}\",\"InvestmentPlatform.AddMasterAccountWizard.Step3.Nickname.Placeholder\":\"Nickname\",\"InvestmentPlatform.AddMasterAccountWizard.Step3.Userpic.Add\":\"Add userpic (Optional)\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.Deposit.SomethingWentWrong\":\"Something went wrong\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.Deposit.SuccessfulTransfer\":\"Successful transfer\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.Deposit.SuccessfulTransferRequest\":\"Transfer request was successfully sent\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.DepositBlock.DepositAmount\":\"Deposit amount\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.DepositBlock.DepositFrom\":\"Deposit from\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.DepositBlock.Description [link]\":\"You can deposit funds to your Master account here<br/>\\nor on the <a href='{{link}}'>Deposit Page</a>\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.DepositBlock.Title\":\"Deposit funds to your account to start trading\",\"InvestmentPlatform.AddMasterAccountWizard.SummaryScreen.MasterAccountCreated\":\"Your Master account was successfully created!\",\"InvestmentPlatform.AllocationMethod.FixedLotAllocation\":\"Fixed Lot Allocation\",\"InvestmentPlatform.AllocationMethod.ProportionallyByBalance\":\"Proportionally to Balance\",\"InvestmentPlatform.AllocationMethod.ProportionallyByBalanceRatio\":\"Proportionally to Balance x Ratio\",\"InvestmentPlatform.AllocationMethod.ProportionallyByEquity\":\"Proportionally to Equity\",\"InvestmentPlatform.AllocationMethod.ProportionallyByEquityRatio\":\"Proportionally to Equity x Ratio\",\"InvestmentPlatform.AllocationMethod.RatioMultiplier\":\"Ratio Multiplier\",\"InvestmentPlatform.CheckSubscriptionCode.Input.Error\":\"Entered code is not valid\",\"InvestmentPlatform.CheckSubscriptionCode.Input.Label\":\"Subscription code\",\"InvestmentPlatform.ChooseAccountTypeDialog.Button.Continue\":\"Continue\",\"InvestmentPlatform.ChooseAccountTypeDialog.Option.Follower.Description\":\"Auto-Copy signals of professional traders\",\"InvestmentPlatform.ChooseAccountTypeDialog.Option.Follower.Name\":\"Investor\",\"InvestmentPlatform.ChooseAccountTypeDialog.Option.Master.Description\":\"Be a Trader, allow others to copy your positions and earn fees\",\"InvestmentPlatform.ChooseAccountTypeDialog.Option.Master.Name\":\"Signal Provider\",\"InvestmentPlatform.ChooseAccountTypeDialog.Title\":\"Who do you want to be?\",\"InvestmentPlatform.Common.AccountMiniChart.Label\":\"Account\",\"InvestmentPlatform.Common.ActivityWeek.MoreThan10Deals\":\"More than 10 deals\",\"InvestmentPlatform.Common.ActivityWeek.MoreThan30Deals\":\"More than 30 deals\",\"InvestmentPlatform.Common.ActivityWeek.MoreThan50Deals\":\"More than 50 deals\",\"InvestmentPlatform.Common.CheckSubscriptionCode.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.Common.CheckSubscriptionCode.Button.Proceed\":\"Proceed\",\"InvestmentPlatform.Common.CheckSubscriptionCode.Error.CodeIsIncorrect\":\"Code is incorrect\",\"InvestmentPlatform.Common.CheckSubscriptionCode.Error.ErrorCheckingCode\":\"An error occurred while checking the code\",\"InvestmentPlatform.Common.CheckSubscriptionCode.SubscriptionCode.ProvideCodeSubscribe\":\"To subscribe to this master account, you need to provide the subscription code\",\"InvestmentPlatform.Common.CheckSubscriptionCode.SubscriptionCode.ProvideCodeView\":\"To be able to view the page of this master account, you need to provide the subscription code\",\"InvestmentPlatform.Common.CheckSubscriptionCode.SubscriptionCode.SubTitle\":\"Process will take less than 1 minute\",\"InvestmentPlatform.Common.CheckSubscriptionCode.SubscriptionCode.Title\":\"Create New Subscription\",\"InvestmentPlatform.Common.FeeList.Error.FieldIsRequired\":\"Field is required\",\"InvestmentPlatform.Common.FeeList.ManagementFee.Description\":\"A percentage of net assets (account equity) that is paid in parts on a periodic basis.\",\"InvestmentPlatform.Common.FeeList.ManagementFee.Error.MaxValue [value]\":\"Max. management fee - {{value}}%\",\"InvestmentPlatform.Common.FeeList.ManagementFee.Subtitle\":\"(% from AUM)\",\"InvestmentPlatform.Common.FeeList.ManagementFee.Title\":\"Management Fee\",\"InvestmentPlatform.Common.FeeList.PerformanceFee.Description\":\"A percentage of net profit calculated based on the High-Water Mark model. It is charged at the end of a specified period.\",\"InvestmentPlatform.Common.FeeList.PerformanceFee.Error.MaxValue [value]\":\"Max. performance fee - {{value}}%\",\"InvestmentPlatform.Common.FeeList.PerformanceFee.Subtitle\":\"(% from net profit)\",\"InvestmentPlatform.Common.FeeList.PerformanceFee.Title\":\"Performance Fee\",\"InvestmentPlatform.Common.FeeList.ProfitFee.Description\":\"A percentage of profit earned only from profitable positions, calculated based on the High-Water Mark model. It is charged at the end of a specified period.\",\"InvestmentPlatform.Common.FeeList.ProfitFee.Error.MaxValue [value]\":\"Max. profit fee - {{value}}%\",\"InvestmentPlatform.Common.FeeList.ProfitFee.Subtitle\":\"(% from profit)\",\"InvestmentPlatform.Common.FeeList.ProfitFee.Title\":\"Profit Fee\",\"InvestmentPlatform.Common.FeeList.SubscriptionFee.Description\":\"A fixed amount paid on a periodic basis regardless of trading performance. It is charged at the beginning of a specified period.\",\"InvestmentPlatform.Common.FeeList.SubscriptionFee.Error.MaxValue [value]\":\"Max. subscription fee - {{value}}USD\",\"InvestmentPlatform.Common.FeeList.SubscriptionFee.Subtitle\":\"(USD / period)\",\"InvestmentPlatform.Common.FeeList.SubscriptionFee.Title\":\"Subscription Fee\",\"InvestmentPlatform.Common.FeeList.TradeFee.Description\":\"A fixed amount paid per 1 lot of traded volume. It is charged upon position closing.\",\"InvestmentPlatform.Common.FeeList.TradeFee.Error.MaxValue [value]\":\"Max. volume fee - {{value}}USD\",\"InvestmentPlatform.Common.FeeList.TradeFee.Subtitle\":\"(USD/lot)\",\"InvestmentPlatform.Common.FeeList.TradeFee.Title\":\"Volume Fee\",\"InvestmentPlatform.Common.PromoCode.InputComponent.ApplyButton\":\"Apply\",\"InvestmentPlatform.Common.PromoCode.InputComponent.DeleteButton\":\"Delete\",\"InvestmentPlatform.Common.PromoCode.InputComponent.PromoCodeOk\":\"Promo code has been applied\",\"InvestmentPlatform.Common.PromoCode.InputComponent.PromoCodeTitle\":\"Promo code\",\"InvestmentPlatform.Common.SubscribeButton.Label.Follow\":\"+ Follow\",\"InvestmentPlatform.Common.SubscribeButton.Label.ViewAccount\":\"View Account\",\"InvestmentPlatform.Common.SubscribeButton.Tooltip.UnavailableAccounts\":\"You have no accounts for subscription\",\"InvestmentPlatform.Common.SubscribeDialog.AccountLoadError\":\"An error occurred while getting account details\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.Button.Subscribe\":\"Subscribe\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.DialogSubTitle\":\"Process will take less than 1 minute\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.DialogTitle\":\"Create New Subscription\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.SectionTitle.Fees\":\"With subscription’s fees\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.SectionTitle.FollowerAccount\":\"Follower Account\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.SectionTitle.MasterAccount\":\"To a Master account\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.TermAndConditions\":\"By checking the box you acknowledge and agree to terms and conditions of using copy trading service\",\"InvestmentPlatform.Common.SubscribeDialog.Deposit.CancelButton.Label\":\"Cancel\",\"InvestmentPlatform.Common.SubscribeDialog.Deposit.CloseButton.Label\":\"Close\",\"InvestmentPlatform.Common.SubscribeDialog.Deposit.DepositAmount\":\"Deposit Amount\",\"InvestmentPlatform.Common.SubscribeDialog.Deposit.DepositFrom\":\"Deposit From\",\"InvestmentPlatform.Common.SubscribeDialog.Deposit.SubmitButton.Label\":\"Deposit now\",\"InvestmentPlatform.Common.SubscribeDialog.Error.WithoutPaymentAccountMinDepositError [amount]\":\"Your account balance is less than the minimum deposit required to subscribe to this master account. You account balance must be at least {{amount}} USD to meet the requirement and create the subscription \",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.CancelButton\":\"Go Back\",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.DepositBlock.Description [value]\":\"Your account balance is less the minimum deposit requirement set for this master account. Please deposit a minimum of {{value}} to your account.\",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.DepositBlock.Title\":\"Make a Deposit\",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.MinimumDepositRequirement\":\"Minimum deposit (USD) requirement  \",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.SubscribeFailMessage\":\"Not enough funds to subscribe to this master\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.DepositBlock.Description [link]\":\"You can deposit funds to your Investment account here or <br> on the <a href=\\\"{{link}}\\\">Deposit page</a>.\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.DepositBlock.Title\":\"Next Step: Deposit funds to your investment account\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.DepositError.IncorrectAccountId\":\"Incorrect Account Id\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.DialogTitle\":\"New subscription \\nwas successfully created\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.FollowerAccount\":\"Investment Account\",\"InvestmentPlatform.Common.SubscribeDialog.SuccessSubscription.MasterAccount\":\"Master Account\",\"InvestmentPlatform.ConfirmationDialog.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.ConfirmationDialog.Button.Proceed\":\"Proceed\",\"InvestmentPlatform.EFeePeriod.Daily\":\"Daily\",\"InvestmentPlatform.EFeePeriod.Monthly\":\"Monthly\",\"InvestmentPlatform.EFeePeriod.Quarter\":\"Quarterly\",\"InvestmentPlatform.EFeePeriod.Weekly\":\"Weekly\",\"InvestmentPlatform.FeesPlanDetails.CloseDetails\":\"Close\",\"InvestmentPlatform.FeesPlanDetails.Title\":\"Fee Plan\",\"InvestmentPlatform.Leaderboard.AUM\":\"AUM, USD\",\"InvestmentPlatform.Leaderboard.Details.Chart.DateFilter.AllTime\":\"All time\",\"InvestmentPlatform.Leaderboard.Details.Chart.DateFilter.Month\":\"Month\",\"InvestmentPlatform.Leaderboard.Details.Chart.DateFilter.SixMonth\":\"6 Month\",\"InvestmentPlatform.Leaderboard.Details.Chart.DateFilter.ThreeMonth\":\"3 Month\",\"InvestmentPlatform.Leaderboard.Details.Chart.DateFilter.Year\":\"Year\",\"InvestmentPlatform.Leaderboard.FilterButton.Label\":\"Filters\",\"InvestmentPlatform.Leaderboard.FilterButton.Title\":\"Filters\",\"InvestmentPlatform.Leaderboard.FilterDialog.FromToField.From\":\"From\",\"InvestmentPlatform.Leaderboard.FilterDialog.FromToField.To\":\"To\",\"InvestmentPlatform.Leaderboard.FilterDialog.ResetButton.Label\":\"Reset\",\"InvestmentPlatform.Leaderboard.FilterDialog.SubmitButton.Label\":\"Apply Filters\",\"InvestmentPlatform.Leaderboard.FilterDialog.Title\":\"Custom Filters\",\"InvestmentPlatform.Leaderboard.FilterLabel.Active\":\"Active\",\"InvestmentPlatform.Leaderboard.FilterLabel.Age\":\"Account Age (days)\",\"InvestmentPlatform.Leaderboard.FilterLabel.AtLeastOneDealInLastSevenDays\":\"At least 1 deal in last 7 days\",\"InvestmentPlatform.Leaderboard.FilterLabel.Equal\":\"equal\",\"InvestmentPlatform.Leaderboard.FilterLabel.Followers\":\"Followers\",\"InvestmentPlatform.Leaderboard.FilterLabel.NotLess\":\"not less\",\"InvestmentPlatform.Leaderboard.FilterLabel.NotMore\":\"not more\",\"InvestmentPlatform.Leaderboard.FilterLabel.ProfitAll\":\"Return (Total), %\",\"InvestmentPlatform.Leaderboard.FilterLabel.ProfitMonth\":\"Return (30 days), %\",\"InvestmentPlatform.Leaderboard.FilterLabel.ProfitWeek\":\"Return (7 days), %\",\"InvestmentPlatform.Leaderboard.FilterLabel.Risk\":\"Risk\",\"InvestmentPlatform.Leaderboard.FilterLabel.SelectRiskLevel\":\"Select risk type(s)\",\"InvestmentPlatform.Leaderboard.FindAccountByIdDialog.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.Leaderboard.FindAccountByIdDialog.Button.Proceed\":\"Proceed\",\"InvestmentPlatform.Leaderboard.FindAccountByIdDialog.Input.Placeholder\":\"Account number\",\"InvestmentPlatform.Leaderboard.FindAccountByIdDialog.Title\":\"Find by number\",\"InvestmentPlatform.Leaderboard.FindAccountByIdLink.Text\":\"Find by number\",\"InvestmentPlatform.Leaderboard.Followers\":\"Followers\",\"InvestmentPlatform.Leaderboard.Profit7d\":\"Return (7d)\",\"InvestmentPlatform.Leaderboard.ProfitAll\":\"Return (Total)\",\"InvestmentPlatform.Leaderboard.RiskLevel.High\":\"High\",\"InvestmentPlatform.Leaderboard.RiskLevel.Low\":\"Low\",\"InvestmentPlatform.Leaderboard.RiskLevel.Medium\":\"Medium\",\"InvestmentPlatform.Leaderboard.Score\":\"Score\",\"InvestmentPlatform.Leaderboard.ShowMoreButton.Label\":\"Show More\",\"InvestmentPlatform.Leaderboard.Sort.AumAsc\":\"AUM ↑\",\"InvestmentPlatform.Leaderboard.Sort.AumDesc\":\"AUM ↓\",\"InvestmentPlatform.Leaderboard.Sort.FollowersAsc\":\"Followers ↑\",\"InvestmentPlatform.Leaderboard.Sort.FollowersDesc\":\"Followers ↓\",\"InvestmentPlatform.Leaderboard.Sort.ProfitAllAsc\":\"Return  (Total) ↑\",\"InvestmentPlatform.Leaderboard.Sort.ProfitAllDesc\":\"Return (Total) ↓\",\"InvestmentPlatform.Leaderboard.Sort.ProfitWeekAsc\":\"Return (7 days) ↑\",\"InvestmentPlatform.Leaderboard.Sort.ProfitWeekDesc\":\"Return (7 days) ↓\",\"InvestmentPlatform.Leaderboard.Sort.ScoreAsc\":\"Score ↑\",\"InvestmentPlatform.Leaderboard.Sort.ScoreDesc\":\"Score ↓\",\"InvestmentPlatform.Leaderboard.SortTraders\":\"Sort By\",\"InvestmentPlatform.Leaderboard.TraderAccount.Empty\":\"No available accounts to display\",\"InvestmentPlatform.Leaderboard.TraderAccountView.AlreadySubscribed\":\"Already Subscribed\",\"InvestmentPlatform.Leaderboard.TraderAccountView.MinDeposit [value]\":\"Min. deposit {{value}}\",\"InvestmentPlatform.Positions.ConfirmationDialog.DetachPosition.Description\":\"Are you sure you want to detach position? After detaching a position from a master’s position it will not be closed\",\"InvestmentPlatform.Positions.ConfirmationDialog.DetachPosition.Title\":\"Detach confirmation\",\"InvestmentPlatform.Positions.DetachPosition.Success\":\"Position was successfully detached\",\"InvestmentPlatform.SubscriptionStatus.Active\":\"Active\",\"InvestmentPlatform.SubscriptionStatus.Pause\":\"Paused\",\"InvestmentPlatform.SubscriptionStatus.Unsubscribed\":\"Unsubscribed\",\"InvestmentPlatform.TransactionRequestStatus.Canceled\":\"Canceled\",\"InvestmentPlatform.TransactionRequestStatus.Cancelled\":\"Cancelled\",\"InvestmentPlatform.TransactionRequestStatus.Executed\":\"Executed\",\"InvestmentPlatform.TransactionRequestStatus.Fail\":\"Failed\",\"InvestmentPlatform.TransactionRequestStatus.FeesFinished\":\"Fees Finished\",\"InvestmentPlatform.TransactionRequestStatus.FeesInProgress\":\"Fees in Progress\",\"InvestmentPlatform.TransactionRequestStatus.InProgress\":\"In Progress\",\"InvestmentPlatform.TransactionRequestStatus.InQueue\":\"In Queue\",\"InvestmentPlatform.TransactionRequestStatus.InsufficientFunds\":\"Insufficient Funds\",\"InvestmentPlatform.TransactionRequestStatus.Partial\":\"Partial\",\"InvestmentPlatform.TransactionRequestStatus.PartialWithdrawal\":\"Partial\",\"InvestmentPlatform.TransactionRequestStatus.Pending\":\"Pending\",\"InvestmentPlatform.TransactionRequestStatus.Processing\":\"Processing\",\"InvestmentPlatform.TransactionRequestStatus.Ready\":\"Ready\",\"InvestmentPlatform.TransactionRequestStatus.Rejected\":\"Rejected\",\"InvestmentPlatform.TransactionRequestStatus.Success\":\"Success\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.Button.ChangeAmount\":\"Change a withdrawal amount\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.Button.Proceed\":\"Proceed and unsubscribe\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.Table.MasterAccHeader\":\"Master account\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.Table.MinDepositHeader\":\"Min deposit requirement\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.TableLabel\":\"If you withdraw the requested amount, you'll be unsubscribed from the following master accounts:\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.Title\":\"Choose the next action\",\"InvestmentPlatform.UnsubscribeConfirmationDialog.UserSubscriptionsLabel [count]\":\"You have {{ count }} subscription(s) that don't meet the minimum deposit requirements.\",\"Mam.AccountDetails.Title\":\"Account Details\",\"Mam.AccountList.AccountGroupTitle.Follower\":\"Investment\",\"Mam.AccountList.AccountGroupTitle.Master\":\"Master\",\"Mam.AccountList.DefaultPageTitle\":\"My Accounts\",\"Mam.AccountList.Details.Title.FeePayment\":\"Fee Payments\",\"Mam.AccountList.Details.Title.Settings\":\"Settings\",\"Mam.AccountList.Details.Title.Statistics\":\"Statistics\",\"Mam.AccountList.Details.Title.Subscriptions\":\"Subscriptions\",\"Mam.AccountList.Details.Title.TradingReport\":\"Trading Report\",\"Mam.Leaderboard.Title\":\"Leaderboard\",\"Mam.Leaderboard.TraderDetails.Title\":\"Account Statistics\",\"Metatrader.AccountCard.AccountInfo.Followers\":\"Followers\",\"Metatrader.AccountCard.AccountInfo.MaxDrawdown\":\"Max DD\",\"Metatrader.AccountCard.AccountInfo.Profit\":\"Profit\",\"Metatrader.AccountCard.AccountInfo.Status\":\"Status\",\"Metatrader.AccountCard.AccountInfo.Status.Subscribed\":\"Subscribed\",\"Metatrader.AccountCard.AccountInfo.Status.Unsubscribed\":\"Unsubscribed\",\"Metatrader.AccountCard.Balance\":\"Balance\",\"Metatrader.AccountCard.BalanceUpdating\":\"Updating ...\",\"Metatrader.AccountCard.DepositButton.Label\":\"Transfer\",\"Metatrader.AccountCard.FavoriteButton.Tooltip.Add\":\"Add to favorite\",\"Metatrader.AccountCard.FavoriteButton.Tooltip.Remove\":\"Remove from favorite\",\"Metatrader.AccountCard.RefreshButton.Tooltip\":\"Refresh\",\"Metatrader.AccountList.CreateAccountForm.FieldLabel.Currency\":\"Currency\",\"Metatrader.AccountList.CreateAccountForm.FieldLabel.Hedging\":\"Hedging\",\"Metatrader.AccountList.CreateAccountForm.FieldLabel.Leverage\":\"Leverage\",\"Metatrader.AccountList.CreateAccountForm.FieldLabel.Platform\":\"Platform\",\"Metatrader.AccountList.CreateAccountForm.FieldLabel.StartAmount\":\"Start Amount\",\"Metatrader.AccountList.ProductDetailsDialog.CloseDetails\":\"Close\",\"Metatrader.AccountList.ProductDetailsDialog.NoDetails.Title\":\"There is no description\",\"Metatrader.AccountList.ProductDetailsDialog.Title\":\"Details\",\"Metatrader.CreateAccount.ErrorMessage.LimitExceeded\":\"Account Limit Exceeded\",\"Metatrader.CreateAccount.ErrorMessage.PreviousRequestIsNotApproved\":\"Previous Request is Still not Approved\",\"Metatrader.CreateAccount.ErrorMessage.SomethingWentWrong\":\"Something went wrong\",\"Metatrader.CreateAccountCard.WarningMessage.AccountOpeningLimitReached\":\"Account opening limit reached\",\"Metatrader.DetailsPage.AccountSuccessfullyRenamed\":\"Account Successfully Renamed\",\"Metatrader.DetailsPage.Settings.AccountNamePlaceholder\":\"Account name\",\"Metatrader.DetailsPage.Settings.ChangeAccountName\":\"Change account name\",\"Metatrader.DetailsPage.Settings.ChangeName\":\"Change name\",\"Metatrader.DetailsPage.Settings.ChangeStrategyName\":\"Change strategy name\",\"Metatrader.DetailsPage.Settings.EnterNewAccountName\":\"Enter new account name\",\"Metatrader.DetailsPage.Settings.EnterNewStrategyName\":\"Enter new strategy name\",\"Metatrader.DetailsPage.Settings.Password\":\"Password\",\"Metatrader.DetailsPage.Settings.PasswordResetInstructionsWereSentToYourEmail\":\"Password reset instructions were sent to your email\",\"Metatrader.DetailsPage.Settings.ResetPassword\":\"Reset password\",\"Metatrader.DetailsPage.Settings.ResetPassword.GoBack\":\"Go back\",\"Metatrader.DetailsPage.Settings.StrategyNamePlaceholder\":\"Strategy name\",\"PageTitle.B2Copy.AccountList\":\"My Accounts\",\"PageTitle.B2Copy.AccountList.AccountDetails\":\"Copy Trading • Account Details\",\"PageTitle.B2Copy.Leaderboard\":\"Copy Trading • Leaderboard\",\"PageTitle.B2Copy.Leaderboard.AccountDetails\":\"Copy Trading • Account Statistics\",\"PageTitle.Mam.AccountList\":\"Mam • My Accounts\",\"PageTitle.Mam.AccountList.AccountDetails\":\"Mam • Account Details\",\"PageTitle.Mam.Leaderboard\":\"Mam • Leaderboard\",\"PageTitle.Mam.Leaderboard.AccountDetails\":\"Mam • Account Statistics\",\"PageTitle.Pamm.AccountList\":\"PAMM • My Accounts\",\"PageTitle.Pamm.AccountList.AccountDetails\":\"PAMM • Account Details\",\"PageTitle.Pamm.Leaderboard\":\"PAMM • Leaderboard\",\"PageTitle.Pamm.Leaderboard.AccountDetails\":\"PAMM • Account Statistics\",\"Pamm.AccountDetails.Title\":\"Account Details\",\"Pamm.AccountList.AccountCard.Follower.DisabledDepositButton.Tooltip\":\"You can make a deposit only to investment accounts which are subscribed to master account\",\"Pamm.AccountList.AccountGroupDescription.Follower.MT4\":\"Investment accounts MT4\",\"Pamm.AccountList.AccountGroupDescription.Master.MT4\":\"Master accounts MT4\",\"Pamm.AccountList.AccountGroupTitle.Follower\":\"Investment\",\"Pamm.AccountList.AccountGroupTitle.Master\":\"Master\",\"Pamm.AccountList.DefaultPageTitle\":\"My Accounts\",\"Pamm.AccountList.Details.Title.FeePayment\":\"Fee Payment\",\"Pamm.AccountList.Details.Title.Requests\":\"Requests\",\"Pamm.AccountList.Details.Title.Settings\":\"Settings\",\"Pamm.AccountList.Details.Title.Statistics\":\"Statistic\",\"Pamm.AccountList.Details.Title.Subscriptions\":\"Subscriptions\",\"Pamm.AccountList.Details.Title.TradingReport\":\"Trading Report\",\"Pamm.Leaderboard.Title\":\"Leaderboard\",\"Pamm.Leaderboard.TraderDetails.Title\":\"Account Statistics\",\"Transfer.Error.PammTransferError\":\"Can't get PAMM master for PAMM investor\",\"Transfer.Error.PammTransferError.ForbiddenAccounts\":\"Pamm transfer is forbidden for these accounts\",\"B2Copy.CopyTrading.Title\":\"CopyTrading\",\"B2Copy.CopyTrading.WalletAccount.Balance [alpha]\":\"Balance {{alpha}}\",\"B2Copy.CopyTrading.WalletAccount.Equity [alpha]\":\"Equity {{alpha}}\",\"B2Copy.CopyTrading.WalletAccount.Title\":\"Wallet Account\",\"B2Copy.Leaderboard.TraderDetails.RealizedPlUsd\":\"Realized PL, USD\",\"InvestmentPlatform.AccountDetails.Overview.FreeMargin\":\"Free Margin\",\"InvestmentPlatform.AccountDetails.Overview.NextFees\":\"Future Fees\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.ButtonChangeCode\":\"Change code\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.CopyPassword.Hint\":\"Copy password\",\"InvestmentPlatform.AccountDetails.Settings.SubscriptionCode.CopyPassword.Message\":\"Password has been successfully copied\",\"InvestmentPlatform.AccountDetails.Subscriptions.NextFees\":\"Next Fees\",\"InvestmentPlatform.AccountDetails.Subscriptions.SuccessfullyResubscribed\":\"Successfully Resubscribed\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.NextFees\":\"Future Fees\",\"InvestmentPlatform.AccountStatistics.Overview.Equity\":\"Equity, USD\",\"InvestmentPlatform.AccountStatistics.Overview.Return [currency]\":\"Return, {{currency}}\",\"InvestmentPlatform.AccountStatistics.Tabs.Return [currency]\":\"Return, {{currency}}\",\"InvestmentPlatform.AllocationMethod.ProportionallyByFreeMargin\":\"Proportionally to Free Margin\",\"InvestmentPlatform.CheckSubscriptionCode.InputLabel.SubscriptionCode\":\"Subscription Code\",\"InvestmentPlatform.Common.SubscribeDialog.CreateNewSubscription.NewAccountCreatedSuccessMessage\":\"New investment account was successfully created\",\"InvestmentPlatform.Common.SubscribeDialog.MakeDepositScreen.SubmitButton\":\"Subscribe to Master Account\",\"InvestmentPlatform.SubscriptionCodeDialog.Button.Cancel\":\"Cancel\",\"InvestmentPlatform.SubscriptionCodeDialog.Button.Proceed\":\"Proceed\",\"InvestmentPlatform.SubscriptionCodeDialog.Error.AccountNotFound\":\"Account not found\",\"InvestmentPlatform.SubscriptionCodeDialog.Error.CodeIsIncorrect\":\"Code is incorrect\",\"InvestmentPlatform.SubscriptionCodeDialog.InputLabel.SubscriptionCode\":\"Subscription code\",\"InvestmentPlatform.SubscriptionCodeDialog.ProvideCode\":\"To be able to view the page for this master account, you need to provide the subscription code\",\"InvestmentPlatform.SubscriptionCodeDialog.Title\":\"Enter code\",\"Mam.AccountList.Title\":\"My Accounts\",\"Metatrader\":\"Metak\",\"Metatrader.AccountCard.WarningMessage.ServiceIsUnavailable\":\"Service is currently unavailable\",\"PageTitle.B2Copy\":\"B2Copy_test\",\"Pamm.AccountList.Title\":\"My Accounts\",\"errors.requiredif\":\"Field is required\",\"errors.same as {{label}}\":\"It is the same as {{label}}\",\"errors.sameGroup\":\"The same group\",\"errors.string\":\"Value must be string\",\"errors.url\":\"Invalid url\",\"exchange.Exceeds_Daily_Limit\":\"Exceeds Daily Limit\",\"exchange.Market_Closed\":\"Market Closed\",\"exchange.Not_Enough_Funds\":\"Not enough funds\",\"from Google Authenticator app\":\"from <span>Google Authenticator app</span>\",\"hours\":\"hours\",\"24h Low\":\"24 Hour Low\",\"24h Volume\":\"24 Hour Volume\",\"2FA instruction link address\":\"2FA instruction link address\",\"AUTO\":\"Auto\",\"Account\":\"Account\",\"Account ID\":\"Account ID\",\"Account archived\":\"Account archived\",\"Account details\":\"Account details\",\"Account name\":\"Account name\",\"Account not found\":\"Account not found\",\"Account opening limit reached\":\"Account opening limit reached\",\"Account renamed successfully\":\"Account renamed successfully\",\"Account settings\":\"Account settings\",\"Account successfully created\":\"Account successfully created\",\"Account type\":\"Account type\",\"Account type caption\":\"{{account_type}} account\",\"Account(s) has no deals\":\"Account(s) has no deals\",\"Accounts limit exceeded\":\"Accounts limit exceeded\",\"Activate dark theme\":\"Activate dark theme\",\"Activate light theme\":\"Activate light theme\",\"Add +\":\"Add +\",\"Add Account\":\"Add Account\",\"Add favorite\":\"Add favorite\",\"Add instrument\":\"Add instrument\",\"Add ticket\":\"Add ticket\",\"Add widget\":\"Add widget\",\"Address\":\"Address\",\"Advanced\":\"Advanced\",\"All\":\"All\",\"All deals\":\"All deals\",\"All time\":\"All time\",\"Already a member?\":\"Already a member?\",\"Already claimed\":\"Already claimed\\t\",\"Amount\":\"Amount\",\"Amount (From)\":\"Amount (From)\",\"Amount (To)\":\"Amount (To)\",\"Amount (from)\":\"Amount (from)\",\"Amount (to)\":\"Amount (to)\",\"Amount to pay\":\"Amount to pay\",\"Analytics\":\"Analytics\",\"Android\":\"Android\",\"Android.dxtrade\":\"Android dxtrade\",\"Android.match_trader\":\"Android\",\"Android.mt4\":\"Android.mt4\",\"Android.mt5\":\"Android.mt5\",\"Announcement.Button.Title\":\"Announcement\",\"Announcements\":\"Announcements\",\"ApiKey.ApiKeyTitle\":\"API Key Management\",\"ApiKey.Delete\":\"Delete\",\"ApiKey.GenerateNewKey\":\"Generate New Key\",\"ApiKey.ImportantMessageSavePrivateKey\":\"Save you Private Key\",\"ApiKey.Message.SuccessDeleteApiKey\":\"Successfully Deleted\",\"ApiKey.Modal.DeleteApiKey.CancelButton\":\"Cancel\",\"ApiKey.Modal.DeleteApiKey.Delete\":\"Delete API Key\",\"ApiKey.Modal.DeleteApiKey.Description\":\"API Key Description\",\"ApiKey.Modal.DeleteApiKey.Title\":\"Delete API Key\",\"ApiKey.NoApiKeyDescription\":\"No API Key Generated Yet\",\"ApiKey.PageTitle\":\"API Key\",\"ApiKey.PrivateApiKeyTitle\":\"Private API Key\",\"ApiKey.PrivateApiKeyTitleCopied\":\"Private API Key Copied\",\"ApiKey.PublicApiKeyTitle\":\"Public API Key\",\"ApiKey.PublicApiKeyTitleCopied\":\"Public API Key Copied\",\"ApiKey.TapToCopy\":\"Tap to Copy\",\"Api_Key_Management\":\"API Key Management\",\"Apologies, registration is temporarily closed\":\"registration is closed\",\"Archive\":\"Archive\",\"Archive account\":\"Archive account\",\"Archive account (description)\":\"Exhausted your account limit? Have old accounts that are not in use any longer? Just \\\"Archive\\\" these accounts to use another account.\",\"Are you sure all the details are correct?\":\"Are you sure all the details are correct?\",\"Are you sure you want to cancel order?\":\"Are you sure you want to cancel order?\",\"Are you sure you want to remove layout?\":\"Delete this workspace?\",\"Are you sure you want to reset spaces?\":\"Reset to default layout?\",\"Ask\":\"Ask\",\"Ask a Question\":\"Create a Ticket\",\"At the moment, no personal data can be displayed\":\"At the moment, no personal data can be displayed\",\"Attach files\":\"Attach files\",\"Auth.Components.EnterPhoneCode.Button.Cancel\":\"Cancel\",\"Auth.Components.EnterPhoneCode.Button.Send\":\"Send\",\"Auth.Components.EnterPhoneCode.Description [phoneNumber]\":\"Description {{phoneNumber}}\",\"Auth.Components.EnterPhoneCode.Title\":\"EnterPhoneCode Title\",\"Auth.Login.Wizard.Loading.CardContent.Error.SubTitle\":\"Something was wrong. Please try again\",\"Auth.Login.Wizard.Loading.CardContent.Subtitle\":\"Auth.Login.Wizard.Loading.CardContent.Subtitle\",\"Auth.Login.Wizard.Loading.CardContent.Title\":\"Welcome to a Platform\",\"Auth.Login.Wizard.Loading.Error.Title\":\"Oops...\",\"Auth.Login.Wizard.Loading.RetryButton.Label\":\"Try again\",\"Auth.Login.Wizard.Loading.StepLabel.CheckingNewTraders\":\"Checking new traders...\",\"Auth.Login.Wizard.Loading.StepLabel.GettingInsightsFromDatabase\":\"Getting insights from database...\",\"Auth.Login.Wizard.Loading.StepLabel.RefreshingCharts\":\"Refreshing charts...\",\"Auth.Login.Wizard.Loading.StepLabel.UpdatingStatistics\":\"Updating statistics...\",\"Auth.Login.Wizard.View.MobileLoginViaQr\":\"https://apps.apple.com/ru/app/b2broker/id1530612666\",\"Auth.Register.ComeBackButton\":\"Come Back\",\"Auth.Register.Expired\":\"Expired\",\"Auth.Register.Failed\":\"Register failed\",\"Auth.Register.Successful\":\"Register successful\",\"Auth.Register.Wizards.WSelectionType.Button.Continue\":\"Continue\",\"Auth.Register.Wizards.WSelectionType.ChooseRegistrationType\":\"Choose Registration Type\",\"Available\":\"Available\",\"B2Margin.Widgets.Favorites.Title\":\"Favorites\",\"B2Margin.Widgets.MarketDepth.Title\":\"Market Depth\",\"B2Margin.Widgets.OpenOrders.Title\":\"Open Orders\",\"B2Margin.Widgets.OrderBook.Title\":\"Order Book\",\"B2Margin.Widgets.OrderHistory.Title\":\"Order History\",\"B2Margin.Widgets.PlaceOrderClassic.Title\":\"Place Order Classic\",\"B2Margin.Widgets.PlaceOrderMaxi.Title\":\"Place Order Maxi\",\"B2Margin.Widgets.Positions.Title\":\"Positions\",\"B2Margin.Widgets.PriceChart.Title\":\"Price Chart\",\"B2Margin.Widgets.SymbolLibrary.Title\":\"Symbol Library\",\"B2Margin.Widgets.Trades.Title\":\"Trades\",\"B2Margin.Widgets.TradesHistory.Title\":\"Trades History\",\"B2Margin.Widgets.TradingView.Title\":\"Price Chart\",\"B2Margin.Widgets.WatchList.Title\":\"Watchlist\",\"B2MarginStatementDialog.B2MarginStatementFormats.HTML\":\"HTML\",\"B2MarginStatementDialog.B2MarginStatementFormats.PDF\":\"PDF\",\"B2MarginStatementDialog.Button.Cancel\":\"Cancel\",\"B2MarginStatementDialog.Button.Custom\":\"Custom\",\"B2MarginStatementDialog.Button.Generate\":\"Get statement\",\"B2MarginStatementDialog.DateFromPlaceholder\":\"from\",\"B2MarginStatementDialog.DateToPlaceholder\":\"to\",\"B2MarginStatementDialog.FormatPlaceholder\":\"Format\",\"B2MarginStatementDialog.Title\":\"Account Statement\",\"B2Trader.Group.Exchange.Title\":\"Exchange\",\"B2Trader.Group.Orders.Title\":\"Orders\",\"B2Trader.Group.TradingInfo.Title\":\"TradingInfo\",\"B2Trader.Widgets.Assets.Title\":\"Assets\",\"B2Trader.Widgets.ExchangeTime.Title\":\"Exchange Time\",\"B2Trader.Widgets.FavoriteMarkets.Title\":\"Favorite Markets\",\"B2Trader.Widgets.FilledOrders.Title\":\"Filled Orders\",\"B2Trader.Widgets.InactiveOrders.Title\":\"Inactive Order\",\"B2Trader.Widgets.Limit.Title\":\"Limit\",\"B2Trader.Widgets.Market.Title\":\"Market\",\"B2Trader.Widgets.MarketDepth.Title\":\"Market Depth\",\"B2Trader.Widgets.MyAccounts.Title\":\"My accounts\",\"B2Trader.Widgets.OpenOrders.Title\":\"Open Orders\",\"B2Trader.Widgets.OrderBook\":\"Order Book\",\"B2Trader.Widgets.OrderBook.Title\":\"Order Book\",\"B2Trader.Widgets.OrderHistory.Title\":\"Order History\",\"B2Trader.Widgets.QuickLimit.Title\":\"Quick Limit Order\",\"B2Trader.Widgets.QuickMarket.Title\":\"Quick Market Order\",\"B2Trader.Widgets.SimpleExchange.Title\":\"SimpleExchange\",\"B2Trader.Widgets.StopLimit.Title\":\"Stop Limit\",\"B2Trader.Widgets.StopMarket.Title\":\"Stop Market\",\"B2Trader.Widgets.StopOrders.Title\":\"Stop Orders\",\"B2Trader.Widgets.TradesHistory.Title\":\"Trades History\",\"B2Trader.Widgets.TradingView.Title\":\"TradingView\",\"B2Trader.Widgets.WatchList.Title\":\"WatchList\",\"B2trader.Widgets.InactiveOrders\":\"Inactive Orders\",\"B2trader.Widgets.InactiveOrders.Title\":\"Inactive Orders\",\"BShared.Modules.ToggleView.Title.Table\":\"Table\",\"Back\":\"Back\",\"Balance\":\"Balance\",\"Bank\":\"Bank\",\"Bank Account Number\":\"Bank Account Number\",\"Bank Address\":\"Bank Address\",\"Bank IBAN\":\"Bank IBAN\",\"Bank Name\":\"Bank Name\",\"Bank Swift\":\"Bank Swift\",\"Bank account name\":\"Account name\",\"Bank branch\":\"Bank branch\",\"Bank branch address\":\"Bank branch address\",\"Bank city\":\"Bank city\",\"Bank iban\":\"Bank IBAN\",\"Bank province\":\"Bank province\",\"Banners\":\"Banners\",\"Beneficiary Address\":\"Beneficiary Address\",\"Beneficiary Bank SWIFT\":\"Beneficiary Bank SWIFT\",\"Beneficiary Bank address\":\"Beneficiary Bank address\",\"Beneficiary Bank name\":\"Beneficiary Bank name\",\"Beneficiary IBAN\":\"Beneficiary IBAN\",\"Beneficiary Name\":\"Beneficiary Name\",\"Bid\":\"Bid\",\"Birth date\":\"Birth date\",\"Birthday\":\"Birthday\",\"Bonus Amount\":\"Bonus Amount\\t\",\"Bonus successfully claimed\":\"Bonus successfully claimed\\t\",\"Bonus, %\":\"Bonus, %\",\"Briefcase.BriefcaseInfo.Amount\":\"Amount\",\"Briefcase.BriefcaseInfo.Asset\":\"Asset\",\"Briefcase.BriefcaseInfo.AssetsTitle\":\"Assets Title\",\"Briefcase.BriefcaseInfo.AvgPrice\":\"Average Price\",\"Briefcase.BriefcaseInfo.Bond\":\"Bond\",\"Briefcase.BriefcaseInfo.Currency\":\"Currency\",\"Briefcase.BriefcaseInfo.CurrentPrice\":\"Current Price\",\"Briefcase.BriefcaseInfo.NoAssets\":\"No Assets\",\"Briefcase.BriefcaseInfo.Notification\":\"\",\"Briefcase.BriefcaseInfo.PositionChanges\":\"Position Changes\",\"Briefcase.BriefcaseInfo.Qty\":\"Quantity\",\"Briefcase.BriefcaseInfo.Range.AllTime\":\"All Time\",\"Briefcase.BriefcaseInfo.Range.Today\":\"Today\",\"Briefcase.BriefcaseInfo.Stock\":\"Stock\",\"Briefcase.BriefcaseInfo.TotalPrice\":\"Total Price\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AccountType.Bo\":\"Bo\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AccountType.BoHint\":\"BoHint\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccount.AccountType.IisBo\":\"IisBo\",\"Buy\":\"Buy\",\"Buy / Long\":\"Buy / Long\",\"Buy limit\":\"Buy limit\",\"Buy stop\":\"Buy stop\",\"CANCELED\":\"Canceled\",\"CANCELING\":\"Canceling\",\"CLOSING\":\"Close\",\"COMPLETED\":\"Completed\",\"CVC\":\"CVC\",\"CVC2, CVV2 or CID\":\"CVC2, CVV2 or CID\",\"Cancel\":\"Cancel\",\"Cancel request\":\"Cancel request\",\"Cancelled\":\"Cancelled\",\"Captcha is invalid. Try again\":\"Captcha is invalid. Try again\",\"Card Bank\":\"Bank Name\",\"Card number\":\"Card number\",\"Cardholder's name\":\"Cardholder's name\",\"Cardholders's name\":\"Cardholders's Name\",\"Certificate number\":\"Certificate Number\",\"Change\":\"Change\",\"Change account name\":\"Change account name\",\"Change name\":\"Change name\",\"City\":\"City\",\"Claim\":\"Claim\",\"Claimed\":\"Claimed\\t\",\"Clear all\":\"Clear all\",\"Close\":\"Close\",\"Close Details\":\"Close Details\",\"Close position\":\"Close position\",\"Close price\":\"Close price\",\"Close time\":\"Close time\",\"Close widget\":\"Close widget\\t\",\"Code\":\"Code\",\"Code copied\":\"Code copied\",\"CodeResend.Label.HaveNotReceivedCode\":\"Have Not Received Code\",\"CodeResend.Label.RequestsWillBeAbleAfter\":\"Requests Will Be Able After\",\"CodeResend.Label.Resend\":\"Resend\",\"CodeResend.Label.ResendIn\":\"Resend In\",\"CodeResend.Label.TooManyRequests\":\"Too Many Requests\",\"Come back soon! ⏳\":\"Come back soon! ⏳\",\"Comment\":\"Comment\",\"Commission\":\"Commission\",\"Common.Widgets.Account.Title\":\"Account\",\"Common.Widgets.AccountInfo.Title\":\"Account Info\",\"Common.Widgets.AccountInstruments.Title\":\"Account Instruments\",\"Common.Widgets.AcquisitionReport.Title\":\"Acquisition Report\",\"Common.Widgets.Assets.Title\":\"Assets\",\"Common.Widgets.AverageTraderLifetime.Title\":\"Average Trader LifeTime\",\"Common.Widgets.Banners.Title\":\"Banners\",\"Common.Widgets.Depth.Title\":\"Depth\",\"Common.Widgets.ExchangeTime.Title\":\"ExchangeTime\",\"Common.Widgets.Favorites.Title\":\"Favorites\",\"Common.Widgets.FilledOrders.Title\":\"Filled Orders\",\"Common.Widgets.InactiveOrders.Title\":\"Inactive Orders\",\"Common.Widgets.LeadConversionRatio.Title\":\"Lead Convertion Ratio\",\"Common.Widgets.Limit.Title\":\"Limit\",\"Common.Widgets.Market.Title\":\"Market\",\"Common.Widgets.OpenOrders.Title\":\"Open Orders\",\"Common.Widgets.OrderBook.Title\":\"Order Book\",\"Common.Widgets.OrderHistory.Title\":\"Order history\",\"Common.Widgets.Orders.Title\":\"Title\",\"Common.Widgets.PartnerLink.Title\":\"Your Partner Link\",\"Common.Widgets.PaymentAccounts.Title\":\"Payment Accounts\",\"Common.Widgets.PerformanceIndicators.Title\":\"Performance Indicators\",\"Common.Widgets.PlaceOrderClassic.Title\":\"Place Order Classic\",\"Common.Widgets.PlaceOrderMaxi.Title\":\"Place Order Maxi\",\"Common.Widgets.PlaceOrderSpotClassic.Title\":\"PlaceOrderSpotClassic\",\"Common.Widgets.Portfolio.Title\":\"Portfolio\",\"Common.Widgets.Positions.Title\":\"Positions\",\"Common.Widgets.QuickLimit.Title\":\"Quick Limit Order\",\"Common.Widgets.QuickLinks.Title\":\"Quick Links\",\"Common.Widgets.QuickMarket.Title\":\"Quick Market Order\",\"Common.Widgets.RecentTransactions.Title\":\"Recent Transactions\",\"Common.Widgets.SimpleExchange.Title\":\"SimpleExchange\",\"Common.Widgets.StopLimit.Title\":\"Stop Limit\",\"Common.Widgets.StopMarket.Title\":\"Stop Market\",\"Common.Widgets.StopOrders.Title\":\"Stop Orders\",\"Common.Widgets.Support.Title\":\"Support\",\"Common.Widgets.SymbolLibrary.Title\":\"Symbol Library\",\"Common.Widgets.TickerWidget.Title\":\"Ticker Widget\",\"Common.Widgets.TotalBalance.Title\":\"Wallets Overview\",\"Common.Widgets.TradersInfo.Title\":\"Traders Info\",\"Common.Widgets.Trades.Title\":\"Tradess\",\"Common.Widgets.TradesHistory.Title\":\"Trades History\",\"Common.Widgets.TradingAccounts.Title\":\"Trading Accounts\",\"Common.Widgets.TradingAccountsFavorites.Title\":\"Trading Account Favorites\",\"Common.Widgets.TradingReport.Title\":\"Trading Report\",\"Common.Widgets.TradingView.Title\":\"Trading Views\",\"Common.Widgets.TrafficSource.Title\":\"Traffic Source\",\"Common.Widgets.Verification.Title\":\"Verification\",\"Common.Widgets.Wallet.Title\":\"Wallet\",\"Common.Widgets.WatchList.Title\":\"Watch List\",\"Common.Widgets.Welcome.Description\":\"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\",\"Common.Widgets.Welcome.Title\":\"Welcome\",\"Communication language\":\"Communication language\",\"Complete KYC\":\"Complete KYC\",\"Completed\":\"Completed\",\"Confirm.No\":\"No\",\"Confirm.Yes\":\"Yes\",\"Confirmation\":\"Confirmation\",\"Confirmation code sent to your phone number: {phone_number}\":\"Confirmation code sent to your phone number: {{phone_number}}\",\"Confirmation code was sent to your email\":\"Confirmation code was sent to your email\",\"Congratulations!\":\"Congratulations!\",\"Contact us at\":\"Contact us at <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a>\",\"Continue\":\"Continue\",\"Copy to clipboard\":\"Copy to clipboard\",\"Copyright Text\":\"B2BDemo | 2022\",\"Core.Components.SectionUnderDevelopment.Message\":\"Under сonstruction\",\"Core.Components.SectionUnderDevelopment.ThankForSupport\":\"Thank you for your patience\",\"Core.Form.Errors.MaxFileSizeInPx [value]\":\"Max. file size is {{value}} px\",\"Core.Form.Errors.dateMustBeGreaterThan [value]\":\"Errors: date Must Be Greater Than {{value}}\",\"Core.Form.Errors.dateMustBeLessThan [value]\":\"Errors: date Must Be Less Than {{value}}\",\"Core.Form.Errors.isToLessFrom\":\"To is less From\",\"Core.Form.Errors.notEnoughFunds\":\"not Enough Funds\",\"Core.Models.Helpdesk.Ticket.TicketStatusTypes.NeedToReply\":\"Need to Reply\",\"Core.Models.Transaction.Status.Decline\":\"Declined\",\"Core.Models.Transaction.Status.Done\":\"Done\",\"Core.Models.Transaction.Status.Failed\":\"Failed\",\"Core.Models.Transaction.Status.Inprogress\":\"In Progress\",\"Core.Models.Transaction.Status.Wait\":\"Wait\",\"Core.Pagination.PageCountOfTotal [count, total]\":\"Page {{count}} of {{total}}\",\"Core.Services.ExceptionService.BadRequestException\":\"Bad Request\",\"Core.Services.ExceptionService.NotFoundException\":\"Not Found\",\"Core.Services.ExceptionService.ServiceUnavailableException\":\"Service is Unavailable\",\"Core.Services.NotificationService.NewMessage\":\"New Message\",\"Core.Services.NotificationService.StatusUpdated\":\"Status Updated\",\"Corporate account\":\"Corporate account\",\"Corporate account registration\":\"Corporate account registration\",\"Correspondent Bank Address\":\"Correspondent Bank Address\",\"Correspondent Bank Name\":\"Correspondent Bank Name\",\"Correspondent Bank SWIFT\":\"Correspondent Bank SWIFT\",\"Correspondent Bank Swift\":\"Correspondent Bank Swift\",\"Correspondent Bank address\":\"Correspondent Bank address\",\"Correspondent Bank name\":\"Correspondent Bank name\",\"Create Demo Account\":\"Create Demo Account\",\"Create Live Account\":\"Create Live Account\",\"Create a Ticket\":\"Create a Ticket\",\"Create a ticket here if you have a technical issue or a question regarding any B2Broker service you use\":\"Create a ticket here if you have a technical issue or a question regarding any service you use\",\"Create account\":\"Create {{account_type}} account\",\"Create widget\":\"Create widget\\t\",\"Create your first ticket\":\"Create your first ticket\",\"Credit\":\"Credit\",\"Currency\":\"Currency\",\"CurrencyExchange.IndicativeAmount.Title.Tooltip\":\"It’s an indicative amount, which depends on currency precision. The deposit amount won’t be affected.\",\"Current account type\":\"Current account type\",\"Current price\":\"Current price\",\"CustomWorkspaces\":\"Custom Workspaces\",\"Customer name\":\"Customer name\",\"DAY\":\"DAY\",\"DIRECT_EXCHANGE_ORDER_FILLED\":\"Direct exchange order filled\",\"Dashboard.Widgets.Assets.Title\":\"Assets\",\"Dashboard.Widgets.Banners.Title\":\"Banners\",\"Dashboard.Widgets.FilledOrders.Title\":\"Filled Orders\",\"Dashboard.Widgets.OpenOrders.Title\":\"Open Orders\",\"Dashboard.Widgets.OrderBook.Title\":\"Order Book\",\"Dashboard.Widgets.OrderHistory.Title\":\"Order History\",\"Dashboard.Widgets.PaymentAccounts.Mt4.Title\":\"Payment Accounts MT4\",\"Dashboard.Widgets.PaymentAccounts.Mt5.Title\":\"Payment Accounts MT5\",\"Dashboard.Widgets.RecentTransactions.SeeFullHistory\":\"See Full History\",\"Dashboard.Widgets.RecentTransactions.Title\":\"Recent Transactions\",\"Dashboard.Widgets.RecentTransactions.Tittle\":\"Recent Transactions\",\"Dashboard.Widgets.Support.Title\":\"Support\",\"Dashboard.Widgets.Support.Tittle\":\"Support\",\"Dashboard.Widgets.Ticker.AddInstrument\":\"Add Instrument\",\"Dashboard.Widgets.TickerWidget.Mt4.Title\":\"Ticker Widget MT4\",\"Dashboard.Widgets.TickerWidget.Mt5.Title\":\"Ticker Widget MT5\",\"Dashboard.Widgets.TotalBalance.ChartTitle\":\"Total Balance\",\"Dashboard.Widgets.TotalBalance.EmptyChart.Subtitle\":\"Subtitle\",\"Dashboard.Widgets.TotalBalance.EmptyChart.Title\":\"Total Balance\",\"Dashboard.Widgets.TotalBalance.LastUpdate [lastUpdateTime]\":\"Last Update {{lastUpdateTime}}\",\"Dashboard.Widgets.TotalBalance.LoadingLabel\":\"Loading\",\"Dashboard.Widgets.TotalBalance.Refresh\":\"Refresh\",\"Dashboard.Widgets.TotalBalance.Title\":\"Wallets Overview\",\"Dashboard.Widgets.TradingAccounts.Mt4.Title\":\"Trading Accounts MT4\",\"Dashboard.Widgets.TradingAccounts.Mt5.Title\":\"Trading Accounts MT5\",\"Dashboard.Widgets.TradingAccounts.mt5.Title\":\"Trading Accounts\",\"Dashboard.Widgets.TradingAccountsFavorites.Balance\":\"Balance\",\"Dashboard.Widgets.TradingAccountsFavorites.EmptyMessage\":\"The message is empty\",\"Dashboard.Widgets.TradingAccountsFavorites.FreeFunds\":\"Free Funds\",\"Dashboard.Widgets.TradingAccountsFavorites.Mt4.Title\":\"Trading Account Favorites MT4\",\"Dashboard.Widgets.TradingAccountsFavorites.Mt5.Title\":\"Trading Account Favorites MT5\",\"Dashboard.Widgets.TradingAccountsFavorites.NotificationMessage\":\"Notification Message\",\"Dashboard.Widgets.TradingAccountsFavorites.SubMenu.DeleteFavorite\":\"Delete\",\"Dashboard.Widgets.TradingAccountsFavorites.SubMenu.Deposit\":\"Deposit\",\"Dashboard.Widgets.TradingAccountsFavorites.SubMenu.SeeDetails\":\"See Details\",\"Dashboard.Widgets.TradingAccountsFavorites.mt5.Title\":\"Trading Account \",\"Dashboard.Widgets.Verification.Title\":\"VerificationED\",\"Dashboard.Widgets.Verification.Tittle\":\"VerificationTRT\",\"Dashboard.Widgets.Walkthrough.Title\":\"Walkthrough\",\"Dashboard.Widgets.Walkthrough.Tittle\":\"Walkthrough\",\"Dashboard.Widgets.WatchList.Title\":\"WatchList\",\"Date\":\"Date\",\"Date from\":\"Date from\",\"Date of create\":\"Date of create\",\"Date of processing\":\"Date of processing\",\"Date to\":\"Date to\",\"Days Remaining\":\"Days Remaining\\t\",\"Days remaining\":\"Days remaining\",\"Deal ID\":\"Deal ID\",\"Deals History\":\"Deals History\",\"Default\":\"Default\",\"Demo\":\"Demo\",\"Deposit\":\"Deposit\",\"Deposit amount\":\"Deposit amount\",\"Deposit from\":\"Deposit from\",\"Describe your issue / question\":\"Describe your issue / question\",\"Destination Account\":\"Destination Account\",\"Disable Double-click Trading\":\"Disable Double-click Trading\",\"Disable Two-factor Authentication\":\"Disable Two-factor Authentication\",\"Document\":\"Document\",\"Document Type\":\"Document Type\",\"Documents downloaded\":\"Documents downloaded\",\"Double-click Trading\":\"Double-click Trading\",\"Download\":\"Download\",\"Download and install the free Google Authenticator app\":\"Download and install the free <b>Google Authenticator</b> app\",\"DxTrade.AccountCard.BalanceUpdating\":\"Updating...\",\"DxTrade.AccountCard.Caption\":\"DXTrade account\",\"DxTrade.AccountCard.Trade\":\"Trade\",\"E-mail Confirmation\":\"E-mail Confirmation\",\"EXECUTING\":\"Executing\",\"EXPIRED\":\"Expired\",\"Edit\":\"Change\",\"Email\":\"Email\",\"Enable\":\"Enable\",\"Enable Double-click Trading\":\"Enable Double-click Trading\",\"Enable Google authenticator\":\"Enable Google authenticator\",\"Enable Text Message (SMS)\":\"Enable Text Message (SMS)\",\"Ending At\":\"Ending At\\t\",\"Enter 2FA Code from Google Authenticator app\":\"Enter 2FA Code from <b>Google Authenticator</b> app\",\"Enter code\":\"Enter code\",\"Enter new account name\":\"Enter new account name\",\"Enter new password\":\"Enter new password\",\"Enter phone number for receiving confirmation code\":\"Enter phone number for receiving confirmation code\",\"Entry\":\"Entry\",\"Equity\":\"Equity\",\"Eqwire.Account.Card.Copy\":\"Copy\",\"Eqwire.Account.Card.Download\":\"Download\",\"Eqwire.Auth.Register.TermsAndConditions\":\"Terms And Conditions\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.AccountNumber\":\"Account Number\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.BeneficiariesName\":\"Beneficiaries Name\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Bic\":\"Bic\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Cancel\":\"Cancel\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Create\":\"Create\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Currency\":\"Currency\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Iban\":\"Iban\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.SortCode\":\"Sort Code\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Title\":\"Add New Beneficiaries\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.Type\":\"Type\",\"Eqwire.Beneficiaries.AddNewBeneficiariesDialog.referenceMessage\":\"reference Message\",\"Eqwire.Beneficiaries.AddNewBeneficiary\":\"Add New Beneficiary\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.AccountNumber\":\"Account Number\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.BeneficiariesName\":\"Beneficiaries Name\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.Bic\":\"Bic\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.Currency\":\"Currency\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.Iban\":\"Iban\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.ReferenceMessage\":\"Reference Message\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.SortCode\":\"Sort Code\",\"Eqwire.Beneficiaries.BeneficiaryFormDialog.Type\":\"Type\",\"Eqwire.Beneficiaries.Card.AccountNumber\":\"Account Number\",\"Eqwire.Beneficiaries.Card.Bic\":\"Bic\",\"Eqwire.Beneficiaries.Card.Confirm\":\"Confirm\",\"Eqwire.Beneficiaries.Card.Currency\":\"Currency\",\"Eqwire.Beneficiaries.Card.Iban\":\"Iban\",\"Eqwire.Beneficiaries.Card.ReferenceMessage\":\"Reference Message\",\"Eqwire.Beneficiaries.Card.SortCode\":\"Sort Code\",\"Eqwire.Beneficiaries.Card.SortCodeAndAccountNumber\":\"Sort Code And Account Number\",\"Eqwire.Beneficiaries.Card.Status\":\"Status\",\"Eqwire.Beneficiaries.Card.Type\":\"Type\",\"Eqwire.Beneficiaries.Card.Update\":\"Update\",\"Eqwire.Beneficiaries.DeleteDialog.Cancel\":\"Cancel\",\"Eqwire.Beneficiaries.DeleteDialog.Confirm\":\"Confirm\",\"Eqwire.Beneficiaries.DeleteDialog.Message\":\"Message\",\"Eqwire.Beneficiaries.DeleteDialog.Message [beneficiaryName]\":\"Message {{beneficiaryName}}\",\"Eqwire.Beneficiaries.DeleteDialog.Title\":\"Title\",\"Eqwire.Beneficiaries.EditBeneficiaryDialog.Cancel\":\"Cancel\",\"Eqwire.Beneficiaries.EditBeneficiaryDialog.Edit\":\"Edit\",\"Eqwire.Beneficiaries.EditBeneficiaryDialog.Title\":\"Title\",\"Eqwire.Beneficiaries.SearchByName\":\"Search By Name\",\"Eqwire.Beneficiaries.SearchInputPlaceholder\":\"Search Input Placeholder\",\"Eqwire.Beneficiaries.SmsConfirm.ReachedLimit\":\"Reached Limit\",\"Eqwire.Beneficiaries.SmsConfirm.Title\":\"Sms Confirm Title\",\"Eqwire.Beneficiaries.SmsConfirm.Verify\":\"Verify\",\"Eqwire.Beneficiaries.SmsConfirm.VerifyButton\":\"Verify Button\",\"Eqwire.Beneficiaries.SortPlaceholder\":\"Sort Placeholder\",\"Eqwire.Beneficiaries.SuccessfulConfirm\":\"Successful Confirm\",\"Eqwire.Beneficiaries.SuccessfulCreate\":\"Successful Create\",\"Eqwire.Beneficiaries.SuccessfulDelete\":\"Successful Delete\",\"Eqwire.Beneficiaries.Title\":\"Title\",\"Eqwire.Consent.Components.ShareInformationConfirm.Footer [tradingName]\":\"Footer {{tradingName}}\",\"Eqwire.Consent.Components.ShareInformationConfirm.SelectAccountsLabel [companyName] [tradingName]\":\"SelectAccountsLabel {{companyName}} {{tradingName}}\",\"Eqwire.Onboarding.Components.FormButtonsTemplate.Button.Label.Apply\":\"Apply\",\"Eqwire.Onboarding.Components.FormButtonsTemplate.Button.Label.Back\":\"Back\",\"Eqwire.Onboarding.Components.FormButtonsTemplate.Button.Label.Next\":\"Next\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.BeneOwner\":\"BeneOwner\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.BeneOwnerAndDirector\":\"Bene Owner And Director\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.BeneOwnerAndPartner\":\"BeneOwner And Partner\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.CommunityInterestCompany\":\"Community Interest Company\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.CompanySecretary\":\"Company Secretary\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.Director\":\"Director\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.Partner\":\"Partner\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.Signatory\":\"Signatory\",\"Eqwire.Onboarding.Const.FieldOption.AssociateType.SoleTrader\":\"SoleTrader\",\"Eqwire.Onboarding.Const.FieldOption.CompanyType.LimitedCompany\":\"LimitedCompany\",\"Eqwire.Onboarding.Const.FieldOption.CompanyType.LimitedLiabilityPartnership\":\"Limited Liability Partnership\",\"Eqwire.Onboarding.Const.FieldOption.CompanyType.PubliclyListedCompany\":\"PubliclyListedCompany\",\"Eqwire.Onboarding.Const.FieldOption.CompanyType.SoleTrader\":\"SoleTrader\",\"Eqwire.Onboarding.Const.FieldOption.LimitedPartnership\":\"LimitedPartnership\",\"Eqwire.Onboarding.Const.FormConfig.AddressLine1\":\"AddressLine1\",\"Eqwire.Onboarding.Const.FormConfig.AddressLine2\":\"AddressLine2\",\"Eqwire.Onboarding.Const.FormConfig.AssociateApplicant\":\"Associate Applicant\",\"Eqwire.Onboarding.Const.FormConfig.AssociateEmail\":\"Associate Email\",\"Eqwire.Onboarding.Const.FormConfig.AssociateOwnership\":\"Associate Ownership\",\"Eqwire.Onboarding.Const.FormConfig.AssociatePhone\":\"Associate Phone\",\"Eqwire.Onboarding.Const.FormConfig.AssociateType\":\"Associate Type\",\"Eqwire.Onboarding.Const.FormConfig.BirthDate\":\"BirthDate\",\"Eqwire.Onboarding.Const.FormConfig.BusinessType\":\"Business Type\",\"Eqwire.Onboarding.Const.FormConfig.City\":\"City\",\"Eqwire.Onboarding.Const.FormConfig.CompanyName\":\"Company Name\",\"Eqwire.Onboarding.Const.FormConfig.CompanyRegNumber\":\"Company RegNumber\",\"Eqwire.Onboarding.Const.FormConfig.Country\":\"Country\",\"Eqwire.Onboarding.Const.FormConfig.ExpectedMonthlySpend\":\"Expected Monthly Spend\",\"Eqwire.Onboarding.Const.FormConfig.IndustryCode\":\"Industry Code\",\"Eqwire.Onboarding.Const.FormConfig.LegalEntity\":\"Legal Entity\",\"Eqwire.Onboarding.Const.FormConfig.LegalEntity.Gb\":\"Gb\",\"Eqwire.Onboarding.Const.FormConfig.LegalFirstName\":\"Legal First Name\",\"Eqwire.Onboarding.Const.FormConfig.LegalLastName\":\"Legal Last Name\",\"Eqwire.Onboarding.Const.FormConfig.LegalMiddleName\":\"Legal Middle Name\",\"Eqwire.Onboarding.Const.FormConfig.PostCode\":\"Post Code\",\"Eqwire.Onboarding.Const.StepperConfig.AboutYourBusiness\":\"About Your Business\",\"Eqwire.Onboarding.Const.StepperConfig.AssociatesDetailsAndHomeAddress\":\"Associates Details And Home Address\",\"Eqwire.Onboarding.Const.StepperConfig.RegisteredAddress\":\"Registered Address\",\"Eqwire.Onboarding.Const.StepperConfig.TradingAddress\":\"Trading Address\",\"Eqwire.Onboarding.PendingOnboarding.Message\":\"Message\",\"Eqwire.Onboarding.PendingOnboarding.RefreshPage\":\"Refresh Page\",\"Eqwire.Onboarding.PendingOnboarding.Title\":\"Pending Onboarding\",\"Eqwire.Onboarding.SuggestOnboarding.GoToOnboarding\":\"Go To Onboarding\",\"Eqwire.Onboarding.SuggestOnboarding.Message\":\"SuggestOnboarding Message\",\"Eqwire.Onboarding.SuggestOnboarding.Title\":\"SuggestOnboarding\",\"Eqwire.PaymentsAndTransfers.MakeTransfer.SuccessMessage\":\"Transfer Success Message\",\"Eqwire.PaymentsAndTransfers.SavedBeneficiary.Placeholder.SelectBeneficiary\":\"Select Beneficiary\",\"Eqwire.PaymentsAndTransfers.SourceAccount\":\"Source Account\",\"Eqwire.PaymentsAndTransfers.SubmitButton.Label\":\"SubmitButton Label\",\"Eqwire.PaymentsAndTransfers.Title\":\"Title\",\"Eqwire.PaymentsAndTransfers.TransactionType.MyAccount\":\"My Account\",\"Eqwire.PaymentsAndTransfers.TransactionType.NewPayment\":\"New Payment\",\"Eqwire.PaymentsAndTransfers.TransactionType.SavedBeneficiary\":\"Saved Beneficiary\",\"EqwireAccounts.Components.AddNewFundDialog.AddNewFund\":\"Add New Fund\",\"EqwireAccounts.Components.AddNewFundDialog.Amount\":\"Amount\",\"EqwireAccounts.Components.AddNewFundDialog.Bank\":\"Bank\",\"EqwireAccounts.Components.AddNewFundDialog.Cancel\":\"Cancel\",\"EqwireAccounts.Components.AddNewFundDialog.FundDescription\":\"Fund Description\",\"EqwireAccounts.Components.AddNewFundDialog.ReferenceMessage\":\"Reference Message\",\"EqwireAccounts.Components.AddNewFundDialog.ReferenceMessageHint\":\"Reference Message Hint\",\"EqwireAccounts.Components.AddNewFundDialog.Title\":\"Title\",\"EqwireAccounts.Components.TransactionDetailDialog.PaymentDetails\":\"Payment Details\",\"EqwireAccounts.Components.TransactionDetailDialog.PaymentId\":\"Payment Id\",\"EqwireAccounts.Components.TransactionDetailDialog.RecipientDetails\":\"Recipient Details\",\"EqwireAccounts.Components.TransactionsFilter.All\":\"All\",\"EqwireAccounts.Components.TransactionsFilter.In\":\"In\",\"EqwireAccounts.Components.TransactionsFilter.Out\":\"Out\",\"EqwireAccounts.Components.TransactionsFilter.ResetFilters\":\"Reset Filters\",\"EqwireAccounts.Components.TransactionsFilter.Title\":\"Title\",\"EqwireAccounts.EqwireAccountCard.AccountNumber\":\"Account Number\",\"EqwireAccounts.EqwireAccountCard.SortCode\":\"Sort Code\",\"EqwireAccounts.EqwireAccountsList.SortOptions.Name.Asc\":\"Name Asc\",\"EqwireAccounts.EqwireAccountsList.SortOptions.Name.Desc\":\"Name Desc\",\"EqwireAccounts.EqwireBeneficiaries.SortOptions.Asc\":\"Asc\",\"EqwireAccounts.EqwireBeneficiaries.SortOptions.Desc\":\"Desc\",\"EqwireAccounts.EqwireBeneficiaries.SortOptions.Name.Asc\":\"Name Asc\",\"EqwireAccounts.EqwireBeneficiaries.SortOptions.Name.Desc\":\"Name Desc\",\"Error.Button.GoToHomePage\":\"Go to home page\",\"ErrorDialog.AccentButton.Refresh\":\"Refresh\",\"ErrorDialog.Common.Header\":\"Header\",\"ErrorDialog.Common.Text\":\"Text\",\"ErrorDialog.GridsterExcessiveActivity.ButtonText\":\".ButtonText\",\"ErrorDialog.GridsterExcessiveActivity.Header\":\"Gridster Excessive Activity\",\"ErrorDialog.GridsterExcessiveActivity.Text\":\"Text\",\"ErrorDialog.PrimaryButton.GoBack\":\"Go Back\",\"Exchange\":\"Exchange\",\"Exchange.Button.GoToHomePage\":\"Go to home page\",\"Exchange.RecentExchanges.NoTransactionsMessage.PartOne\":\"Part One\",\"Exchange.RecentExchanges.NoTransactionsMessage.PartTwo\":\"Part Two\",\"Exchange.RecentExchanges.SourceToDestination [source, destination]\":\"Source to Destination {{source}},{{destination}}\",\"Exchange.RecentExchanges.Title\":\"Title\",\"Expiration month\":\"Expiration month\",\"Expiration year\":\"Expiration year\",\"Expires at\":\"Expires at\",\"External\":\"External\",\"FOK\":\"FOK\",\"FPL\":\"FPL\",\"Family name\":\"Family name\",\"Features.HelpAndFqa\":\"HelpAndFqa\",\"Features.TestingToAccessInstruments\":\"Testing To Access Instruments\",\"Features.Verification\":\"Verification\",\"Fee\":\"Fee\",\"Field is required\":\"Field is required\",\"File not selected\":\"File not selected\",\"Files is too large\":\"Files are too large\",\"Filters results\":\"Filters results\",\"First name\":\"First name\",\"Footer.NeedHelp\":\"Need Help ?\",\"Forgot your password?\":\"Forgot your password?\",\"Form doesn't contain required field. Please, contact administrator\":\"Form doesn't contain required field. Please, contact administrator.\",\"Form.Components.FeeSelect.Title\":\"Select Withdrawal Fee\",\"Form.Components.Select.SelectAutocomplete.NoResults\":\"No results found\",\"Free Funds\":\"Free Funds\",\"Free Margin\":\"Free Margin\",\"From\":\"From\",\"From Account\":\"From Account\",\"From Trading Account\":\"From Trading Account\",\"From currency\":\"From currency\",\"FullyExecuted\":\"Fully Executed\",\"Funds were successfully deposited. Please note that it may take some time for funds to appear on your account, depending on a Payment system.\":\"Funds were successfully deposited. Please note that it may take some time for funds to appear on your account, depending on a Payment system.\",\"GTC\":\"GTC\",\"GTD\":\"GTD\",\"Get more orders\":\"Get more orders\",\"Given name\":\"Given name\",\"GlobalWorkspaces\":\"Global Workspaces\",\"Go back\":\"Go back\",\"Go home\":\"Go home\",\"Go to upgrade\":\"Upgrade level\",\"Google Authenticator\":\"Google Authenticator\",\"GridOptions.Fit\":\"Fit\",\"GridOptions.HorizontalFixed\":\"Fixed\",\"GridOptions.ScrollHorizontal\":\"Scroll\",\"GridOptions.ScrollVertical\":\"Scroll\",\"GridOptions.VerticalFixed\":\"Fixed\",\"Grouping\":\"Grouping\",\"Haven't received the code?\":\"Haven't received the code?\",\"Help Desk\":\"24/7 Customer Support\",\"Help desk (description)\":\"Help desk (description)\",\"HelpAndFaq.Title\":\"HelpAndFaq\",\"HelpDesk.SupportMessage.Arabic\":\"Arabic\",\"HelpDesk.SupportMessage.Arabic.Timetable\":\"8:00-20:00\",\"HelpDesk.SupportMessage.Chinese\":\"Chinese\",\"HelpDesk.SupportMessage.Chinese.Timetable\":\"Enter the working hours for the Chinese-speaking support\",\"HelpDesk.SupportMessage.Description\":\"HelpDesk Support Message Description\",\"HelpDesk.SupportMessage.English\":\"English\",\"HelpDesk.SupportMessage.English.Timetable\":\"8:00-20:00\",\"HelpDesk.SupportMessage.Hindi\":\"Hindi\",\"HelpDesk.SupportMessage.Hindi.Timetable\":\"Enter the working hours for the Hindi-speaking support\",\"HelpDesk.SupportMessage.Russian\":\"Помощь на русском\",\"HelpDesk.SupportMessage.Russian.Timetable\":\"8:00-20:00\",\"HelpDesk.SupportMessage.Spanish\":\"Spanish\",\"HelpDesk.SupportMessage.Spanish.Timetable\":\"8:00-20:00\",\"HelpDesk.SupportMessage.SubTitle\":\"Sub Title\",\"HelpDesk.SupportMessage.Title\":\"Title\",\"Helpdesk.Chat.Button.Notification.CreateTicket\":\"Create Ticket\",\"Helpdesk.Chat.Button.Notification.Desktop.Enable\":\"Enable Desktop Notifications\",\"Helpdesk.Chat.Button.Notification.Email.Disable\":\"Disable Email Notifications\",\"Helpdesk.Chat.Button.Notification.Email.Enable\":\"Enable Email Notifications\",\"Helpdesk.Chat.CreateTicket\":\"Create Ticket\",\"Helpdesk.Chat.DropZone.Text\":\"Drop your Files Here\",\"Helpdesk.Chat.Error.SomethingWentWrong\":\"Something Went Wrong\",\"Helpdesk.Chat.Error.UserIsBeingCreated\":\"User is Being Created\",\"Helpdesk.Chat.SearchBy.ID\":\"SearchBy ID\",\"Helpdesk.Chat.SearchBy.TicketContent\":\"Search by Ticket Content\",\"Helpdesk.Chat.SearchBy.TicketSubject\":\"Search by Ticket Subject\",\"Helpdesk.Chat.TicketSuccessfullyCreated\":\"Ticket Successfully Created\",\"Helpdesk.Chat.Title\":\"Support chat\",\"Helpdesk.Chat.UserSuccessfullyCreated\":\"User Successfully Created\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.Button.Proceed\":\"Proceed\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.CommentLabel\":\"Share your feedback\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.CommentPlaceholder\":\"Write your feedback here. It’s optional\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.Description [tickedId]\":\"Your support ticket ID: {{tickedId}} was marked as resolved. We’d love to hear what you think about our customer service\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.StatusLabel\":\"How do you rate the support?\",\"Helpdesk.SupportRateDialog.Components.SupportRateDialog.Title\":\"User feedback\",\"Here you can download the trading platform for any OS\":\"Here you can download the trading platform for any OS\",\"Hide zero balances\":\"Hide zero balances\",\"History\":\"History\",\"InternalTransfer.SelectPlatform.DestinationPlatform\":\"Destination Platform\",\"InternalTransfer.SelectPlatform.Proceed\":\"Proceed\",\"InternalTransfer.SelectPlatform.Title\":\"Select Platform\",\"IntroductionTour.BackButtonText\":\"Back\",\"IntroductionTour.ExitButtonText\":\"Skip\",\"IntroductionTour.NextButtonText\":\"Next\",\"Invalid filters\":\"Invalid filters\",\"Investment\":\"Investment\",\"It’s time to fix it!\":\"It's time to fix it!\",\"LIMIT\":\"LIMIT\",\"Language\":\"Language\",\"Language changed successfully\":\"Language changed successfully\",\"Last 3 days\":\"Last 3 days\",\"Last Price\":\"Last Price\",\"Last month\":\"Last month\",\"Last name\":\"Last name\",\"Last week\":\"Last week\",\"Layouts.CookieMessage.Button.Close\":\"Close\",\"Layouts.CookieMessage.Text\":\"This site uses cookies.\",\"Layouts.CookieMessage.Title\":\"Cookies\",\"Layouts.FullLayout.Logout\":\"Log Out\",\"Layouts.Header.Logout\":\"Log Out\",\"Level\":\"Level\",\"Leverage\":\"Leverage\",\"Leverages is not configure\":\"Leverages is not configure\",\"Limit\":\"Limit\",\"Limits\":\"Pending Orders\",\"Linux\":\"Linux\",\"Live\":\"Live\",\"Loading\":\"Loading\",\"Log Off\":\"Log Off\",\"Log out\":\"Log out\",\"Login.QRCodeLogin.CodeExpired\":\"Code Expired\",\"Login.QRCodeLogin.Loading\":\"Loading\",\"Login.QRCodeLogin.TextWithAppLink\":\"Text With App Link\",\"Login.QRCodeLogin.Title\":\"QR Code Login\",\"Login.QRCodeLogin.UpdateCode\":\"Update Code\",\"Logout\":\"\\tLogout\",\"Logout.FailLogoutMessage\":\"Log Out Failed\",\"Lots Remaining\":\"Lots Remaining\\t\",\"Lots per Unit\":\"Lots per Unit\\t\",\"Lots remaining\":\"Lots remaining\",\"MARKET\":\"MARKET\",\"MM\":\"MM\",\"MacOS\":\"MacOS\",\"Maker Fee\":\"Maker Fee\",\"Margin rate %\":\"Margin Level, %\",\"MarginOptions.Default\":\"Default\",\"MarginOptions.Large\":\"Large\",\"MarginOptions.None\":\"None\",\"MarginOptions.Small\":\"Small\",\"Mark all as read\":\"Mark all as read\",\"Market\":\"Market\",\"Master\":\"Master\",\"MatchTrader.AccountCard.BalanceUpdating\":\"Updating...\",\"MatchTrader.AccountCard.Caption\":\"MatchTrader account\",\"MatchTrader.AccountCard.Trade\":\"Trade\",\"Menu.New\":\"New\",\"Menu_Analytics\":\"Analytics\",\"Menu_ApiKeyManagement\":\"API Key Management\",\"Menu_B2Copy\":\"B2Copy\",\"Menu_B2Copy_AccountList\":\"https://www.google.com/\",\"Menu_B2Copy_Leaderboard\":\"https://www.google.com/\",\"Menu_B2Margin\":\"B2Margin\",\"Menu_B2Trader\":\"B2Trader\",\"Menu_Bonuses\":\"Bonuses\",\"Menu_DXTrade\":\"DXTrade\",\"Menu_Dashboard\":\"Dashboard\",\"Menu_Deposit\":\"Deposit\",\"Menu_Exchange\":\"Exchange\",\"Menu_ExternalLink1\":\"Test\",\"Menu_Finance\":\"Finance\",\"Menu_Funds\":\"Funds\",\"Menu_Help\":\"Help\",\"Menu_Helpdesk\":\"Helpdesk\",\"Menu_History\":\"History\",\"Menu_IB\":\"IB\",\"Menu_InternalTransfer\":\"Internal Transfer\",\"Menu_InvestmentAccounts\":\"My Accounts\",\"Menu_Leaderboard\":\"Leaderboard\",\"Menu_MAM\":\"MAM\",\"Menu_MT4\":\"MT4\",\"Menu_MT5\":\"MT5\",\"Menu_Mam\":\"MAM\",\"Menu_Mam_AccountList\":\"My Accounts\",\"Menu_Mam_Leaderboard\":\"Leaderboard\",\"Menu_MatchTrader\":\"MatchTrader\",\"Menu_New_Trade\":\"New Trade\",\"Menu_Orders\":\"Orders\",\"Menu_OzPxm\":\"Oz Pxm\",\"Menu_PAMM\":\"PAMM\",\"Menu_Partner\":\"Partner\",\"Menu_PartnerAccounts\":\"Accounts\",\"Menu_PartnerAcquisitions\":\"Acquisitions\",\"Menu_PartnerBanners\":\"Banners\",\"Menu_PartnerClientList\":\"Clients\",\"Menu_PartnerCreate\":\"Join Us\",\"Menu_PartnerDashboard\":\"Dashboard\",\"Menu_PartnerDeposits\":\"Deposits\",\"Menu_PartnerLinks\":\"Links\",\"Menu_PartnerPromo\":\"Promo\",\"Menu_PartnerReferral\":\"Referral\",\"Menu_PartnerReports\":\"Reports\",\"Menu_PartnerRewards\":\"Rewards\",\"Menu_PartnerTrades\":\"Trades\",\"Menu_PartnerTransactions\":\"Transactions\",\"Menu_PartnerWithdrawals\":\"Withdrawals\",\"Menu_PayoutWhiteList\":\"Address Whitelist\",\"Menu_Pbsr_HelpAndFaq\":\"Help And Faq\",\"Menu_Pbsr_PbsrTesting\":\"Testing\",\"Menu_PersonalInfo\":\"Personal Info\",\"Menu_Platforms\":\"Platforms\",\"Menu_Profile\":\"Profile\",\"Menu_Security\":\"Security\",\"Menu_Settings\":\"Settings\",\"Menu_Trade\":\"Trade\",\"Menu_Trade_B2Margin\":\"B2Margin\",\"Menu_Trade_B2Trader\":\"B2Trader\",\"Menu_Transactions\":\"Transactions\",\"Menu_Transfer\":\"Transfer\",\"Menu_Verification\":\"Verification\",\"Menu_Verification_Sum_Sub\":\"Verification\",\"Menu_Wallet\":\"Wallet\",\"Menu_Withdraw\":\"Withdraw\",\"Menu_cTrader\":\"cTrader Trading Platform\",\"Message not sent due to error\":\"Message not sent due to error\",\"Method\":\"Method\",\"Middle name\":\"Middle name\",\"Modify position\":\"Modify position\",\"Moex.Widgets.MoexPlaceOrder.Title\":\"Widgets Place Order\",\"Moex.Widgets.Orders.Title\":\"Widgets Orders\",\"Moex.Widgets.Verification.Title\":\"Verification\",\"Moex.Widgets.WarpPortfolio.Title\":\"Portfolio\",\"Moex.Widgets.WatchList.Title\":\"WatchList\",\"MoexOrder.All\":\"All\",\"MoexOrder.Buy\":\"Buy\",\"MoexOrder.Conditional\":\"Conditional\",\"MoexOrder.Limit\":\"Limit\",\"MoexOrder.Market\":\"Market\",\"MoexOrder.Sell\":\"Sell\",\"Must be greater than zero\":\" must be greater than zero\",\"Must be lower or equal to {{value}}\":\"Must be lower or equal to {{value}}\",\"Name\":\"Name\",\"New Widget\":\"New Widget\",\"Next page\":\"Next Page\",\"Next step\":\"Next step\",\"Night mode\":\"Night Mode\",\"No\":\"No\",\"No Banners Available at the Moment\":\"No Banners Available at the Moment\",\"No Detailed Info Provided for this Trading Account\":\"No Detailed Info Provided for this Trading Account\",\"No Memo\":\"No Memo ID\",\"No Message\":\"No Message\",\"No Quick Links Yet\":\"No Quick Links Yet\\t\",\"No accounts available\":\"No accounts available\",\"No analytics generated yet, it will be generated once there is any account activity on it.\":\"No analytics generated yet, it will be generated once there is any account activity on it.\",\"No destination tag\":\"No destination tag\",\"No instruments\":\"No symbols\",\"No rate found\":\"No rate found\",\"No record found\":\"No record found\",\"Not a member?\":\"Not a member?\",\"Notifications\":\"Notifications\",\"OCO\":\"OCO\",\"OPENING\":\"Open\",\"ORDER_CANCELING\":\"Cancelling\",\"ORDER_CANCELLATION_REJECTED\":\"Order cancelation rejected\",\"ORDER_CANCELLED\":\"Order canceled\",\"ORDER_COMPLETED\":\"Completed\",\"ORDER_EXECUTING\":\"Executing\",\"ORDER_EXPIRED\":\"Expired\",\"ORDER_MODIFICATION_REJECTED\":\"Order modification rejected\",\"ORDER_MODIFIED\":\"Order Modified\",\"ORDER_PARTIALLY_FILLED\":\"Order was partially filled\",\"ORDER_PENDING\":\"Order is pending\",\"ORDER_PLACED\":\"Order was placed\",\"ORDER_REJECTED\":\"Order was rejected\",\"ORDER_REPLACING\":\"Replacing\",\"ORDER_SENDING\":\"Sending\",\"ORDER_TRIGGERED\":\"Triggered\",\"ORDER_WAITING_CONDITION\":\"Waiting Condition\",\"ORDER_WAITING_LIMIT\":\"Waiting Limit\",\"ORDER_WAITING_STOP\":\"Waiting Stop\",\"ORDER_WAITING_TRIGGER\":\"Waiting Trigger\",\"ORDER_WORKING\":\"Working\",\"Okay, got it\":\"Okay, got it\",\"OneZero\":\"OneZero\",\"Oops, you're lost\":\"Oops, you're lost\",\"Open\":\"Open\",\"Open account\":\"Open account\",\"Open position\":\"Open Position\",\"Open price\":\"Open price\",\"Open time\":\"Open time\",\"Or send funds to the address provided below\":\"Or send funds to the address provided below\",\"Order ID\":\"Order ID\",\"Order Type\":\"Order\",\"Order cancellation rejected\":\"Order cancellation rejected\",\"Order modify rejected\":\"Order modify rejected\",\"Order or instrument not found\":\"Order or instrument not found\",\"Order rejected\":\"Order rejected\",\"Order successfully completed\":\"Order successfully completed\",\"Order successfully filled\":\"Order successfully filled\",\"Order successfully placed\":\"Order successfully placed\",\"Order type\":\"Order\",\"Orders\":\"Orders\",\"Original amount\":\"Original amount\",\"PENDING\":\"Pending\",\"PL\":\"P/L\",\"POSITION_CANCELING\":\"Cancelling\",\"POSITION_CLOSED\":\"Position closed\",\"POSITION_CLOSED_BY_STOP_LOSS\":\"Position closed by Stop Loss\",\"POSITION_CLOSED_BY_TAKE_PROFIT\":\"Position closed by Take Profit\",\"POSITION_CLOSED_VIA_CLOSE_BY\":\"Position closed via Close by\",\"POSITION_CLOSING_PENDING\":\"Position closing pending\",\"POSITION_CLOSING_REJECTED\":\"Position closing rejected\",\"POSITION_COMPLETED\":\"Completed\",\"POSITION_EXECUTING\":\"Executing\",\"POSITION_EXPIRED\":\"Position is expired\",\"POSITION_OPENED\":\"Position opened\",\"POSITION_PARTIALLY_CLOSED\":\"Position was partially closed\",\"POSITION_PARTIALLY_CLOSED_VIA_CLOSE_BY\":\"Position partially closed via Close by\",\"POSITION_PENDING\":\"Position is pending\",\"POSITION_PLACED\":\"Placed\",\"POSITION_REJECTED\":\"Rejected\",\"POSITION_REPLACING\":\"Replacing\",\"POSITION_SENDING\":\"Sending\",\"POSITION_WAITING_CONDITION\":\"Waiting Condition\",\"POSITION_WAITING_LIMIT\":\"Waiting Limit\",\"POSITION_WAITING_STOP\":\"Waiting Stop\",\"POSITION_WAITING_TRIGGER\":\"Waiting Trigger\",\"POSITION_WORKING\":\"Working\",\"PageTitle.Accreditation\":\"Accreditation_test\",\"PageTitle.Analytics\":\"Analytics_test\",\"PageTitle.ApiKeyManagement\":\"API Key Management_test\",\"PageTitle.Bonuses\":\"Bonuses_test\",\"PageTitle.Dashboard\":\"Dashboard_test\",\"PageTitle.Deposit\":\"Deposit_test\",\"PageTitle.Documents\":\"Documents_test\",\"PageTitle.Exchange\":\"Exchange_test\",\"PageTitle.Finance\":\"Finance_test\",\"PageTitle.Helpdesk\":\"Helpdesk_test\",\"PageTitle.History\":\"History_test\",\"PageTitle.IBRoom\":\"IBRoom_test\",\"PageTitle.InternalTransfer\":\"InternalTransfer_test\",\"PageTitle.Login\":\"Login_test\",\"PageTitle.Profile\":\"Profile_test\",\"PageTitle.Register\":\"Register_test\",\"PageTitle.Restore\":\"Restore_test\",\"PageTitle.Trade\":\"Trade_test\",\"PageTitle.TradeAccountStatistic\":\"TradeAccountStatistic_test\",\"PageTitle.TradeAccountTransfer\":\"TradeAccountTransfer_test\",\"PageTitle.TradingBoard\":\"TradingBoard_test\",\"PageTitle.Transactions\":\"Transactions_test\",\"PageTitle.Transfer\":\"Transfer_test\",\"PageTitle.Verification\":\"Verification_test\",\"PageTitle.Wallet\":\"Wallet_test\",\"PageTitle.WalletDetails\":\"WalletDetails_test\",\"PageTitle.Withdraw\":\"Withdraw_test\",\"Partner\":\"TestTest\",\"Partner.Board.Shared.Placeholder.DateFrom\":\"Date: from\",\"Partner.Board.Shared.Placeholder.DateTo\":\"Date: to\",\"Partner.Board.Shared.Placeholder.GroupFilter\":\"Group By\",\"Partner.Board.Widgets.Acquisition.Indicator.Clicks\":\"Acquisitions\",\"Partner.Board.Widgets.Acquisition.Indicator.Registrations\":\"Registrations\",\"Partner.Board.Widgets.Acquisition.Table.Clicks\":\"Acquisitions\",\"Partner.Board.Widgets.Acquisition.Table.Date\":\"Date\",\"Partner.Board.Widgets.Acquisition.Table.Registrations\":\"Registrations\",\"Partner.Board.Widgets.TradingReport.Indicator.ActiveTraders\":\"Active Traders\",\"Partner.Board.Widgets.TradingReport.Indicator.RewardAmount\":\"Reward Amount\",\"Partner.Board.Widgets.TradingReport.Indicator.TradingVolume\":\"Trading Volume\",\"Partner.Board.Widgets.TradingReport.Table.ActiveTraders\":\"Active Traders\",\"Partner.Board.Widgets.TradingReport.Table.Date\":\"Date\",\"Partner.Board.Widgets.TradingReport.Table.RewardAmt\":\"RewardAmt\",\"Partner.Board.Widgets.TradingReport.Table.Trades\":\"Trades\",\"Partner.Board.Widgets.TradingReport.Table.TradingVol\":\"Trading Vol\",\"Partner.Common.PartnerLink.CopyMessage\":\"Link Copied\",\"Partner.Common.PartnerLink.LinkIsNotFound\":\"Please select other parameters to generate URL\",\"Partner.Common.PartnerLink.Placeholder.LandingPage\":\"Landing Page\",\"Partner.Common.PartnerLink.Placeholder.Language\":\"Language\",\"Partner.Create.Multi.Button.Back\":\"Back\",\"Partner.Create.Multi.Button.Cancel\":\"Cancel\",\"Partner.Create.Multi.Button.Confirm\":\"Confirm\",\"Partner.Create.Multi.ChoosePartnerProgram\":\"Select IB Plan\",\"Partner.Create.Multi.EmptyPartnerProgramListMessage\":\"ProgramListMessage\",\"Partner.Create.Multi.Placeholder\":\"Placeholder\",\"Partner.Create.PageTitle\":\"Referral\",\"Partner.Create.Single.LinkText\":\"Become a Partner\",\"Partner.Create.Single.WelcomeMessage\":\"WelcomeMessage\",\"Partner.DropDownMenu.BecomeAPartner\":\"Become A Partner\",\"Partner.PartnerBoard.Shared.Placeholder.Day\":\"Day\",\"Partner.PartnerBoard.Shared.Placeholder.Hour\":\"Hour\",\"Partner.PartnerBoard.Shared.Placeholder.Month\":\"Month\",\"Partner.PartnerBoard.Shared.Placeholder.Week\":\"Week\",\"Partner.PartnerBoard.Shared.Placeholder.Year\":\"Year\",\"Partner.PartnerBoard.Widgets.TrafficSource.Table.Name\":\"Name\",\"Partner.PartnerBoard.Widgets.TrafficSource.Table.Rewards\":\"Rewards\",\"Partner.Promo.Banners.AltImage\":\"Alt Image\",\"Partner.Promo.Banners.BannerCode.CopyMessage\":\"Copy Message\",\"Partner.Promo.Banners.Button.Copy\":\"Copy\",\"Partner.Promo.Banners.NoBanners\":\"No Banners\",\"Partner.Promo.Banners.NoSizes\":\"No Sizes\",\"Partner.Promo.Banners.OptionName.All\":\"All\",\"Partner.Promo.Banners.Placeholder.Language\":\"Language\",\"Partner.Promo.Banners.Placeholder.Link\":\"Link\",\"Partner.Promo.Banners.Placeholder.Size\":\"Size\",\"Partner.Promo.Banners.Placeholder.Theme\":\"Theme\",\"Partner.Promo.Links.ContentLink.Title\":\"Your Partner Link\",\"Partner.Promo.Links.PageTitle\":\"Links\",\"Partner.Promo.Links.QRCode.Button.CopyEmbed\":\"CopyEmbed\",\"Partner.Promo.Links.QRCode.Button.Download\":\"Download\",\"Partner.Promo.Links.QRCode.Button.GenerateCode\":\"Generate Code\",\"Partner.Promo.Links.QRCode.CopyMessage\":\"QR Code Copied\",\"Partner.Promo.Links.QRCode.Placeholder.Color\":\"Color\",\"Partner.Promo.Links.QRCode.Placeholder.Icon\":\"Icon\",\"Partner.Promo.Links.QRCode.Placeholder.ResetValue\":\"None\",\"Partner.Promo.Links.QRCode.Title\":\"QR Code\",\"Partner.Promo.Links.UTMParameters.Button.Reset\":\"UTM Parameters Reset\",\"Partner.Promo.Links.UTMParameters.Button.Show\":\"UTM Parameters Show\",\"Partner.Promo.Links.UTMParameters.Placeholder.CampaignName\":\"Campaign Name\",\"Partner.Promo.Links.UTMParameters.Placeholder.Content\":\"Content\",\"Partner.Promo.Links.UTMParameters.Placeholder.Medium\":\"Medium\",\"Partner.Promo.Links.UTMParameters.Placeholder.Source\":\"Source\",\"Partner.Promo.Links.UTMParameters.Placeholder.Term\":\"Term\",\"Partner.Reports.Accounts.ColumnLabel.AccountId\":\"AccountId\",\"Partner.Reports.Accounts.ColumnLabel.Balance\":\"Balance\",\"Partner.Reports.Accounts.ColumnLabel.Created\":\"Created\",\"Partner.Reports.Accounts.ColumnLabel.Credit\":\"Credit\",\"Partner.Reports.Accounts.ColumnLabel.Currency\":\"Currency\",\"Partner.Reports.Accounts.ColumnLabel.Deposits\":\"Deposits\",\"Partner.Reports.Accounts.ColumnLabel.Equity\":\"Equity\",\"Partner.Reports.Accounts.ColumnLabel.Platform\":\"Platform\",\"Partner.Reports.Accounts.ColumnLabel.PnL\":\"PnL\",\"Partner.Reports.Accounts.ColumnLabel.Profit\":\"Profit\",\"Partner.Reports.Accounts.ColumnLabel.Rewards\":\"Rewards\",\"Partner.Reports.Accounts.ColumnLabel.Trades\":\"Trades\",\"Partner.Reports.Accounts.ColumnLabel.Vol.lots\":\"Vol.lots\",\"Partner.Reports.Accounts.ColumnLabel.Withdrawals\":\"Withdrawals\",\"Partner.Pending.Description\":\"Pending.Description\",\"Partner.Pending.PageTitle\":\"IB Room\",\"Partner.Reports.Accounts.PageTitle\":\"Accounts\",\"Partner.Reports.Accounts.Placeholder.AccountId\":\"Account ID\",\"Partner.Reports.Accounts.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Acquisitions.Button.HideUtmFilters\":\"Hide Utm Filters\",\"Partner.Reports.Acquisitions.Button.ShowUtmFilters\":\"Show Utm Filters\",\"Partner.Reports.Acquisitions.ColumnLabel.ClientId\":\"Client ID\",\"Partner.Reports.Acquisitions.ColumnLabel.Country\":\"Country\",\"Partner.Reports.Acquisitions.ColumnLabel.Date\":\"Date\",\"Partner.Reports.Acquisitions.ColumnLabel.IpAddress\":\"IP Address\",\"Partner.Reports.Acquisitions.ColumnLabel.Link\":\"Link\",\"Partner.Reports.Acquisitions.ColumnLabel.Referrer\":\"Referrer\",\"Partner.Reports.Acquisitions.PageTitle\":\"Acquisitions\",\"Partner.Reports.Acquisitions.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Acquisitions.Placeholder.CountryCode\":\"Country\",\"Partner.Reports.Acquisitions.Placeholder.LandingPage\":\"Landing Page\",\"Partner.Reports.Acquisitions.Placeholder.Referrer\":\"Referrer\",\"Partner.Reports.ClientDetails.Button.ClientDetails\":\"Client Details\",\"Partner.Reports.ClientDetails.PersonalData.ClientId\":\"Client ID\",\"Partner.Reports.ClientDetails.PersonalData.CountryOfResidence\":\"Country Of Residence\",\"Partner.Reports.ClientDetails.PersonalData.Email\":\"Email\",\"Partner.Reports.ClientDetails.PersonalData.Level\":\"Level\",\"Partner.Reports.ClientDetails.PersonalData.Name\":\"Name\",\"Partner.Reports.ClientDetails.Tabs.Accounts\":\"Accounts\",\"Partner.Reports.ClientDetails.Tabs.PersonalData\":\"Personal Data\",\"Partner.Reports.ClientDetails.Tabs.Rewards\":\"Rewards\",\"Partner.Reports.ClientDetails.Tabs.SubClients\":\"Client List\",\"Partner.Reports.Clients.Accounts.ColumnLabel.AccountId\":\"Account ID\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Balance\":\"Balance\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Created\":\"Created\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Credit\":\"Credit\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Currency\":\"Currency\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Deposits\":\"Deposits\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Equity\":\"Equity\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Platform\":\"Platform\",\"Partner.Reports.Clients.Accounts.ColumnLabel.PnL\":\"PnL\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Profit\":\"Profit\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Rewards\":\"Rewards\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Trades\":\"Trades\",\"Partner.Reports.Clients.Accounts.ColumnLabel.VolumeLots\":\"Volume Lots\",\"Partner.Reports.Clients.Accounts.ColumnLabel.Withdrawals\":\"Withdrawals\",\"Partner.Reports.Clients.Accounts.Placeholder.ClientId\":\"Account ID\",\"Partner.Reports.Clients.Accounts.Placeholder.OpenedDate\":\"Opened Date\",\"Partner.Reports.Clients.ColumnLabel.ClientId\":\"Client ID\",\"Partner.Reports.Clients.ColumnLabel.Clients\":\"Clients\",\"Partner.Reports.Clients.ColumnLabel.Country\":\"Country\",\"Partner.Reports.Clients.ColumnLabel.Date\":\"Date\",\"Partner.Reports.Clients.ColumnLabel.Email\":\"Email\",\"Partner.Reports.Clients.ColumnLabel.Name\":\"Name\",\"Partner.Reports.Clients.ColumnLabel.Rewards\":\"Rewards\",\"Partner.Reports.Clients.ColumnLabel.TradeExecutionTime\":\"Trade Execution Time\",\"Partner.Reports.Clients.ColumnLabel.TradingVolume\":\"Vol., lots\",\"Partner.Reports.Clients.PageTitle\":\"Clients\",\"Partner.Reports.Clients.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Clients.Placeholder.CountryCode\":\"Country\",\"Partner.Reports.Clients.Placeholder.SubIb\":\"SubIb\",\"Partner.Reports.Clients.Rewards.ColumnLabel.AccountId\":\"Account ID\",\"Partner.Reports.Clients.Rewards.ColumnLabel.ClientId\":\"Client ID\",\"Partner.Reports.Clients.Rewards.ColumnLabel.Level\":\"Level\",\"Partner.Reports.Clients.Rewards.ColumnLabel.Platform\":\"Platform\",\"Partner.Reports.Clients.Rewards.ColumnLabel.RewardAmount\":\"Reward Amount\",\"Partner.Reports.Clients.Rewards.ColumnLabel.Side\":\"Side\",\"Partner.Reports.Clients.Rewards.ColumnLabel.Symbol\":\"Symbol\",\"Partner.Reports.Clients.Rewards.ColumnLabel.TradeExecTime\":\"TradeExecTime\",\"Partner.Reports.Clients.Rewards.ColumnLabel.TradeId\":\"Trade ID\",\"Partner.Reports.Clients.Rewards.ColumnLabel.TransactionId\":\"Transaction ID\",\"Partner.Reports.Clients.Rewards.ColumnLabel.VolumeLots\":\"Volume Lots\",\"Partner.Reports.Clients.Rewards.Placeholder.AccountId\":\"Account ID\",\"Partner.Reports.Clients.Rewards.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Clients.Rewards.Placeholder.Level\":\"Level\",\"Partner.Reports.Clients.Rewards.Placeholder.Symbol\":\"Symbol\",\"Partner.Reports.Clients.Rewards.Placeholder.TradeExecutionTime\":\"Trade Execution Time\",\"Partner.Reports.Clients.Rewards.Placeholder.TradeId\":\"Trade ID\",\"Partner.Reports.Clients.Rewards.Placeholder.TransactionId\":\"Transaction ID\",\"Partner.Reports.Clients.TotalClients\":\"Total Clients\",\"Partner.Reports.Deposits.ColumnLabel.Account\":\"Account\",\"Partner.Reports.Deposits.ColumnLabel.Amount\":\"Amount\",\"Partner.Reports.Deposits.ColumnLabel.Currency\":\"Currency\",\"Partner.Reports.Deposits.ColumnLabel.Date\":\"Date\",\"Partner.Reports.Deposits.ColumnLabel.Id\":\"Id\",\"Partner.Reports.Deposits.PageTitle\":\"Page Title\",\"Partner.Reports.Deposits.Placeholder.Account\":\"Account\",\"Partner.Reports.Deposits.Placeholder.Date\":\"Date\",\"Partner.Reports.Deposits.Placeholder.PlatformUniqueValue\":\"Platform Unique Value\",\"Partner.Reports.Rewards.ColumnLabel.AccountId\":\"Account Id\",\"Partner.Reports.Rewards.ColumnLabel.ClientId\":\"Client Id\",\"Partner.Reports.Rewards.ColumnLabel.Level\":\"Level\",\"Partner.Reports.Rewards.ColumnLabel.Platform\":\"Platform\",\"Partner.Reports.Rewards.ColumnLabel.RewardAmount\":\"Reward Amount\",\"Partner.Reports.Rewards.ColumnLabel.Side\":\"Side\",\"Partner.Reports.Rewards.ColumnLabel.Symbol\":\"Symbol\",\"Partner.Reports.Rewards.ColumnLabel.TradeExecTime\":\"Trade exec. time\",\"Partner.Reports.Rewards.ColumnLabel.TradeId\":\"Trade Id\",\"Partner.Reports.Rewards.ColumnLabel.TransactionId\":\"Transaction Id\",\"Partner.Reports.Rewards.ColumnLabel.VolumeLot\":\"Vol.,lots\",\"Partner.Reports.Rewards.PageTitle\":\"Rewards\",\"Partner.Reports.Rewards.Placeholder.AccountId\":\"Account ID\",\"Partner.Reports.Rewards.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Rewards.Placeholder.Level\":\"Level\",\"Partner.Reports.Rewards.Placeholder.Symbol\":\"Symbol\",\"Partner.Reports.Rewards.Placeholder.TradeExecutionTime\":\"Trade execution time\",\"Partner.Reports.Rewards.Placeholder.TradeId\":\"Trade ID\",\"Partner.Reports.Rewards.Placeholder.TransactionId\":\"Transaction ID\",\"Partner.Reports.Shared.Mobile.Button.ShowFilters\":\"Filters\",\"Partner.Reports.Trades.ColumnLabel.AccountId\":\"Account ID\",\"Partner.Reports.Trades.ColumnLabel.ClientId\":\"Client ID\",\"Partner.Reports.Trades.ColumnLabel.Commission\":\"Commission\",\"Partner.Reports.Trades.ColumnLabel.Email\":\"Email\",\"Partner.Reports.Trades.ColumnLabel.Platform\":\"Platform\",\"Partner.Reports.Trades.ColumnLabel.Position\":\"Position\",\"Partner.Reports.Trades.ColumnLabel.PositionId\":\"Position ID\",\"Partner.Reports.Trades.ColumnLabel.Price\":\"Price\",\"Partner.Reports.Trades.ColumnLabel.Profit\":\"Profit\",\"Partner.Reports.Trades.ColumnLabel.Side\":\"Side\",\"Partner.Reports.Trades.ColumnLabel.Swap\":\"Swap\",\"Partner.Reports.Trades.ColumnLabel.TradeExecutionTime\":\"Trade Execution Time\",\"Partner.Reports.Trades.ColumnLabel.TradeId\":\"Trade ID\",\"Partner.Reports.Trades.ColumnLabel.Volume\":\"Volume\",\"Partner.Reports.Trades.FilterOption.Side.Buy\":\"Buy\",\"Partner.Reports.Trades.FilterOption.Side.Sell\":\"Sell\",\"Partner.Reports.Trades.PageTitle\":\"Trades\",\"Partner.Reports.Trades.Placeholder.AccountId\":\"Account ID\",\"Partner.Reports.Trades.Placeholder.ClientId\":\"Client ID\",\"Partner.Reports.Trades.Placeholder.Email\":\"Email\",\"Partner.Reports.Trades.Placeholder.PositionId\":\"Position ID\",\"Partner.Reports.Trades.Placeholder.Side\":\"Side\",\"Partner.Reports.Trades.Placeholder.TradeId\":\"Trade ID\",\"Partner.Reports.Transactions.ColumnLabel.Amount\":\"Amount\",\"Partner.Reports.Transactions.ColumnLabel.Date\":\"Date\",\"Partner.Reports.Transactions.ColumnLabel.Rewards\":\"Rewards\",\"Partner.Reports.Transactions.ColumnLabel.TransactionId\":\"Transaction ID\",\"Partner.Reports.Transactions.ColumnLabel.WalletId\":\"Wallet ID\",\"Partner.Reports.Transactions.PageTitle\":\"Transactions\",\"Partner.Reports.Transactions.Placeholder.AccountNumber\":\"Wallet ID\",\"Partner.Reports.Transactions.Placeholder.TransactionId\":\"Transaction ID\",\"Partner.Reports.Transactions.TotalTransactions\":\"Transactions\",\"Partner.Reports.Withdrawals.ColumnLabel.Account\":\"Account\",\"Partner.Reports.Withdrawals.ColumnLabel.Amount\":\"Amount\",\"Partner.Reports.Withdrawals.ColumnLabel.Currency\":\"Currency\",\"Partner.Reports.Withdrawals.ColumnLabel.Date\":\"Date\",\"Partner.Reports.Withdrawals.ColumnLabel.Id\":\"Id\",\"Partner.Reports.Withdrawals.PageTitle\":\"Withdrawals\",\"Partner.Reports.Withdrawals.Placeholder.Account\":\"Account\",\"Partner.Reports.Withdrawals.Placeholder.Date\":\"Date\",\"Partner.Reports.Withdrawals.Placeholder.PlatformUniqueValue\":\"Platform Unique Value\",\"Partner.Shared.Button.ApplyFilters\":\"Apply Filters\",\"Partner.Shared.Button.ResetFilters\":\"Reset Filters\",\"Partner.Shared.EmptyListMessage\":\"No Data\",\"Partner.Shared.Filters.Title\":\"Title\",\"Partner.Shared.Pagination.Of\":\"Of\",\"Partner.Shared.Pagination.Page\":\"Page\",\"Partner.Shared.Pagination.ShowOnPage\":\"Show On Page\",\"Partner.Shared.Placeholder.DateFrom\":\"DateFrom\",\"Partner.Shared.Placeholder.DateRange\":\"Date Range\",\"Partner.Shared.Placeholder.DateTo\":\"DateTo\",\"Partner.Shared.Placeholder.ResetValue\":\"None\",\"Partner.Shared.Placeholder.SubIbLevel.First\":\"Yes\",\"Partner.Shared.Placeholder.SubIbLevel.Null\":\"No\",\"Partner.Shared.UTMParameters.Placeholder.CampaignName\":\"Campaign Name\",\"Partner.Shared.UTMParameters.Placeholder.Content\":\"Content\",\"Partner.Shared.UTMParameters.Placeholder.Medium\":\"Medium\",\"Partner.Shared.UTMParameters.Placeholder.Source\":\"Source\",\"Partner.Shared.UTMParameters.Placeholder.Term\":\"Term\",\"PartnerBoard.Widgets.Wallet.SeeTransactions\":\"See Transactions\",\"PartnerBoard.Widgets.Wallet.TotalRewards\":\"Total Rewards\",\"PartnerBoard.Widgets.Wallet.WithdrawFunds\":\"Withdraw Funds\",\"Passport\":\"Passport\",\"Password\":\"Password\",\"Password confirmation\":\"Password confirmation\",\"PayPal account ID\":\"PayPal account ID\",\"Payment amount\":\"Payment amount\",\"Payment currency\":\"Payment currency\",\"Payment.BankRequisites.Id\":\"ID\",\"Payment.Button.GoHome\":\"Go Home\",\"Payment.Button.GoToHomePage\":\"Go to home page\",\"Payment.Button.Proceed \":\"Proceed\",\"Payment.DescriptionInfo\":\"about deposit\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Balance\":\"Balance\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Currency\":\"Currency\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.CurrentPnL\":\"Current PnL\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Equity\":\"Equity\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.FeesPaid\":\"Paid Fees\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.FeesPlan\":\"Fee Plan\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Floating\":\"Floating\",\"Payment.NoWalletsMessage\":\"No Wallets Available\",\"Payment.PaymentDetails.Title\":\"Payment Details\",\"Payment.SelectGroupCurrency.Title\":\"Select Group\",\"Payment.SelectMethod.Title\":\"Select Method\",\"Payment.SelectType.Title\":\"Select Type\",\"Payout.Wizards.GoogleConfirmation.Confirm\":\"Verify\",\"Payout.Wizards.GoogleConfirmation.Description\":\"We’ve sent you 6-digit security code to your<font color=\\\"#0AAD89\\\"> Google Autehnticator app</font>. Please enter your code below\",\"Payout.Wizards.GoogleConfirmation.GoBack\":\"Cancel\",\"Payout.Wizards.GoogleConfirmation.Title\":\"Please enter 2FA code\",\"Payout.Wizards.Phone.Description\":\"Phone\",\"Payout.Wizards.Phone.Title\":\"Phone\",\"Payout.Wizards.Withdraw.Button.GoHome\":\"Go Home\",\"Payout.Wizards.Withdraw.Button.GoToHomePage\":\"Go to home page\",\"Payout.Wizards.Withdraw.Button.Proceed\":\"Proceed\",\"Payout.Wizards.Withdraw.Commission\":\"Commission\",\"Payout.Wizards.Withdraw.DescriptionInfo\":\"Description\",\"Payout.Wizards.Withdraw.DoNotAskAgain\":\"Do Not Ask Again\",\"Payout.Wizards.Withdraw.FinalAmount\":\"Final Amount\",\"Payout.Wizards.Withdraw.GoBack\":\"Go Back\",\"Payout.Wizards.Withdraw.IndicativeAmount\":\"Indicative Amount\",\"Payout.Wizards.Withdraw.Message\":\"This is an example of the Demo <a href=\\\"https://my.example.net/trade/platform/MetaTrader4\\\">description link</a>.\",\"Payout.Wizards.Withdraw.NoWalletsMessage\":\"No Wallets Available\",\"Payout.Wizards.Withdraw.Patterns.NewPatternName\":\"New Pattern Name\",\"Payout.Wizards.Withdraw.Patterns.Placeholder\":\"Place holder\",\"Payout.Wizards.Withdraw.Patterns.SaveDetails\":\"Save Details\",\"Payout.Wizards.Withdraw.Proceed\":\"Proceed\",\"Payout.Wizards.Withdraw.Rate\":\"Rate\",\"Payout.Wizards.Withdraw.SelectGroupCurrency.Title\":\"Select Group\",\"Payout.Wizards.Withdraw.SelectMethod.Title\":\"Select Method\",\"Payout.Wizards.Withdraw.SelectType.Title\":\"Select Type\",\"Payout.Wizards.Withdraw.Title\":\"Do you save details?\",\"Payout.Wizards.Withdraw.WithdrawalAmount\":\"Withdraw Amount\",\"Payout.Wizards.Withdraw.WithdrawalAmount.Placeholder\":\"Withdraw Amount\",\"Pbsr.Briefcase.BriefcaseInfo.AssetInfoCard.Qty\":\"Quantity\",\"Personal\":\"Personal\",\"Personal information\":\"Personal information\",\"Phone\":\"Phone\",\"Phone Confirmation\":\"Phone Confirmation\",\"Phone number\":\"Phone number\",\"Platforms.AccountCard.TransferFundsButton\":\"Transfer funds\",\"Platforms.DxTrade.Title\":\"DXTrade\",\"Platforms.MatchTrader.Title\":\"Match Trader\",\"Platforms.OneZero.AccountParam.Equity\":\"Equity\",\"Platforms.OneZero.AccountParam.FreeMargin\":\"Free Margin\",\"Platforms.OneZero.AccountParam.MarginLevel\":\"Margin Level\",\"Platforms.OneZero.AccountParam.Pnl\":\"Pnl\",\"Platforms.OneZero.AccountParam.UsedMargin\":\"Used Margin\",\"Platforms.OneZero.CardTitle\":\"OZ\",\"Platforms.OneZero.Link1.Label\":\"OneZero Link1\",\"Platforms.OneZero.Link2.Label\":\"OneZero Link2\",\"Platforms.OneZero.Title\":\"OneZero\",\"Platforms.OzPrimeXm.Title\":\"OzPrimeXm\",\"Platforms.PrimeXm.AccountParam.Equity\":\"Equity\",\"Platforms.PrimeXm.AccountParam.FreeMargin\":\"FreeMargin\",\"Platforms.PrimeXm.Title\":\"PrimeXm\",\"Platforms.TradeAccount.Create.IAgreeWithCustomerAgreement [link]\":\"I Agree With <a target='_blank' href='{{link}}'>Customer Agreement</a>\",\"Please check your email for confirmation code\":\"Please check your email for confirmation code\",\"Please log in\":\"Please log in\",\"Please make sure to include the {field} otherwise your transaction will be lost\":\"Please make sure to include the {{field}} otherwise your transaction will be lost\",\"Please scan this QR code\":\"Please scan this QR code\",\"Please, come back later\":\"Please, come back later\",\"Please, enter code was sent to your phone\":\"Please, enter code was sent to your phone\",\"Please, enter your mobile phone number\":\"Please, enter your mobile phone number\",\"Please, select another currency\":\"Please, select another currency\",\"Please, wait for the previous operation\":\"Please, wait for the previous operation\",\"Position id\":\"Position ID\",\"Postal code\":\"Postal code\",\"Prev\":\"Prev\",\"Previous page\":\"Previous Page\",\"Price\":\"Price\",\"PrimeXm\":\"PrimeXm\",\"Proceed\":\"Proceed\",\"Product.Hedging\":\"Hedging\",\"Product.Netting\":\"Netting\",\"Profile.Accreditation.Quiz.Actions.Back\":\"Back\",\"Profile.Accreditation.Quiz.Actions.Cancel\":\"Cancel\",\"Profile.Accreditation.Quiz.Actions.Confirm\":\"Confirm\",\"Profile.Accreditation.Quiz.Actions.Next\":\"Next\",\"Profile.Accreditation.Quiz.Actions.Ok\":\"Ok\",\"Profile.Accreditation.Quiz.BackToTests\":\"Back To Tests\",\"Profile.Accreditation.Quiz.NotAvailable\":\"Not Available\",\"Profile.Accreditation.Quiz.RePass\":\"RePass\",\"Profile.Accreditation.Quiz.SomethingWentWrong\":\"An error occurred. Please try again later.\",\"Profile.Accreditation.Quiz.TestFailed\":\"Test Failed\",\"Profile.Accreditation.Quiz.TestFailedHint\":\"Test Failed Hint\",\"Profile.Accreditation.Quiz.TestPassed\":\"Test Passed\",\"Profile.Accreditation.Quiz.WaitingForApproval\":\"Waiting For Approval\",\"Profile.AccreditationCard.Hide\":\"Hide\",\"Profile.AccreditationCard.Info\":\"To access high-risk instruments and operations, testing is required for unqualified investors:\\noperations,\\ntesting,\\nhigh-risk.\\nTo access high-risk instruments and operations, testing is required for unqualified investors.\",\"Profile.AccreditationCard.More\":\"Show more\",\"Profile.AccreditationCard.NoTestsFound\":\"No Tests Found\",\"Profile.AccreditationCard.Table.TakeTheTest\":\"Take The Test\",\"Profile.AccreditationCard.Table.TestPassed\":\"Test Passed\",\"Profile.AccreditationCard.Table.TestPending\":\"Test Pending\",\"Profile.AccreditationCard.Title\":\"Accreditation\",\"Profile.Finance.Title\":\"Finance\",\"Profile.PayoutWhiteList.Dialog.Add.Wizards.Phone.Description\":\"Description\",\"Profile.PayoutWhiteList.Dialog.Add.Wizards.Phone.Title\":\"Phone\",\"Profile.PayoutWhiteList.Dialog.Delete.Dialog.Title\":\"Delete\",\"Profile.PayoutWhiteList.Dialog.Delete.Wizards.Phone.Description\":\"Description\",\"Profile.PayoutWhiteList.Dialog.Delete.Wizards.Phone.Title\":\"Phone\",\"Profile.PayoutWhiteList.WhiteList.PayoutWhiteList.SwitchOff.Message.Success\":\"Success\",\"Profile.PayoutWhiteList.WhiteList.TwoFaDisable.Wizards.Phone.Description\":\"Description\",\"Profile.PayoutWhiteList.WhiteList.TwoFaDisable.Wizards.Phone.Title\":\"hone\",\"Profile.PersonalInfo.ContactInfo.AdditionalContacts\":\"Additional Contacts\",\"Profile.PersonalInfo.ContactInfo.Email\":\"Email\",\"Profile.PersonalInfo.ContactInfo.Fax\":\"Fax\",\"Profile.PersonalInfo.ContactInfo.Phone\":\"Phone\",\"Profile.PersonalInfo.DocumentsAndAddresses.ActualAddress\":\"Actual Address\",\"Profile.PersonalInfo.DocumentsAndAddresses.AdditionalData\":\"Additional Data\",\"Profile.PersonalInfo.GeneralInfo.GoToTesting\":\"Go To Testing\",\"Profile.PersonalInfo.Title\":\"PersonalInfo Title\",\"Profile.Security.AntiPhishing.Buttons.ChangeCode\":\"Change code\",\"Profile.Security.AntiPhishing.Buttons.CreateCode\":\"Create a Code\",\"Profile.Security.AntiPhishing.Buttons.Proceed\":\"Proceed\",\"Profile.Security.AntiPhishing.Code\":\"Code\",\"Profile.Security.AntiPhishing.CodeRequirements\":\"Code requirements\",\"Profile.Security.AntiPhishing.Description\":\"This is an identification code that users set to prevent damage caused by phishing emails by manually setting an anti-phishing code to distinguish official emails from phishing emails\",\"Profile.Security.AntiPhishing.EnterAuthenticationType\":\"Choose Authentication Type\",\"Profile.Security.AntiPhishing.EnterCodeTextGoogle\":\"Enter 2FA Code from Google Authenticator app\",\"Profile.Security.AntiPhishing.EnterCodeTextSMS\":\"Enter Code from SMS\",\"Profile.Security.AntiPhishing.Inactive\":\"Inactive\",\"Profile.Security.AntiPhishing.Messages.CodeAdded\":\"Code added\",\"Profile.Security.AntiPhishing.Messages.CodeRemoved\":\"Code removed\",\"Profile.Security.AntiPhishing.Title\":\"Anti-Phishing Code\",\"Profile.Security.DeviceManagement.DeviceRemoved\":\"Device was removed\",\"Profile.Security.DeviceManagement.NoDevices\":\"No Devices\",\"Profile.Security.DeviceManagement.Table.Browser\":\"Browser\",\"Profile.Security.DeviceManagement.Table.Date\":\"Date\",\"Profile.Security.DeviceManagement.Table.Device\":\"Device\",\"Profile.Security.DeviceManagement.Table.IP\":\"IP\",\"Profile.Security.DeviceManagement.Table.LastLogin\":\"Last Login\",\"Profile.Security.DeviceManagement.Table.Location\":\"Location\",\"Profile.Security.DeviceManagement.Table.OS\":\"OS\",\"Profile.Security.DeviceManagement.Table.RemoveDevice\":\"Remove Device\",\"Profile.Security.DeviceManagement.Table.TimeZone\":\"Timezone\",\"Profile.Security.DeviceManagement.Title\":\"Device Management\",\"Profile.Security.Title\":\"Security\",\"Profile.Security.Whitelist.Add\":\"Add Withdrawal Whitelist\",\"Profile.Security.Whitelist.Dialog.Add.Title\":\"Add Withdrawal Whitelist\",\"Profile.Security.Whitelist.Dialog.CodeWasSendViaEmail\":\"Code was send via e-mail\",\"Profile.Security.Whitelist.Dialog.Disable.Title\":\"Whitelist Disable Confirmation\",\"Profile.Security.Whitelist.Dialog.EnterCode\":\"Enter your code\",\"Profile.Security.Whitelist.Dialog.OnEmailAddress [email]\":\"code was sent via e-mail\\non your address \",\"Profile.Security.Whitelist.Dialog.Proceed\":\"Proceed\",\"Profile.Security.Whitelist.Dialog.Submit\":\"Submit\",\"Profile.Security.Whitelist.NoAddresses\":\"No Addresses\",\"Profile.Security.Whitelist.ShowMore\":\"Show More\",\"Profile.Security.Whitelist.SuccessAdd\":\"Addresses was success add\",\"Profile.Security.Whitelist.SuccessDelete\":\"Addresses was success delete\",\"Profile.Security.Whitelist.Table.Address\":\"Address\",\"Profile.Security.Whitelist.Table.Currency\":\"Currency\",\"Profile.Security.Whitelist.Table.DeleteAddress\":\"Delete Address\",\"Profile.Security.Whitelist.Table.DestinationTag\":\"Destination Tag\",\"Profile.Security.Whitelist.Title\":\"Address Management\",\"Profile.Security.Whitelist.TurnOff\":\"Turn Off Whitelist\",\"Profile.Security.Whitelist.TurnOn\":\"Turn On Whitelist\",\"Profile.Security.WidgetRequire2FA\":\"This widget is only available when enabled Two-factor Authentication\",\"Profile.Settings.AccountSettings.Wizards.Nickname.Button.Cancel\":\"Cancel\",\"Profile.Settings.AccountSettings.Wizards.Nickname.Button.Rename\":\"Rename\",\"Profile.Settings.AccountSettings.Wizards.Nickname.Message.Success\":\"Success\",\"Profile.Settings.AccountSettings.Wizards.Nickname.Placeholder\":\"Placeholder\",\"Profile.Settings.AccountSettings.Wizards.Nickname.Title\":\"Title\",\"Profile.Settings.AccountSettings.Wizards.View.AddNickname\":\"Add Nickname\",\"Profile.Settings.AccountSettings.Wizards.View.ChangeNickname\":\"Change Nickname\",\"Profile.Settings.AccountSettings.Wizards.View.ChangePassword\":\"Change Password\",\"Profile.Settings.AccountSettings.Wizards.View.EMail\":\"EMail\",\"Profile.Settings.AccountSettings.Wizards.View.Id\":\"ID\",\"Profile.Settings.AccountSettings.Wizards.View.Nickname\":\"Nickname\",\"Profile.Settings.AccountSettings.Wizards.View.Password\":\"Password\",\"Profile.Settings.AccountSettings.Wizards.View.Phone\":\"Phone\",\"Profile.Settings.AccountSettings.Wizards.View.Title\":\"Title\",\"Profile.Settings.AccountSettings.Wizards.View.UploadAvatar.RequestCreated\":\"RequestCreated\",\"Profile.Settings.AccountSettings.Wizards.View.UploadAvatar.Success\":\"Avatar was successfully uploaded\",\"Profile.Settings.CustomCommissions.MakerFee\":\"Maker Fee\",\"Profile.Settings.CustomCommissions.Market\":\"Market\",\"Profile.Settings.CustomCommissions.TakerFee\":\"Taker Fee\",\"Profile.Settings.CustomCommissions.Title\":\"Custom Commissions\",\"Profile.Settings.DeleteAccount.Message.AccountSuccessfullyDeleted\":\"Your account was successfully deleted\",\"Profile.Settings.DeleteAccount.Wizards.Email.Description\":\"Description\",\"Profile.Settings.DeleteAccount.Wizards.Email.Title\":\"Confirmation code sent to email\",\"Profile.Settings.DeleteAccount.Wizards.View.DeleteButton\":\"Delete Your Account\",\"Profile.Settings.DeleteAccount.Wizards.View.Description\":\"We’ll delete your personal data from QAstand account services within 30 days, except in a few cases where required for legitimate business or legal purposes.\",\"Profile.Settings.DeleteAccount.Wizards.View.Title\":\"Delete Account\",\"Profile.Settings.Password.Wizards.Email.Button.Cancel\":\"Cancel\",\"Profile.Settings.Password.Wizards.Email.Button.Send\":\"Send\",\"Profile.Settings.Password.Wizards.Email.Description\":\"Description\",\"Profile.Settings.Password.Wizards.Email.Title\":\"Email\",\"Profile.Settings.Password.Wizards.Email.Warning\":\"Warning\",\"Profile.Settings.Password.Wizards.Password.Button.Cancel\":\"Cancel\",\"Profile.Settings.Password.Wizards.Password.Button.Change\":\"Change\",\"Profile.Settings.Password.Wizards.Password.Description\":\"Description\",\"Profile.Settings.Password.Wizards.Password.Title\":\"Title\",\"Profile.Settings.Password.Wizards.Password.Warning\":\"Warning\",\"Profile.Settings.Tr.Verification.Level.GoToUpgrade\":\"Upgrade level\",\"Profile.Settings.Verification.Button.Cancel\":\"Cancel\",\"Profile.Settings.Verification.Button.Send\":\"Send\",\"Profile.Settings.Verification.Document.Button.View\":\"View\",\"Profile.Testing.TestingCardTitle\":\"Testing Card Title\",\"Profile.Testing.Title\":\"Testing\",\"Profile.VerificationCard.Title\":\"VerificationCard\",\"Profit\":\"Profit\",\"Proof of ID\":\"Proof of ID\",\"Public.Header.Button.SignIn.Label\":\"SignIn\",\"Public.Header.Button.SignIn.Url\":\"Sign \",\"Public.Header.Button.SignOut.Label\":\"Sign Out\",\"Public.Header.Button.SignUp.Label\":\"Sign Up\",\"Public.Header.Button.SignUp.Url\":\"Sign Up\",\"Public.Header.Button.Template.SignIn\":\"Sign In\",\"Public.Header.Button.Template.SignUp\":\"Sign Up\",\"Public.Header.Logo.Url\":\"Header\",\"Public.NoWidgetsMessage.ClickableText\":\"No Widgets Available\",\"Public.NoWidgetsMessage.Text\":\"No Widgets Available\",\"QR code\":\"QR code\",\"Quantity\":\"Quantity\",\"Quantity Filled\":\"Quantity Filled\",\"Quantity to close\":\"Quantity to close\",\"Quick_Link_Analytics\":\"Analytics\",\"Quick_Link_Bonuses\":\"Bonuses\",\"Quick_Link_Deposit\":\"Deposit\",\"Quick_Link_Exchange\":\"Exchange\",\"Quick_Link_History\":\"History\",\"Quick_Link_Mt4\":\"Mt4\",\"Quick_Link_Mt5\":\"Mt5\",\"Quick_Link_Pamm\":\"PAMM\",\"Quick_Link_Partner\":\"IB\",\"Quick_Link_Payout_White_List\":\"Payout White List\",\"Quick_Link_Security\":\"Security\",\"Quick_Link_Settings\":\"Settings\",\"Quick_Link_Trade\":\"Trade\",\"Quick_Link_Trade_B2Margin\":\"B2Margin\",\"Quick_Link_Transfer\":\"Transfer\",\"Quick_Link_Verification\":\"Verification\",\"Quick_Link_Withdraw\":\"Withdraw\",\"Quote\":\"Quote\",\"REJECTED\":\"Rejected\",\"REPLACING\":\"Replacing\",\"Rate\":\"Rate\",\"Rate:\":\"Rate:\",\"Reason\":\"Reason\",\"Reason: You are blacklisted\":\"Reason: You are blacklisted\",\"Receive time-sensitive authentication codes messaged to your phone\":\"Receive time-sensitive authentication codes messaged to your phone\",\"Receiver account id\":\"Receiver account id\",\"Recommended\":\"Recommended\",\"Refresh page\":\"Refresh page\",\"Refresh the rate\":\"Refresh the rate\",\"Region country\":\"Region Country\",\"Registration successful,\":\"Registration successful,\",\"Rejected\":\"Rejected\",\"Remains\":\"Remains\",\"Remove\":\"Remove\",\"Repeat\":\"Repeat\",\"Request is pending\":\"Request is pending\",\"Request successful sent\":\"Request successful sent\",\"Request successfully sent\":\"Request successfully sent\",\"Requests will be able after\":\"Requests will be able after\",\"Requirements\":\"Requirements\",\"Resend\":\"Resend\",\"Resend in\":\"Resend in\",\"Reset\":\"Reset\",\"Reset Dashboard\":\"Reset Dashboard\",\"Reset password\":\"Reset password\",\"Resolution\":\"Resolution\",\"Restore a password\":\"Restore a password\",\"Return to login\":\"Return to login\",\"SENDING\":\"Sending\",\"STOP\":\"STOP\",\"STOP_LIMIT\":\"Stop Limit\",\"STOP_LOSS_ATTACHED\":\"Stop Loss was attached\",\"STOP_LOSS_MODIFIED\":\"Stop Loss was modified\",\"STOP_LOSS_PENDING\":\"Stop Loss is pending\",\"STOP_LOSS_REJECTED\":\"Stop Loss was rejected\",\"STOP_LOSS_REMOVAL_REJECTED\":\"Stop Loss removal was rejected\",\"STOP_LOSS_REMOVED\":\"Stop Loss was removed\",\"STOP_LOSS_TRIGGERED\":\"Stop Loss triggered\",\"STOP_MARKET\":\"Stop Market\",\"Save\":\"Save\",\"Search\":\"Search\",\"Search by login\":\"Search by login\",\"Search instrument\":\"Search symbol\",\"See KYC requirements here\":\"See KYC requirements <a href='{{link}}' target='_blank'>here</a>\",\"See Reason\":\"See Reason\",\"See Registration Tutorial Video\":\"See Registration Tutorial Video\",\"See all\":\"See all\",\"See status details\":\"Level details\",\"See upload history\":\"See upload history\",\"Select Trading Account\":\"Select Trading Account\\t\",\"Select Trading Account(s)\":\"Select Trading Account(s)\",\"Select account\":\"Select account\",\"Select all\":\"Select all\",\"Select authentication method\":\"Select authentication method\",\"Select currency\":\"Select currency\",\"Select language\":\"Select language\",\"Select your document\":\"Select your document\",\"Selected platform not found\":\"Selected platform not found\",\"Selfie\":\"Selfie\",\"Sell\":\"Sell\",\"Sell / Short\":\"Sell / Short\",\"Sell limit\":\"Sell limit\",\"Sell stop\":\"Sell stop\",\"Send\":\"Send\",\"Send Confirmation Code\":\"Send Confirmation Code\",\"Send a request for registration of a corporate account, and our manager will contact you\":\"Send a request for registration of a corporate account, and our manager will contact you.\",\"Send only amount in {currency} to this deposit address\":\"Send only amount in {{currency}} to this deposit address\",\"Sending any other currency to this address may result in the loss of your deposit\":\"Sending any other currency to this address may result in the loss of your deposit\",\"Set password\":\"Set password\",\"Shared.AvatarUpload.Button.Delete\":\"Delete\",\"Shared.AvatarUpload.Tooltip.ChangeAvatar\":\"Change Avatar\",\"Shared.AvatarUpload.Tooltip.UploadNewAvatar\":\"Upload userpic\",\"Shared.AvatarUploadDialog.Button.Cancel\":\"Cancel\",\"Shared.AvatarUploadDialog.Button.Save\":\"Save\",\"Shared.AvatarUploadDialog.Requirements [valueInPx, valueInMb]\":\"You can upload an image in png and jpg format. Image resolution not less than {{valueInPx}} × {{valueInPx}} pixels, file size not more than {{valueInMb}} MB.\",\"Shared.AvatarUploadDialog.Title.UserpicConfiguration\":\"Userpic Configuration\",\"Shared.AvatarUploadDialog.UploadUserpic\":\"Upload Userpic\",\"Shared.Modules.AccountMenu.AccountType [accountType]\":\"AccountType {{accountType}}\",\"Shared.Modules.AccountMenu.CreateNewAccount [accountType]\":\"Create new {{accountType}} account\",\"Shared.Modules.ToggleView.Title.Tile\":\"Tile\",\"Shared.Pagination.Of [total]\":\"of {{total}}\",\"Shared.Pagination.Page\":\"Page\",\"Show closed ticket\":\"Show closed ticket\",\"Show menu\":\"Show menu\",\"Show more\":\"Show more\",\"Show workspaces\":\"Show workspaces\",\"Side\":\"Side\",\"Sign in\":\"Sign in\",\"Sign in now\":\"Sign in now\",\"Sign in now!\":\"Sign in now!\",\"Sign up\":\"Sign up\",\"Sign up now!\":\"Sign up now!\",\"Simple\":\"Simple\",\"Something went wrong\":\"Something went wrong\",\"Something went wrong, please try again later\":\"Something went wrong, please try again later\",\"Something went wrong, please try later\":\"Something went wrong, please try later\",\"Something went wrong. Please come back later\":\"Something went wrong. Please come back later\",\"Sorry, you won’t be able to verify your account\":\"Sorry, you won’t be able to verify your account\",\"Sorry, you're blocked\":\"Sorry, you're blocked\",\"Sort\":\"Sort\",\"Source Account\":\"Source Account\",\"Source amount\":\"Source amount\",\"Source of funds\":\"Source of funds\",\"Spread\":\"Spread\",\"State\":\"State\",\"Status\":\"Status\",\"Step\":\"Step\",\"Stop\":\"Stop\",\"Stop loss\":\"Stop loss\",\"StopMarket\":\"StopMarket\",\"Subject\":\"Subject\",\"Submit\":\"Submit\",\"Successful transfer\":\"Successful transfer\",\"Swap\":\"Swap\",\"Symbol\":\"Symbol\",\"TAKE_PROFIT_ATTACHED\":\"Take Profit was attached\",\"TAKE_PROFIT_MODIFIED\":\"Take Profit was modified\",\"TAKE_PROFIT_PENDING\":\"Take Profit pending\",\"TAKE_PROFIT_REJECTED\":\"Take Profit rejected\",\"TAKE_PROFIT_REMOVAL_REJECTED\":\"Take Profit removal rejected\",\"TAKE_PROFIT_REMOVED\":\"Take Profit removed\",\"TAKE_PROFIT_TRIGGERED\":\"Take Profit triggered\",\"TRAIL_STOP\":\"Trail Stop\",\"TRAIL_STOP_LIMIT\":\"Trail Stop Limit\",\"TRIGGERED_LIMIT\":\"Triggered Limit\",\"TRIGGERED_STOP\":\"Triggered Stop\",\"Take Profit\":\"Take Profit\",\"Take profit\":\"Take profit\",\"Taker Fee\":\"Taker Fee\",\"Tap to copy\":\"Tap to copy\",\"Tap to open chat\":\"Tap to open chat\",\"Text Message (SMS)\":\"Text Message (SMS)\",\"The document set is too big, please try to use a reduced documents size\":\"The document set is too big, please try to use a reduced documents size\",\"The email has already been taken\":\"This email has already been taken.\",\"The page you are looking for was not found\":\"The page you are looking for was not found\",\"The rate for this pair cannot be found,\":\"The rate for this pair cannot be found,\",\"There is no bonuses to show\":\"There is no bonuses to show\",\"There is no document to show\":\"There is no document to show\",\"Three digits from the back of the card\":\"Three digits from the back of the card\",\"Ticket\":\"Ticket\",\"Ticket.CompleteButton\":\"Complete\",\"Ticket.ReopenedButton\":\"Reopened\",\"Ticket_AwaitingReply\":\"Awaiting Reply\",\"Ticket_Closed\":\"Closed\",\"Ticket_Declined\":\"Resolved\",\"Ticket_Duplicate\":\"Duplicate\",\"Ticket_Get_More_Message\":\"Get More Messages\",\"Ticket_Get_More_Tickets\":\"Get More Tickets\",\"Ticket_InProgress\":\"In Progress\",\"Ticket_Opened\":\"Opened\",\"Ticket_Pending\":\"Pending\",\"Ticket_Resolved\":\"Resolved\",\"Tickets per page\":\"Tickets per Page\",\"Tier Group\":\"Tier Group\",\"Time\":\"Time\",\"Time in Force\":\"Time in Force\",\"Time to fund\":\"Time to funds\",\"To\":\"To\",\"To Account\":\"To Account\",\"To Client ID\":\"To Client ID\",\"To Trading Account\":\"To Trading Account\\t\",\"To confirm it’s you\":\"To confirm it’s you\",\"To currency\":\"To currency\",\"Today\":\"Today\",\"Total\":\"Total\",\"Trade\":\"Trade\",\"Trade.List.Button.Favorite.Tooltip.Add\":\"Add\",\"Trade.List.Button.Favorite.Tooltip.Remove\":\"Remove\",\"TradeAccountDetails.ChangePassword.CheckEmailMessage\":\"Check Email Message\",\"TradeAccountDetails.ChangePassword.EnterPassword.Placeholder.Password\":\"Password\",\"TradeAccountDetails.ChangePassword.EnterPassword.Placeholder.RepeatPassword\":\"RepeatPassword\",\"TradeAccountDetails.ChangePassword.EnterPassword.Proceed\":\"Proceed\",\"TradeAccountDetails.ChangePassword.EnterPassword.Title\":\"Change Password\",\"TradeAccountDetails.ChangePassword.PasswordCreatedMessage\":\"Password Created Message\",\"TradeAccountDetails.ChangePassword.SecondType.Body\":\"Body\",\"TradeAccountDetails.ChangePassword.SecondType.Option.InvestorPassword\":\"Investor Password\",\"TradeAccountDetails.ChangePassword.SecondType.Option.MasterPassword\":\"Master Password\",\"TradeAccountDetails.ChangePassword.SelectType.Body\":\"Body\",\"TradeAccountDetails.ChangePassword.SelectType.Cancel\":\"Cancel\",\"TradeAccountDetails.ChangePassword.SelectType.Option.Manual\":\"Manual\",\"TradeAccountDetails.ChangePassword.SelectType.Option.Random\":\"Random\",\"TradeAccountDetails.ChangePassword.SelectType.Option.Random.SubText\":\"recommended\",\"TradeAccountDetails.ChangePassword.SelectType.Proceed\":\"Proceed\",\"TradeAccountDetails.ChangePassword.SelectType.Title\":\"Change Password\",\"TradeAccountDetails.ChangePassword.SomethingWentWrong\":\"Something Went Wrong\",\"TradeAccountDetails.ChangePassword.UnknownError\":\"Unknown Error\",\"TradeAccountDetails.ChangePasswordDialog.EmailCode.Proceed\":\"Proceed\",\"TradeAccountDetails.ChangePasswordDialog.EmailCode.SubText1\":\"SubText1\",\"TradeAccountDetails.ChangePasswordDialog.EmailCode.SubText2\":\"SubText2\",\"TradeAccountDetails.ChangePasswordDialog.EmailCode.Title\":\"Change Password Dialog\",\"Traded Volume (Last 30 days)\":\"Traded Volume (Last 30 days)\",\"Traded lots\":\"Traded lots\",\"Trading Account Details\":\"Trading Account Details\\t\",\"Trading Account Opening Request successfully sent\":\"Trading Account Opening Request successfully sent\\t\",\"Trading Fee Tier\":\"Trading Fee Tier\",\"Trading is not available\":\"Trading is not available\\t\",\"Trading platforms\":\"Trading platforms\",\"Trading will resume in\":\"Trading will resume in\",\"TradingBoard.B2TraderAuth.Public.UnauthorizedMessage\":\"TradingBoard.B2TraderAuth.Public.UnauthorizedMessageTradingBoard.B2TraderAuth.Public.UnauthorizedMessage\",\"TradingBoard.CancelOrderConfirmationDialogComponent.Back\":\"Back\",\"TradingBoard.CancelOrderConfirmationDialogComponent.Message [order]\":\"Message {{order}}\",\"TradingBoard.CancelOrderConfirmationDialogComponent.Remove\":\"Remove\",\"TradingBoard.CancelOrderConfirmationDialogComponent.Title\":\"Title\",\"TradingBoard.Components.B2traderIntroduction.WelcomeMessage\":\"Welcome to Trading Board!\",\"TradingBoard.Components.ConfirmClose.Cancel\":\"No\",\"TradingBoard.Components.ConfirmClose.Close\":\"Yes\",\"TradingBoard.Components.Dialog.ModifyOrder.Button.Cancel\":\"Cancel\",\"TradingBoard.Components.Dialog.ModifyOrder.Button.ModifyOrder\":\"Submit\",\"TradingBoard.Components.Dialog.ModifyOrder.CurrentPrice\":\"Current price\",\"TradingBoard.Components.Dialog.ModifyOrder.LastModified\":\"Last modified\",\"TradingBoard.Components.Dialog.ModifyOrder.LimitPrice\":\"Limit price\",\"TradingBoard.Components.Dialog.ModifyOrder.LotSize\":\"Lots\",\"TradingBoard.Components.Dialog.ModifyOrder.MarginImpact\":\"Required margin\",\"TradingBoard.Components.Dialog.ModifyOrder.OpenedAt\":\"Created\",\"TradingBoard.Components.Dialog.ModifyOrder.OrderId\":\"Order ID\",\"TradingBoard.Components.Dialog.ModifyOrder.OrderType\":\"Order\",\"TradingBoard.Components.Dialog.ModifyOrder.OrderType.Limit\":\"Limit\",\"TradingBoard.Components.Dialog.ModifyOrder.OrderType.Stop\":\"Stop\",\"TradingBoard.Components.Dialog.ModifyOrder.Side\":\"Side\",\"TradingBoard.Components.Dialog.ModifyOrder.StopPrice\":\"Stop price\",\"TradingBoard.Components.Dialog.ModifyOrder.Title\":\"Modify Order\",\"TradingBoard.Components.Dialog.OrderWindow.Button.Cancel\":\"Cancel\",\"TradingBoard.Components.Dialog.OrderWindow.Button.SendOrder\":\"Submit\",\"TradingBoard.Components.Dialog.OrderWindow.Buy\":\"Buy\",\"TradingBoard.Components.Dialog.OrderWindow.CurrentPrice\":\"Current price\",\"TradingBoard.Components.Dialog.OrderWindow.LimitPrice\":\"Limit price\",\"TradingBoard.Components.Dialog.OrderWindow.LotSize\":\"Lots\",\"TradingBoard.Components.Dialog.OrderWindow.MarginImpact\":\"Required margin\",\"TradingBoard.Components.Dialog.OrderWindow.OrderType\":\"Order\",\"TradingBoard.Components.Dialog.OrderWindow.OrderType.Limit\":\"Limit\",\"TradingBoard.Components.Dialog.OrderWindow.OrderType.Market\":\"Market\",\"TradingBoard.Components.Dialog.OrderWindow.OrderType.Stop\":\"Stop\",\"TradingBoard.Components.Dialog.OrderWindow.Sell\":\"Sell\",\"TradingBoard.Components.Dialog.OrderWindow.Side\":\"Side\",\"TradingBoard.Components.Dialog.OrderWindow.StopPrice\":\"Stop price\",\"TradingBoard.Components.Dialog.OrderWindow.Title\":\"New Order\",\"TradingBoard.Components.Dialog.PopupMessage.DoneButton\":\"Done\",\"TradingBoard.Components.Dialog.Position.Button.Cancel\":\"Cancel\",\"TradingBoard.Components.Dialog.Position.CurrentPrice\":\"Current price\",\"TradingBoard.Components.Dialog.Position.FillPrice\":\"Fill price\",\"TradingBoard.Components.Dialog.Position.LastModified\":\"Last modified\",\"TradingBoard.Components.Dialog.Position.Leverage\":\"Leverage\",\"TradingBoard.Components.Dialog.Position.LotSize\":\"Lots\",\"TradingBoard.Components.Dialog.Position.OpenedAt\":\"Created\",\"TradingBoard.Components.Dialog.Position.Pl\":\"PnL\",\"TradingBoard.Components.Dialog.Position.PositionId\":\"Position ID\",\"TradingBoard.Components.Dialog.Position.Side\":\"Side\",\"TradingBoard.Components.DialogOrderWindow.Duration.DateLabel\":\"Day/Month/Year\",\"TradingBoard.Components.DialogOrderWindow.Duration.DurationTitle\":\"Duration\",\"TradingBoard.Components.DialogOrderWindow.Duration.TimeLabel\":\"Time\",\"TradingBoard.Components.DialogOrderWindow.LeverageRange.LeverageMaxLabel\":\"max\",\"TradingBoard.Components.DialogOrderWindow.LeverageRange.LeverageMinLabel\":\"min\",\"TradingBoard.Components.Navigation.IntroductionMessage\":\"Navigation bar for quick access to exchange options/UI elements\",\"TradingBoard.Components.Navigation.IntroductionTitle\":\"Sidebar Menu\",\"TradingBoard.Components.Navigation.Logout\":\"Log Out\",\"TradingBoard.Components.ProtectionOrders.ProjectedLoss.Label [alpha]\":\"Projected loss, {{alpha}}\",\"TradingBoard.Components.ProtectionOrders.ProjectedProfit.Label [alpha]\":\"Projected profit, {{alpha}}\",\"TradingBoard.Components.ProtectionOrders.StopLossPrice.Label\":\"Stop-loss\",\"TradingBoard.Components.ProtectionOrders.TakeProfitPrice.Label\":\"Take-profit\",\"TradingBoard.Components.ProtectionOrders.Title\":\"Protection Orders\",\"TradingBoard.Components.PublicLogin.EmailPlaceholder\":\"Enter your email\",\"TradingBoard.Components.PublicLogin.InvalidEmail\":\"Invalid email\",\"TradingBoard.Components.PublicLogin.PasswordPlaceholder\":\"Enter your password\",\"TradingBoard.Components.PublicLogin.SignInSubtitle\":\"SignIn\",\"TradingBoard.Components.PublicLogin.SignInTitle\":\"SignIn Title\",\"TradingBoard.Components.SelectInstrument.List.NewInstrument\":\"New Instrument\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.Back\":\"Back\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.BondMarket\":\"Bond\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.Choose\":\"Choose\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.CurrencyMarket\":\"Currency\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.FindBy\":\"Find By \",\"TradingBoard.Components.Warp.SelectInstrumentDialog.NoInstrumentsFound\":\"No Instruments Found\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.StockMarket\":\"Stock\",\"TradingBoard.Components.Warp.SelectInstrumentDialog.Title\":\"Select Instrument\",\"TradingBoard.Consts.B2Trader.AccountInfo.IntroductionMessage\":\"\",\"TradingBoard.Consts.B2Trader.Assets.IntroductionMessage\":\"This widget displays the list of all your assets along with the amount of total and available assets in each currency: \\n- The total assets indicate the overall amount of assets available in your wallet\\n- The available assets indicate the assets remaining at your disposal, meaning the difference between your total assets and a sum of all limit orders placed by you by this time (Available = Total - Pending Orders)\\nYou can hide zero balance assets from the list using a corresponding check box.\\nTo increase the amount of assets available to you in a specific currency, you can add funds to your Wallet or exchange one asset for another using the Simple Exchange widget.\",\"TradingBoard.Consts.B2Trader.Banners.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.Depth.IntroductionMessage\":\"Using this widget you can assess the current market depth indicating the market liquidity of an asset evaluated based on the number of open orders to buy and sell it at various price levels.\\nThe widget displays a chart indicating the overall volume of buy (green) and sell (red) orders at various price levels awaiting execution at the moment. You can hover the mouse pointer over the chart to learn the exact price and volume of an asset traded at a specific price level.\\nTo view data for a specific market, click the X button on the menu displaying the selected currency pair and choose a desired market. Alternatively, you can quickly switch between currency pairs using the Favorite Markets widget.\\nAnother crucial metric enabling you to assess a market for a given asset is its bid-ask spread which you can monitor using the Order Book widget.\",\"TradingBoard.Consts.B2Trader.ExchangeTime.IntroductionMessage\":\"Introduction Message\",\"TradingBoard.Consts.B2Trader.Favorites.IntroductionMessage\":\"Using this widget you can switch between preferred currency pairs making the following widgets display information about a selected market: \\n- Market Orders\\n- Limit Orders\\n- Order Book\\n- Trades History\\n- Trading View\\n- Market Depth\\nYou can use the Watch List widget to monitor multiple currency pairs at a time.\",\"TradingBoard.Consts.B2Trader.FilledOrders.IntroductionMessage\":\"IntroductionMessage\",\"iOS\":\"iOS\",\"iOS.ctrader\":\"cTrader\",\"iOS.dxtrade\":\"iOS dxtrade\",\"iOS.match_trader\":\"iOS\",\"iOS.mt4\":\"iOS.mt4\",\"TradingBoard.Consts.B2Trader.InactiveOrders.IntroductionMessage\":\"This widget displays a list of suspended orders that are currently not awaiting execution. \\nDetails about each order are indicated, including the information about a traded currency (instrument), order side (buy or sell), order type, asset quantity and price, as well as current order status.\\nYou can filter the list by order date or use quick filters to show orders for the last three days, last week or last month.\",\"TradingBoard.Consts.B2Trader.Limit.IntroductionMessage\":\"Using this widget you can place a new limit order, meaning an instruction to buy or sell a certain quantity of an asset at the price specified by you. Limit orders are placed in the order book and executed only when the market price hits the limit specified by you. For this reason, limit orders with a limit price significantly different from the current one may never be executed.\\nYou can use limit orders to buy assets at a lower price or sell at a higher price than the current market price.\\nYou can specify a custom order quantity or set a fixed amount by clicking the percentage buttons enabling you to quickly place a sell or buy order in the amount of 25%, 50%, 75% or 100% of funds at your disposal.\",\"TradingBoard.Consts.B2Trader.Market.IntroductionMessage\":\"Using this widget you can place a new market order, meaning an instruction to instantly buy or sell a certain quantity of an asset at the currently best price on the market. Market orders are not placed in the order book and executed or canceled immediately.\\nYou should only use market orders if you want to buy or sell as quickly as possible.\\nYou can specify a custom order quantity or set a fixed amount by clicking the percentage buttons enabling you to quickly place a sell or buy order in the amount of 25%, 50%, 75% or 100% of funds at your disposal.\",\"iOS.mt5\":\"https://www.google.com\",\"if you have problems\":\"if you have problems\",\"info_description_exchange\":\"This is the exchange message !\",\"info_description_internal_transfer\":\"Internal Transfer Description\",\"TradingBoard.Consts.B2Trader.OpenOrders.IntroductionMessage\":\"This widget displays a list of currently opened limit orders placed over a specified time period.\\nDetails about each order are indicated, including the information about a traded currency (instrument), order side (buy or sell), asset quantity, filled quantity and price.\\nYou can filter the list by order date or use quick filters to show orders for the last three days, last week or last month.\",\"TradingBoard.Consts.B2Trader.OrderBook.IntroductionMessage\":\"This widget displays a list of currently open buy and sell limit orders for a selected asset along with the current bid-ask spread value. The Order Book is dynamic and is constantly updated in real time. It provides three different sections displaying the following information:\\n- Open sell orders are highlighted with red and listed in the top section. The best ask bid (the sell order with the lowest price) is displayed at the bottom of this list\\n- Open buy orders are highlighted with green and listed in the bottom section. The best bid (the buy order with the highest price) is displayed at the top of this list\\n- The middle section displays the current bid-ask spread indicating the gap between the best ask and bid prices declared for an asset\\nUsing the +/- buttons on the Grouping menu you can make the widget display adjacent orders on the list with a specified increment (10, 50, 100, etc. from the top-of-the-book order price).\\nTo view data for a specific market, click the X button on the menu displaying the selected currency pair and choose a desired market. Alternatively, you can quickly switch between currency pairs using the Favorite Markets widget.\\nIn addition, you can use the Market Depth widget to evaluate the liquidity of a specific asset based on the overall volume of orders traded at various price levels.\",\"TradingBoard.Consts.B2Trader.PaymentAccounts.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.QuickLimit.IntroductionMessage\":\"Using this widget you can quickly place a new limit order, meaning an instruction to buy or sell a certain quantity of an asset at the price specified by you. \\nChoose a currency pair and specify the base asset amount that you wish to buy or sell at a specific price. Click the button to place the new limit order right away.\\nThe order will be placed in the order book and executed only after the market price hits the specified limit.\",\"TradingBoard.Consts.B2Trader.QuickMarket.IntroductionMessage\":\"Using this widget you can quickly place a new market order, meaning an instruction to instantly buy or sell a certain quantity of an asset at the currently best price on the market.\\nChoose a currency pair and specify the base asset amount that you wish to instantly buy or sell. Click the button to place the new order right away.\\nThe order will not be placed in the order book — it will be executed immediately in the amount that is currently available at the top-of-the-book price (partially or in full). Any portion of a market order that cannot be filled immediately will be canceled.\",\"TradingBoard.Consts.B2Trader.RecentTransactions.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.SimpleExchange.IntroductionMessage\":\"This widget enables you to quickly exchange one asset for another at the current market price.\\nUpon specifying a traded asset, you can select a desired asset for exchange. Enter the quantity you wish to exchange in the From or To field, and the quantity of the exchanged asset will be updated automatically based on the current exchange rate on the market (the exchange ratio displayed in the widget is updated in real time). Alternatively, you can exchange a fixed amount of an asset (10%, 25%, 50% or 100% of the quantity available in your wallet) by clicking a corresponding button.\\nYou can instantly exchange assets if deposit accounts in the specified currencies have been created in your wallet. Otherwise, the submitted exchange operation will be pending until you create a corresponding account, after which the exchanged funds in the selected currencies will be automatically withdrawn and deposited to a new account in a selected currency.\",\"TradingBoard.Models.B2Trader.OrdersManager.Last3Days\":\"Last 3 days\",\"TradingBoard.Models.B2Trader.OrdersManager.Last3Months\":\"Last 3 months\",\"TradingBoard.Models.B2Trader.OrdersManager.LastMonth\":\"Last month\",\"TradingBoard.Models.B2Trader.OrdersManager.LastWeek\":\"Last week\",\"TradingBoard.Consts.B2Trader.StopLimit.IntroductionMessage\":\"Stop Limit Introduction Message\",\"TradingBoard.Consts.B2Trader.StopMarket.IntroductionMessage\":\"Stop Market Introduction Message\",\"TradingBoard.Consts.B2Trader.StopOrders.IntroductionMessage\":\"Introduction Message\",\"TradingBoard.Consts.B2Trader.Support.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.TradersInfo.IntroductionMessage\":\"\",\"TradingBoard.Consts.B2Trader.TradesHistory.IntroductionMessage\":\"This widget provides up-to-date information about all orders executed to date fully and partially on a selected market.\\nSell orders are highlighted with red, buy orders are highlighted with green. The asset price, lot size and creation date are displayed for each order. The most recent order appears at the top of the list.\\nTo view data for a specific market, click the X button on the menu displaying the selected currency pair and choose a desired market. Alternatively, you can quickly switch between currency pairs using the Favorite Markets widget.\",\"TradingBoard.Consts.B2Trader.TradingAccounts.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.TradingAccountsFavorites.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.TradingView.IntroductionMessage\":\"This widget displays a trading chart providing all the essential tools to help you make informed trading decisions. \\nThe chart illustrates fluctuation of prices over a certain time period. The horizontal axis (X-axis) represents the time scale, the vertical axis (Y-axis) indicates the price level. \\nYou can switch between bar, candle, Heikin Ashi, line, area and baseline views, as well as specify the time period for which data should be displayed. Multiple customization options are provided enabling you to set up the chart according to your preferences.\\nThe chart supports numerous financial indicators (such as moving averages and regressions) and a variety of custom shapes (including arrows and lines, pitchforks and various ranges) enabling you to perform an in-depth market analysis.\",\"TradingBoard.Consts.B2Trader.Walkthrough.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.B2Trader.WatchList.IntroductionMessage\":\"Using this widget you can monitor multiple markets at a time. You can add currency pairs to this list or remove them based on your preferences.\\nVarious metrics are displayed and updated in real time for each currency pair, including the last price, change, volume, high and low. Except for the last price, these metrics represent aggregated or average values obtained over the last 24 hours.\\nUpon clicking a row on this list, the following widgets switch to displaying a selected currency pair:\\n- Market Orders\\n- Limit Orders\\n- Order Book\\n- Trades History\\n- Trading View\\n- Market Depth\",\"TradingBoard.Consts.Moex.MyAccounts.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.Consts.Moex.WarpPortfolio.IntroductionMessage\":\"IntroductionMessage\",\"TradingBoard.DialogMoexSelectInstrumentComponent.Back\":\"Back\",\"TradingBoard.DialogMoexSelectInstrumentComponent.BondMarket\":\"Bond\",\"TradingBoard.DialogMoexSelectInstrumentComponent.Choose\":\"Choose\",\"TradingBoard.DialogMoexSelectInstrumentComponent.CurrencyMarket\":\"Currency\",\"TradingBoard.DialogMoexSelectInstrumentComponent.FindBy\":\"FindBy\",\"TradingBoard.DialogMoexSelectInstrumentComponent.NoInstrumentsFound\":\"No Instruments Found\",\"TradingBoard.DialogMoexSelectInstrumentComponent.StockMarket\":\"Stock\",\"TradingBoard.DialogMoexSelectInstrumentComponent.Title\":\"Select Instrument\",\"TradingBoard.EventHandlers.B2Trader.ErrorHandlerService.SomethingWentWrong\":\"The service is temporarily unavailable. Please try again later.\",\"TradingBoard.ExperienceLevelDialog.Button.Begin\":\"Let's Begin\",\"TradingBoard.ExperienceLevelDialog.Button.SkipPage\":\"Skip this page\",\"TradingBoard.ExperienceLevelDialog.Checkbox.RememberChoice\":\"Remember my choice\",\"TradingBoard.ExperienceLevelDialog.Experienced\":\"EXPERIENCED\",\"TradingBoard.ExperienceLevelDialog.Experienced.Description\":\"An experienced participant in the financial market. You have years of experience behind you and hundreds of closed deals\",\"TradingBoard.ExperienceLevelDialog.New\":\"NEW\",\"TradingBoard.ExperienceLevelDialog.New.Description\":\"New to the world of finance and investment. Planning to make your first trades\",\"TradingBoard.ExperienceLevelDialog.Professional\":\"PROFESSIONAL\",\"TradingBoard.ExperienceLevelDialog.Professional.Description\":\"A true professional in his field. Investments are your main source of income\",\"TradingBoard.ExperienceLevelDialog.SubTitle\":\"Based on your selected qualification level we will build an ideal trading workspace for you\",\"TradingBoard.ExperienceLevelDialog.Title\":\"Choose your Qualification Level\",\"TradingBoard.InformationDialog.Ok\":\"Ok\",\"TradingBoard.Models.MoexPlaceOrderForm.ErrorsText.IsRequired\":\"Is Required\",\"TradingBoard.Models.MoexPlaceOrderForm.ErrorsText.MustBeLowerOrEqual [value]\":\"MustBeLowerOrEqual [value]\",\"TradingBoard.Models.MoexPlaceOrderForm.ErrorsText.NotEnoughFunds [value]\":\"NotEnoughFunds [value]\",\"TradingBoard.Models.MoexPlaceOrderForm.ErrorsText.ValueIsInvalid\":\"Invalid\",\"TradingBoard.Models.MoexPlaceOrderForm.ErrorsText.ValueIsZero\":\"Zero\",\"TradingBoard.Models.OrderForm.IncorrectOrderType\":\"Incorrect order type\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.IsRequired\":\"required\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.MustBeHigherOrEqual [value]\":\"must be greater than or equal to {{value}}\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.MustBeHigherThan [value]\":\"must be greater than {{value}}\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.MustBeLowerOrEqual [value]\":\"must be less than or equal to {{value}}\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.MustBeLowerThan [value]\":\"must be less than {{value}}\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.ValueIsInvalid\":\"invalid value\",\"TradingBoard.Models.ProtectionOrderForm.ErrorsText.ValueIsZero\":\"required\",\"TradingBoard.MoexOrderBook.MyLots\":\"My Lots\",\"TradingBoard.MoexOrderBook.OpenSelectInstrumentDialogLabel\":\"Select Instrument\",\"TradingBoard.MoexOrderBook.Price\":\"Price\",\"TradingBoard.MoexOrderBook.Spread\":\"Spread\",\"TradingBoard.MoexOrderBook.Volume\":\"Volume\",\"TradingBoard.MoexOrderBook.Yield\":\"Yield\",\"TradingBoard.Navigation.GeneralSettings\":\"General Settings\",\"TradingBoard.Navigation.GeneralSettings.24HourMode\":\"24HourMode\",\"TradingBoard.Navigation.GetStatementButton\":\"Get Statement \",\"TradingBoard.Navigation.TutorialButton\":\"Launch tutorial\",\"TradingBoard.NoWidgetsMessage.ClickableText\":\"Text\",\"TradingBoard.NoWidgetsMessage.Text\":\"Add first widget\",\"TradingBoard.OrderFilter.DateSelectorLabel\":\"Select Date\",\"TradingBoard.OrderFilter.SelectedDate\":\"Selected Date\",\"TradingBoard.OrdersComponent.ActiveOrders\":\"Active Orders\",\"TradingBoard.OrdersComponent.Commission\":\"Commission\",\"TradingBoard.OrdersComponent.CompletedDeals\":\"Completed Deals\",\"TradingBoard.OrdersComponent.DealPrice\":\"Deal Price\",\"TradingBoard.OrdersComponent.Exchange\":\"Exchange\",\"TradingBoard.OrdersComponent.ISIN\":\"ISIN\",\"TradingBoard.OrdersComponent.Logo\":\"Logo\",\"TradingBoard.OrdersComponent.Lot\":\"Lot\",\"TradingBoard.OrdersComponent.LotQuantity\":\"Lot Quantity\",\"TradingBoard.OrdersComponent.LotSize\":\"Lot Size\",\"TradingBoard.OrdersComponent.Name\":\"Name\",\"TradingBoard.OrdersComponent.NoOrders\":\"No Orders\",\"TradingBoard.OrdersComponent.OrderNumber\":\"Order Number\",\"TradingBoard.OrdersComponent.OutstandingOrders\":\"Outstanding Orders\",\"TradingBoard.OrdersComponent.ParentAccountId\":\"Parent Account Id\",\"TradingBoard.OrdersComponent.PriceValue\":\"Price\",\"TradingBoard.OrdersComponent.RequestType\":\"Type\",\"TradingBoard.OrdersComponent.SetupColumns\":\"Setup Columns\",\"TradingBoard.OrdersComponent.Side\":\"Side\",\"TradingBoard.OrdersComponent.Status\":\"Status\",\"TradingBoard.OrdersComponent.SubAccountId\":\"Sub Account Id \",\"TradingBoard.OrdersComponent.TickerCode\":\"Ticker Code\",\"TradingBoard.OrdersComponent.TotalDealPrice\":\"Total Deal Price\",\"TradingBoard.OrdersComponent.TradeNumber\":\"Trade Number\",\"TradingBoard.OrdersComponent.TransTime\":\"Trans Time\",\"TradingBoard.PopupMessage.B2Margin.Account.Text [baseCurrency, accountCode]\":\"{{Successfully Created}}\",\"TradingBoard.PopupMessage.B2Margin.Liquidation.Text\":\"Liquidation\",\"TradingBoard.PopupMessage.B2Margin.RiskLevelAlert.Level1.Text [riskLevelLimit]\":\"Risk Level Alert Level 1 {{riskLevelLimit}}\",\"TradingBoard.PopupMessage.B2Margin.RiskLevelAlert.Level2.Text [riskLevelLimit]\":\"Risk Level Alert Level 2 {{riskLevelLimit}}\",\"TradingBoard.PopupMessage.B2Margin.RiskLevelAlert.Liquidation.Text [riskLevelLimit]\":\"Risk Level Alert Liquidation {{riskLevelLimit}}\",\"TradingBoard.PopupMessage.LongMessage.Title\":\"Long Message\",\"TradingBoard.PopupMessage.Warning.Title\":\"Warning\",\"TradingBoard.TradingView.OpenSelectInstrumentDialogLabel\":\"Open Select Instrument Dialog Label\",\"TradingBoard.WarpAuthorization.AuthorizationFailedMessage\":\"Authorization Failed Message\",\"TradingBoard.Widgets.Account.AccountIdWasCopiedToClipboard\":\"AccountId Was Copied To Clipboard\",\"TradingBoard.Widgets.Account.AccountNotFound [walletAccountId]\":\"Account {{walletAccountId}} not found\",\"TradingBoard.Widgets.Account.B2MarginAccountStateService.CreateAccount.LimitReached [product]\":\"Account Limit Reached\",\"TradingBoard.Widgets.Account.B2MarginAccountStateService.CreateAccount.Success [product]\":\"Successfully Created an Account\",\"TradingBoard.Widgets.DialogRenameMyAccountComponent.CancelButton\":\"Cancel\",\"TradingBoard.Widgets.DialogRenameMyAccountComponent.RenameButton\":\"Rename\",\"TradingBoard.Widgets.DialogRenameMyAccountComponent.Title\":\"Title\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.OrderId\":\"Order ID\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Price\":\"Price\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Side\":\"Side\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Size\":\"Size\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Status\":\"Status\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Symbol\":\"Symbol\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Time\":\"Time\",\"TradingBoard.Widgets.InactiveOrders.B2Margin.Table.Column.Type\":\"Type\",\"TradingBoard.Widgets.MoexPlaceOrder.Amount\":\"Amount\",\"TradingBoard.Widgets.MoexPlaceOrder.AskPrice\":\"Ask\",\"TradingBoard.Widgets.MoexPlaceOrder.BidPrice\":\"Bid\",\"TradingBoard.Widgets.MoexPlaceOrder.Cancellation\":\"Cancellation\",\"TradingBoard.Widgets.MoexPlaceOrder.CreateLimitOrder.ConfirmationBody\":\"Confirmation Body limit order\",\"TradingBoard.Widgets.MoexPlaceOrder.CreateMarketOrder.ConfirmationBody\":\"Confirmation Body market order\",\"TradingBoard.Widgets.MoexPlaceOrder.CreateOrder.ConfirmationTitle\":\"Title\",\"TradingBoard.Widgets.MoexPlaceOrder.IfPriceGreaterOrEqual\":\"If Price Greater Or Equal\",\"TradingBoard.Widgets.MoexPlaceOrder.IfPriceLowerOrEqual\":\"If Price Lower Or Equal\",\"TradingBoard.Widgets.MoexPlaceOrder.InactiveSubAccount.Caption\":\"Caption\",\"TradingBoard.Widgets.MoexPlaceOrder.InactiveSubAccount.Funds\":\"Funds\",\"TradingBoard.Widgets.MoexPlaceOrder.Instrument\":\"Instrument\",\"TradingBoard.Widgets.MoexPlaceOrder.InstrumentTradingStatus.TradingUnavailableMessage\":\"Trading Unavailable\",\"TradingBoard.Widgets.MoexPlaceOrder.InsufficientQualificationOfInvestor [testLink]\":\"Insufficient Qualification Of Investor\",\"TradingBoard.Widgets.MoexPlaceOrder.LastPrice\":\"Last Price\",\"TradingBoard.Widgets.MoexPlaceOrder.LotSize\":\"Lot Size\",\"TradingBoard.Widgets.MoexPlaceOrder.Lots\":\"Lots\",\"TradingBoard.Widgets.MoexPlaceOrder.MyLots\":\"MyLots\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderSide.Buy\":\"Buy\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderSide.Sell\":\"Sell\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderSucceeded.Message\":\"Message\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderType.Limit\":\"Limit\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderType.Market\":\"Market\",\"TradingBoard.Widgets.MoexPlaceOrder.OrderType.Stop\":\"Stop\",\"TradingBoard.Widgets.MoexPlaceOrder.PlacingOrder\":\"Placing Order\",\"TradingBoard.Widgets.MoexPlaceOrder.PlacingRepeatOrder\":\"Placing Repeat Order\",\"TradingBoard.Widgets.MoexPlaceOrder.Portfolio\":\"Portfolio\",\"TradingBoard.Widgets.MoexPlaceOrder.Price\":\"Price\",\"TradingBoard.Widgets.MoexPlaceOrder.SelectAllLots\":\"Select All Lots\",\"TradingBoard.Widgets.MoexPlaceOrder.SendOrder\":\"Send Order\",\"TradingBoard.Widgets.MoexPlaceOrder.StopLimitPrice\":\"Stop Limit Price\",\"TradingBoard.Widgets.MoexPlaceOrder.StopOrderCancelSucceededMessage\":\"Stop Order Cancel Succeeded Message\",\"TradingBoard.Widgets.MoexPlaceOrder.SubAccount\":\"Sub Account\",\"TradingBoard.Widgets.MoexPlaceOrder.TotalAmount\":\"Total Amount\",\"TradingBoard.Widgets.MoexPlaceOrder.Transfer\":\"Transfer\",\"TradingBoard.Widgets.MoexPlaceOrder.TransferFundsBetweenSubAccounts.Title\":\"Title\",\"TradingBoard.Widgets.MyAccounts.CashInButton\":\"CashIn Button\",\"TradingBoard.Widgets.MyAccounts.Currency\":\"Currency\",\"TradingBoard.Widgets.MyAccounts.Period\":\"Period\",\"TradingBoard.Widgets.MyAccounts.Portfolio\":\"Portfolio\",\"TradingBoard.Widgets.MyAccounts.SubAccount\":\"Sub Account\",\"TradingBoard.Widgets.MyAccounts.Title\":\"Title\",\"TradingBoard.Widgets.MyAccounts.TotalPortfolioPrice\":\"Total Portfolio Price\",\"TradingBoard.Widgets.MyAccounts.TotalSubAccountPrice\":\"Total SubAccount Price\",\"TradingBoard.Widgets.OpenOrders.B2Margin.CloseAll.Confirmation.Body\":\"Are you sure you want to close all ?\",\"TradingBoard.Widgets.OpenOrders.B2Margin.CloseAll.Success\":\"Success\",\"TradingBoard.Widgets.OpenOrders.B2Margin.OrderCloseDialog.Body\":\"Order Close Dialog\",\"TradingBoard.Widgets.OpenOrders.B2Margin.OrderCloseDialog.Title\":\"Close Order\",\"TradingBoard.Widgets.OpenOrders.B2Margin.OrderCloseRejected\":\"Rejected\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Button.Close\":\"Close\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Button.CloseAll\":\"Close All\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Button.Edit\":\"Edit\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Created\":\"Created\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.CurrentPrice\":\"Current price\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Expiration\":\"Expires at\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.FillPrice\":\"Fill price\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.FilledSize\":\"Quantity filled\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.OrderId\":\"Order ID\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Price\":\"Price\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Quantity\":\"Quantity\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Side\":\"Side\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Size\":\"Lots\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.StopLoss\":\"Stop-loss\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Symbol\":\"Symbol\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.TakeProfit\":\"Take-profit\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.TimeInForce\":\"Time in Force\",\"TradingBoard.Widgets.OpenOrders.B2Margin.Table.Column.Type\":\"Type\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Button.Close\":\"Close\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Button.Edit\":\"Edit\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Actions\":\"Actions\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Created\":\"Created\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.CurrentPrice\":\"CurrentPrice\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Expiration\":\"Expiration\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.FillPrice\":\"FillPrice\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.FilledSize\":\"FilledSize\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.OrderId\":\"OrderId\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Price\":\"Price\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Quantity\":\"Quantity\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Side\":\"Side\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Size\":\"Size\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Symbol\":\"Symbol\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.TimeInForce\":\"TimeInForce\",\"TradingBoard.Widgets.OpenOrders.B2MarginSpot.Table.Column.Type\":\"Type\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Amount\":\"Amount\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Cancel\":\"Cancel\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Filled\":\"Filled\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Instrument\":\"Instrument\",\"TradingBoard.Widgets.OpenOrders.TableHeader.LimitPrice\":\"Limit Price\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Remaining\":\"Remaining\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Side\":\"Side\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Status\":\"Status\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Time\":\"Time\",\"TradingBoard.Widgets.OpenOrders.TableHeader.TimeInForce\":\"Time In Force\",\"TradingBoard.Widgets.OpenOrders.TableHeader.Type\":\"Type\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Commission\":\"Commission\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Expiration\":\"Expiration\",\"TradingBoard.Widgets.OrderHistory.Table.Column.FillPrice\":\"Fill price\",\"TradingBoard.Widgets.OrderHistory.Table.Column.OrderId\":\"Order ID\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Price\":\"Price\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Quantity\":\"Quantity\",\"TradingBoard.Widgets.OrderHistory.Table.Column.QuantityFilled\":\"Quantity filled\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Side\":\"Side\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Size\":\"Lots\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Status\":\"Status\",\"TradingBoard.Widgets.OrderHistory.Table.Column.StopLoss\":\"Stop-loss\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Symbol\":\"Symbol\",\"TradingBoard.Widgets.OrderHistory.Table.Column.TakeProfit\":\"Take-profit\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Time\":\"Time\",\"TradingBoard.Widgets.OrderHistory.Table.Column.TimeInForce\":\"Time in force\",\"TradingBoard.Widgets.OrderHistory.Table.Column.TriggerPrice\":\"Trigger price\",\"TradingBoard.Widgets.OrderHistory.Table.Column.Type\":\"Type\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Amount\":\"Amount\",\"TradingBoard.Widgets.OrderHistory.TableHeader.ExecutionPrice\":\"Execution Price\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Filled\":\"Filled\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Instrument\":\"Instrument\",\"TradingBoard.Widgets.OrderHistory.TableHeader.LoadNextPage\":\"Load Next Page\",\"TradingBoard.Widgets.OrderHistory.TableHeader.OrderType\":\"Order Type\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Remaining\":\"Remaining\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Side\":\"Side\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Status\":\"Status\",\"TradingBoard.Widgets.OrderHistory.TableHeader.Time\":\"Time\",\"TradingBoard.Widgets.OrderHistory.TableHeader.TimeInForce\":\"Time In Force\",\"TradingBoard.Widgets.OrdersComponent.OrderCancelSucceededMessage\":\"Order Cancel Succeeded Message\",\"TradingBoard.Widgets.PlaceOrderClassic.Button.Buy\":\"Buy\",\"TradingBoard.Widgets.PlaceOrderClassic.Button.Sell\":\"Sell\",\"TradingBoard.Widgets.PlaceOrderClassic.Leverage\":\"Leverage\",\"TradingBoard.Widgets.PlaceOrderClassic.LeverageMaxLabel\":\"Max Leverage\",\"TradingBoard.Widgets.PlaceOrderClassic.LeverageMinLabel\":\"Min Leverage\",\"TradingBoard.Widgets.PlaceOrderClassic.LotSize\":\"Lots\",\"TradingBoard.Widgets.PlaceOrderClassic.SessionHigh\":\"High\",\"TradingBoard.Widgets.PlaceOrderClassic.SessionLow\":\"Low\",\"TradingBoard.Widgets.PlaceOrderMaxi.Button.Buy\":\"Buy\",\"TradingBoard.Widgets.PlaceOrderMaxi.Button.Sell\":\"Sell\",\"TradingBoard.Widgets.PlaceOrderMaxi.Calendar.Label\":\"Day/Month/Year\",\"TradingBoard.Widgets.PlaceOrderMaxi.Calendar.Placeholder\":\"DD/MM/YYYY\",\"TradingBoard.Widgets.PlaceOrderMaxi.Cost.Buy\":\"Required margin\",\"TradingBoard.Widgets.PlaceOrderMaxi.Cost.Sell\":\"Required margin\",\"TradingBoard.Widgets.PlaceOrderMaxi.Duration\":\"Duration\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.IsRequired\":\"Required\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.MustBeHigherOrEqual [value]\":\"must be greater than or equal to {{value}}\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.MustBeHigherThan [value]\":\"must be greater than {{value}}\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.MustBeLowerOrEqual [value]\":\"must be less than or equal to {{value}}\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.MustBeLowerThan [value]\":\"must be less than {{value}}\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.ValueIsInvalid\":\"ValueIsInvalid\",\"TradingBoard.Widgets.PlaceOrderMaxi.Errors.ValueIsZero\":\"ValueIsZero\",\"TradingBoard.Widgets.PlaceOrderMaxi.Instrument\":\"Instrument\",\"TradingBoard.Widgets.PlaceOrderMaxi.Leverage\":\"Leverage\",\"TradingBoard.Widgets.PlaceOrderMaxi.LeverageMaxLabel\":\"max\",\"TradingBoard.Widgets.PlaceOrderMaxi.LeverageMinLabel\":\"min\",\"TradingBoard.Widgets.PlaceOrderMaxi.LimitPrice\":\"Limit price\",\"TradingBoard.Widgets.PlaceOrderMaxi.LotSize\":\"Lots\",\"TradingBoard.Widgets.PlaceOrderMaxi.OrderType\":\"Order\",\"TradingBoard.Widgets.PlaceOrderMaxi.OrderType.Limit\":\"Limit\",\"TradingBoard.Widgets.PlaceOrderMaxi.OrderType.Market\":\"Market\",\"TradingBoard.Widgets.PlaceOrderMaxi.OrderType.Stop\":\"Stop\",\"TradingBoard.Widgets.PlaceOrderMaxi.StopLossPrice\":\"Stop-loss price\",\"TradingBoard.Widgets.PlaceOrderMaxi.StopPrice\":\"Stop price\",\"TradingBoard.Widgets.PlaceOrderMaxi.TakeProfitPrice\":\"Take-profit price\",\"TradingBoard.Widgets.PlaceOrderMaxi.Time\":\"Time\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.AddItem\":\"AddItem\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.Amount [symbol]\":\"Amount  {{symbol}}\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.Button.Buy\":\"Buy\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.Button.Sell\":\"Sell\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.ConfirmationBody\":\"ConfirmationBody\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.ConfirmationTitle\":\"ConfirmationTitle\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.Instrument\":\"Instrument\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.MarketPrice\":\"MarketPrice\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.OrderType\":\"OrderType\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.OrderType.Limit\":\"Limit\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.OrderType.Market\":\"Market\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.OrderType.Stop\":\"Stop\",\"TradingBoard.Widgets.PlaceOrderSpotClassic.PlaceOrderSpotClassicItem.Price [symbol]\":\"Price {{symbol}}\",\"TradingBoard.Widgets.Portfolio.Back\":\"Back\",\"TradingBoard.Widgets.Portfolio.CloseAll\":\"Close all positions\",\"TradingBoard.Widgets.Portfolio.CloseAll.Confirmation.Body\":\"Close all positions?\",\"TradingBoard.Widgets.Portfolio.CloseAll.Success\":\"All positions closed\",\"TradingBoard.Widgets.Portfolio.Group.NetPL\":\"Net PnL\",\"TradingBoard.Widgets.Portfolio.Group.NetPLHint\":\"Net P&L\",\"TradingBoard.Widgets.Portfolio.Group.UnrealizedPL\":\"PnL\",\"TradingBoard.Widgets.Portfolio.Group.Value\":\"Value\",\"TradingBoard.Widgets.Portfolio.Instrument.Buy\":\"Buy\",\"TradingBoard.Widgets.Portfolio.Instrument.CloseAll.ButtonTitle\":\"Close all positions\",\"TradingBoard.Widgets.Portfolio.Instrument.CloseAll.Confirmation.Body\":\"Close all positions for selected instrument?\",\"TradingBoard.Widgets.Portfolio.Instrument.CloseAll.Success\":\"All positions for selected instrument closed\",\"TradingBoard.Widgets.Portfolio.Instrument.Sell\":\"Sell\",\"TradingBoard.Widgets.Portfolio.LoadingMessage\":\"Loading\",\"TradingBoard.Widgets.Portfolio.NoPositionsMessage\":\"No positions\",\"TradingBoard.Widgets.Portfolio.TableHeader.Commission\":\"Commission\",\"TradingBoard.Widgets.Portfolio.TableHeader.CurrentPrice\":\"Current Price\",\"TradingBoard.Widgets.Portfolio.TableHeader.Lots\":\"Lots\",\"TradingBoard.Widgets.Portfolio.TableHeader.NetPL\":\"Net PnL\",\"TradingBoard.Widgets.Portfolio.TableHeader.NetPLHint\":\"PnL - Swap - Commission\",\"TradingBoard.Widgets.Portfolio.TableHeader.Swap\":\"Swap\",\"TradingBoard.Widgets.Portfolio.TableHeader.Symbol\":\"Symbol\",\"TradingBoard.Widgets.Portfolio.TableHeader.UnrealizedPL\":\"PnL\",\"TradingBoard.Widgets.Portfolio.TableHeader.Value\":\"Value\",\"TradingBoard.Widgets.Positions.Change\":\"PnL %\",\"TradingBoard.Widgets.Positions.Close\":\"Close\",\"TradingBoard.Widgets.Positions.CloseAll.Success\":\"All Positions Closed \",\"TradingBoard.Widgets.Positions.CloseAllButton\":\"Close All\",\"TradingBoard.Widgets.Positions.CloseAllConfirmation.Body\":\"Are you sure you want to close all positions ?\",\"TradingBoard.Widgets.Positions.CloseAllConfirmation.Title\":\"Close All Positions\",\"TradingBoard.Widgets.Positions.CollapseAll\":\"Collapse All\",\"TradingBoard.Widgets.Positions.CollapseRow\":\"Collapse\",\"TradingBoard.Widgets.Positions.CurrentPrice\":\"Current Price\",\"TradingBoard.Widgets.Positions.Edit\":\"Edit\",\"TradingBoard.Widgets.Positions.ExpandAll\":\"Expand All\",\"TradingBoard.Widgets.Positions.ExpandRow\":\"Expand\",\"TradingBoard.Widgets.Positions.FillPrice\":\"Fill Price\",\"TradingBoard.Widgets.Positions.Instrument.CloseAll.Confirmation.Body\":\"Close all positions for this instrument?\",\"TradingBoard.Widgets.Positions.Leverage\":\"Leverage\",\"TradingBoard.Widgets.Positions.NetAggregation\":\"Net Aggregation\",\"TradingBoard.Widgets.Positions.OpenSettings\":\"Settings\",\"TradingBoard.Widgets.Positions.OrderCreated\":\"Order Created\",\"TradingBoard.Widgets.Positions.P/L\":\"P/L\",\"TradingBoard.Widgets.Positions.PositionID\":\"Position ID\",\"TradingBoard.Widgets.Positions.Side\":\"Side\",\"TradingBoard.Widgets.Positions.Size\":\"Size\",\"TradingBoard.Widgets.Positions.StopLoss\":\"Stop Loss\",\"TradingBoard.Widgets.Positions.Symbol\":\"Symbol\",\"TradingBoard.Widgets.Positions.TakeProfit\":\"Take Profit\",\"TradingBoard.Widgets.Positions.TotalCommissions\":\"Total Commissions\",\"TradingBoard.Widgets.Positions.TotalSwap\":\"Total Swap\",\"TradingBoard.Widgets.QuickLimit.Amount\":\"Amount\",\"TradingBoard.Widgets.QuickLimit.Button.Buy [currency]\":\"BUY {{currency}} AT MY PRICE\",\"TradingBoard.Widgets.QuickLimit.Button.Sell [currency]\":\"SELL {{currency}} AT MY PRICE\",\"TradingBoard.Widgets.QuickLimit.Fee\":\"Fee\",\"TradingBoard.Widgets.QuickLimit.Price\":\"My Price\",\"TradingBoard.Widgets.QuickLimit.ReceivedFunds\":\"Received funds\",\"TradingBoard.Widgets.QuickLimit.RequiredFunds\":\"Required funds\",\"TradingBoard.Widgets.QuickMarket.Amount\":\"Amount\",\"TradingBoard.Widgets.QuickMarket.Button.Buy [currency]\":\"BUY {{currency}} NOW\",\"TradingBoard.Widgets.QuickMarket.Button.Sell [currency]\":\"SELL {{currency}} NOW\",\"TradingBoard.Widgets.QuickMarket.Buy.TabTitle\":\"BUY\",\"TradingBoard.Widgets.QuickMarket.Fee\":\"Fee\",\"TradingBoard.Widgets.QuickMarket.ReceivedFunds\":\"Received funds\",\"TradingBoard.Widgets.QuickMarket.RequiredFunds\":\"Required funds\",\"TradingBoard.Widgets.QuickMarket.Sell.TabTitle\":\"SELL\",\"TradingBoard.Widgets.SimpleExchange.Errors.Required\":\"Errors.Required\",\"TradingBoard.Widgets.SimpleExchange.Errors.notZero\":\"Errors.notZero\",\"TradingBoard.Widgets.SimpleExchange.ExchangeButton\":\"Exchange\",\"TradingBoard.Widgets.SimpleExchange.FromAmount\":\"FromAmount\",\"TradingBoard.Widgets.SimpleExchange.GoHomeText\":\"GoHome\",\"TradingBoard.Widgets.SimpleExchange.NoAccountMessage\":\"NoAccountMessage\",\"TradingBoard.Widgets.SimpleExchange.NoPairsMessage\":\"NoPairsMessage\",\"TradingBoard.Widgets.SimpleExchange.OrderCompleted\":\"OrderCompleted\",\"TradingBoard.Widgets.SimpleExchange.OrderRejected\":\"OrderRejected\",\"TradingBoard.Widgets.SimpleExchange.SelectAnotherCurrency\":\"SelectAnotherCurrency\",\"TradingBoard.Widgets.SimpleExchange.SelectCurrency\":\"SelectCurrency\",\"TradingBoard.Widgets.SimpleExchange.ToAmount\":\"ToAmount\",\"TradingBoard.Widgets.StopLimit.Amount\":\"Amount\",\"TradingBoard.Widgets.StopLimit.Available\":\"Available\",\"TradingBoard.Widgets.StopLimit.Button.Buy [currency]\":\"Buy {{currency}}\",\"TradingBoard.Widgets.StopLimit.Button.Sell [currency]\":\"Sell {{currency}}\",\"TradingBoard.Widgets.StopLimit.Buy.TabTitle\":\"Stop Limit Buy Tab Title\",\"TradingBoard.Widgets.StopLimit.CreatedOrder.Activated\":\"The order was activated\",\"TradingBoard.Widgets.StopLimit.CreatedOrder.Rejected\":\"The order was rejected\",\"TradingBoard.Widgets.StopLimit.CreatedOrder.WaitingForActivation\":\"The order was created\",\"TradingBoard.Widgets.StopLimit.Fee\":\"Fee\",\"TradingBoard.Widgets.StopLimit.Fee.Description [feePercent]\":\"Description {{feePercent}}\",\"TradingBoard.Widgets.StopLimit.Price\":\"Price\",\"TradingBoard.Widgets.StopLimit.Sell.TabTitle\":\"Stop Limit Sell Tab Title\",\"TradingBoard.Widgets.StopLimit.StopPrice\":\"Stop Price \",\"TradingBoard.Widgets.StopLimit.StopPrice.Buy.Description\":\"Buy Description\",\"TradingBoard.Widgets.StopLimit.StopPrice.Sell.Description\":\"Sell Description\",\"TradingBoard.Widgets.StopLimit.Total\":\"Total\",\"TradingBoard.Widgets.StopMarket.Amount\":\"Amount\",\"TradingBoard.Widgets.StopMarket.Available\":\"Available\",\"TradingBoard.Widgets.StopMarket.Button.Buy [currency]\":\"Buy {{currency}}\",\"TradingBoard.Widgets.StopMarket.Button.Sell [currency]\":\"Sell {{currency}}\",\"TradingBoard.Widgets.StopMarket.Buy.TabTitle\":\"Stop Market Buy TabTitle\",\"TradingBoard.Widgets.StopMarket.CreatedOrder.Activated\":\"The order was activated\",\"TradingBoard.Widgets.StopMarket.CreatedOrder.Rejected\":\"The order was rejected\",\"TradingBoard.Widgets.StopMarket.CreatedOrder.WaitingForActivation\":\"The order was created\",\"TradingBoard.Widgets.StopMarket.FOK\":\"FOK\",\"TradingBoard.Widgets.StopMarket.Fee\":\"Fee\",\"TradingBoard.Widgets.StopMarket.Fee.Description [feePercent]\":\"Description {{feePercent}}\",\"TradingBoard.Widgets.StopMarket.IOC\":\"IOC\",\"TradingBoard.Widgets.StopMarket.Sell.TabTitle\":\"Stop Market Sell TabTitle\",\"TradingBoard.Widgets.StopMarket.StopPrice\":\"Stop Price\",\"TradingBoard.Widgets.StopMarket.StopPrice.Buy.Description\":\"Buy Description\",\"TradingBoard.Widgets.StopMarket.StopPrice.Sell.Description\":\"Sell Description\",\"TradingBoard.Widgets.StopMarket.TimeInForce\":\"Time In Force\",\"TradingBoard.Widgets.StopMarket.Total\":\"Total\",\"TradingBoard.Widgets.StopOrders.Cancel.Title\":\"Cancel\",\"TradingBoard.Widgets.StopOrders.DeleteOrder.Confirmation.Body\":\"Are you sure you want to cancel order?\",\"TradingBoard.Widgets.StopOrders.DeleteOrder.Confirmation.Title\":\"Cancel stop order\",\"TradingBoard.Widgets.StopOrders.OrderTypePrefix\":\"Stop\",\"TradingBoard.Widgets.StopOrders.StopMarket.Price\":\"Price \",\"TradingBoard.Widgets.StopOrders.TableHeader.Instrument\":\"Instrument\",\"TradingBoard.Widgets.StopOrders.TableHeader.Price\":\"Price\",\"TradingBoard.Widgets.StopOrders.TableHeader.Quantity\":\"Quantity\",\"TradingBoard.Widgets.StopOrders.TableHeader.Side\":\"Side\",\"TradingBoard.Widgets.StopOrders.TableHeader.StopPrice\":\"Stop Price\",\"TradingBoard.Widgets.StopOrders.TableHeader.Time\":\"Time\",\"TradingBoard.Widgets.StopOrders.TableHeader.TimeInForce\":\"Time In Force\",\"TradingBoard.Widgets.StopOrders.TableHeader.Type\":\"Type\",\"TradingBoard.Widgets.SymbolLibrary.Item.AddToWatchlist\":\"Add To Watchlist\",\"TradingBoard.Widgets.SymbolLibrary.Item.ButtonBuy\":\"Buy\",\"TradingBoard.Widgets.SymbolLibrary.Item.ButtonSell\":\"Sell\",\"TradingBoard.Widgets.SymbolLibrary.Item.Change:\":\"Change:\",\"TradingBoard.Widgets.SymbolLibrary.Item.DeleteFromWatchlist\":\"Delete From Watchlist\",\"TradingBoard.Widgets.SymbolLibrary.Item.High:\":\"High:\",\"TradingBoard.Widgets.SymbolLibrary.Item.Low:\":\"Low:\",\"TradingBoard.Widgets.SymbolLibrary.Item.Price:\":\"Price:\",\"TradingBoard.Widgets.SymbolLibrary.Item.SymbolAddedToWatchlist\":\"Symbol Added To Watchlist\",\"TradingBoard.Widgets.SymbolLibrary.Item.SymbolDeletedFromWatchlist\":\"Symbol Deleted From Watchlist\",\"TradingBoard.Widgets.Trades.Table.Column.Commission\":\"Commission\",\"TradingBoard.Widgets.Trades.Table.Column.PositionEffect\":\"Position effect\",\"TradingBoard.Widgets.Trades.Table.Column.PositionId\":\"Position ID\",\"TradingBoard.Widgets.Trades.Table.Column.SettledPl\":\"Settled PnL\",\"TradingBoard.Widgets.Trades.Table.Column.Side\":\"Side\",\"TradingBoard.Widgets.Trades.Table.Column.Size\":\"Lots\",\"TradingBoard.Widgets.Trades.Table.Column.Symbol\":\"Symbol\",\"TradingBoard.Widgets.Trades.Table.Column.Time\":\"Created\",\"TradingBoard.Widgets.Trades.Table.Column.TradePrice\":\"Price\",\"TradingBoard.Widgets.TradesHistory.Table.Column.Price\":\"Price\",\"TradingBoard.Widgets.TradesHistory.Table.Column.Size\":\"Lots\",\"TradingBoard.Widgets.TradesHistory.Table.Column.Time\":\"Created\",\"TradingBoard.Widgets.TradingView.DisplayOptions.LimitOrders\":\"Limit Orders\",\"TradingBoard.Widgets.TradingView.DisplayOptions.Positions\":\"Positions\",\"TradingBoard.Widgets.TradingView.DisplayOptions.PositionsWithOrders\":\"Positions with Orders\",\"TradingBoard.Widgets.TradingView.DisplayOptions.StopOrders\":\"Stop Orders\",\"TradingBoard.Widgets.WarpOrders.CanceledOrders\":\"Canceled Orders\",\"TradingBoard.Widgets.WarpOrders.Components.CancelOrderDialog.Back\":\"Back\",\"TradingBoard.Widgets.WarpOrders.Components.CancelOrderDialog.Message [id]\":\"Are you sure you want to cancel the order {{id}}?\",\"TradingBoard.Widgets.WarpOrders.Components.CancelOrderDialog.Remove\":\"Cancel Order\",\"TradingBoard.Widgets.WarpOrders.Components.CancelOrderDialog.Title\":\"Cancel Order\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.Placeholder\":\"Filter\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterSide.All\":\"All\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterSide.Buy\":\"Buy\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterSide.Sell\":\"Sell\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterStatus.All\":\"All\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterStatus.Filled\":\"Filled\",\"TradingBoard.Widgets.WarpOrders.Components.TableHeaderFilter.WarpOrdersFilterStatus.Working\":\"Working\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Commission\":\"Commission\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Date\":\"Date\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.ExchangeOrderId\":\"Exchange Order ID\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Id\":\"ID\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.InstrumentShortName\":\"Instrument\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Isin\":\"ISIN\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.LotPrice\":\"Lot Price\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.OrderId\":\"Order ID \",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.ParentAccountCaption\":\"Caption\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Price\":\"Price\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Qty\":\"Qty\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.QtyUnits\":\"Units\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Side\":\"Side\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Status\":\"Status\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.StopPrice\":\"Stop Price\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.SubAccountCaption\":\"Caption\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Symbol\":\"Symbol\",\"TradingBoard.Widgets.WarpOrders.Config.ColumnsConfig.Type\":\"Type\",\"TradingBoard.Widgets.WarpOrders.Config.DateFilterConfig.AllTime\":\"All Time\",\"TradingBoard.Widgets.WarpOrders.Config.DateFilterConfig.LastMonth\":\"Last Month\",\"TradingBoard.Widgets.WarpOrders.Config.DateFilterConfig.LastThreeDays\":\"Last Three Days\",\"TradingBoard.Widgets.WarpOrders.Config.DateFilterConfig.Manually\":\"Select Dates\",\"TradingBoard.Widgets.WarpOrders.Config.DateFilterConfig.Today\":\"Today\",\"TradingBoard.Widgets.WarpOrders.Empty\":\"Empty\",\"TradingBoard.Widgets.WarpOrders.Limit\":\"Limit\",\"TradingBoard.Widgets.WarpOrders.Market\":\"Market\",\"TradingBoard.Widgets.WarpOrders.OrderCancelSucceededMessage\":\"Order was successfully canceled\",\"TradingBoard.Widgets.WarpOrders.OrderSide.Buy\":\"Buy\",\"TradingBoard.Widgets.WarpOrders.OrderSide.Sell\":\"Sell\",\"TradingBoard.Widgets.WarpOrders.OrderStatus.Canceled\":\"Canceled\",\"TradingBoard.Widgets.WarpOrders.OrderStatus.Filled\":\"Filled\",\"TradingBoard.Widgets.WarpOrders.OrderStatus.Rejected\":\"Rejected\",\"TradingBoard.Widgets.WarpOrders.OrderStatus.Working\":\"Working\",\"TradingBoard.Widgets.WarpOrders.OrderType.Limit\":\"Limit\",\"TradingBoard.Widgets.WarpOrders.OrderType.Market\":\"Market\",\"TradingBoard.Widgets.WarpOrders.OrderType.Stop\":\"Stop\",\"TradingBoard.Widgets.WarpOrders.OrderType.StopLimit\":\"Stop Limit\",\"TradingBoard.Widgets.WarpOrders.Orders\":\"Orders\",\"TradingBoard.Widgets.WarpOrders.Services.WarpOrdersDataMapperService.StopOrder.MarketPrice\":\"Market Price\",\"TradingBoard.Widgets.WarpOrders.Stop\":\"Stop\",\"TradingBoard.Widgets.WarpOrders.Trades\":\"Trades\",\"TradingBoard.Widgets.WarpPortfolio.AvailableFunds\":\"Available Funds\",\"TradingBoard.Widgets.WarpPortfolio.AvgChange\":\"Avg Change \",\"TradingBoard.Widgets.WarpPortfolio.AvgPrice\":\"Avg Price\",\"TradingBoard.Widgets.WarpPortfolio.BondMarket\":\"Bond Market\",\"TradingBoard.Widgets.WarpPortfolio.Change24h\":\"Change 24h\",\"TradingBoard.Widgets.WarpPortfolio.CurrencyMarket\":\"Currency Market\",\"TradingBoard.Widgets.WarpPortfolio.EUR\":\"EUR\",\"TradingBoard.Widgets.WarpPortfolio.Exchange\":\"Exchange\",\"TradingBoard.Widgets.WarpPortfolio.FindBySymbolOrISINOrName\":\"FindBySymbol/ISIN/Name\",\"TradingBoard.Widgets.WarpPortfolio.ISIN\":\"ISIN\",\"TradingBoard.Widgets.WarpPortfolio.LastPrice\":\"Last Price\",\"TradingBoard.Widgets.WarpPortfolio.Name\":\"Name\",\"TradingBoard.Widgets.WarpPortfolio.ObligationType.T0\":\"T0\",\"TradingBoard.Widgets.WarpPortfolio.ObligationType.T1\":\"T1\",\"TradingBoard.Widgets.WarpPortfolio.ObligationType.T2\":\"T2\",\"TradingBoard.Widgets.WarpPortfolio.ObligationType.TFut\":\"TFut\",\"TradingBoard.Widgets.WarpPortfolio.Period\":\"Period\",\"TradingBoard.Widgets.WarpPortfolio.Portfolio\":\"Portfolio\",\"TradingBoard.Widgets.WarpPortfolio.PrevClosePrice\":\"Prev Close Price\",\"TradingBoard.Widgets.WarpPortfolio.Price\":\"Price\",\"TradingBoard.Widgets.WarpPortfolio.Qty\":\"Qty\",\"TradingBoard.Widgets.WarpPortfolio.RUB\":\"RUB\",\"TradingBoard.Widgets.WarpPortfolio.StockMarket\":\"Stock Market\",\"TradingBoard.Widgets.WarpPortfolio.SubAccount\":\"Sub Account\",\"TradingBoard.Widgets.WarpPortfolio.TickerCode\":\"Ticker Code\",\"TradingBoard.Widgets.WarpPortfolio.TotalPortfolioPrice\":\"Total Portfolio Price\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Login\":\"Login\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.LossLimit\":\"Loss Limit\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Nickname/Login\":\"Nickname/Login\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.OpenedPositions\":\"Open Positions\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.PauseCopy\":\"Pause Copy\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.PnlUSD\":\"PnL, USD\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.RealizedFloating\":\"Current / Floating\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.RealizedFloatingPnl\":\"Current  / Floating \",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.ReverseCopy\":\"Reverse Copy\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.RiskRatio\":\"Risk Ratio\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Since\":\"Since\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.Status\":\"Status\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.StatusSince\":\"Since\",\"InvestmentPlatform.AccountDetails.Subscriptions.Table.View\":\"View\",\"InvestmentPlatform.AccountDetails.Subscriptions.Title\":\"Subscriptions\",\"InvestmentPlatform.AccountDetails.Subscriptions.UnsubscriptionPostponed\":\"Trading sessions for one or more symbols are closed. The unsubscription is pending\",\"InvestmentPlatform.AccountDetails.Subscriptions.ViewFeesPlan\":\"View Fee Plan\",\"InvestmentPlatform.AccountDetails.Tabs.ClosedPositions\":\"Closed Positions\",\"InvestmentPlatform.AccountDetails.Tabs.Deals\":\"Deals\",\"InvestmentPlatform.AccountDetails.Tabs.OpenedPositions\":\"Open Positions\",\"InvestmentPlatform.AccountDetails.TradingReport.Title\":\"Trading Report\",\"InvestmentPlatform.AccountList.AccountsFilter.SearchByLogin\":\"Search By Login\",\"InvestmentPlatform.AccountList.AccountsFilter.SortBy\":\"Sort By\",\"TradingBoard.Widgets.WarpPortfolio.TotalSubAccountPrice\":\"Total Sub Account Price\",\"TradingBoard.Widgets.WarpPortfolio.USD\":\"USD\",\"TradingBoard.Widgets.WarpPortfolio.WholePortfolio\":\"Whole Portfolio\",\"TradingBoard.Widgets.WatchList.B2Margin.RemoveButton\":\"Remove\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Ask\":\"Ask (buy)\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Bid\":\"Bid (sell)\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Change\":\"24h change\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.High\":\"24h high\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Low\":\"24h low\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Spread\":\"Spread\",\"TradingBoard.Widgets.WatchList.B2Margin.TableHeader.Symbol\":\"Symbol\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.RemoveButton\":\"RemoveButton\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.TableHeader.Change\":\"Change\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.TableHeader.High\":\"High\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.TableHeader.Low\":\"Low\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.TableHeader.Spread\":\"Spread\",\"TradingBoard.Widgets.WatchList.B2MarginSpot.TableHeader.Symbol\":\"Symbol\",\"TradingBoard.Widgets.WatchList.B2Trader.AddInstrument\":\"Add Instrument\",\"TradingBoard.Widgets.WatchList.Moex.AbsoluteChange\":\"Absolute Change\",\"TradingBoard.Widgets.WatchList.Moex.AddInstrument\":\"Add Instrument\",\"TradingBoard.Widgets.WatchList.Moex.Currency\":\"Currency\",\"TradingBoard.Widgets.WatchList.Moex.CurrentPrice\":\"Current Price\",\"TradingBoard.Widgets.WatchList.Moex.ISIN\":\"ISIN\",\"TradingBoard.Widgets.WatchList.Moex.InstrumentName\":\"Name\",\"TradingBoard.Widgets.WatchList.Moex.InstrumentTradingUnavailable\":\"Instrument Trading Unavailable\",\"TradingBoard.Widgets.WatchList.Moex.InstrumentType\":\"Type\",\"TradingBoard.Widgets.WatchList.Moex.RelativeChange\":\"Relative Change\",\"TradingBoard.Widgets.WatchList.Moex.Remove\":\"Remove\",\"TradingBoard.Widgets.WatchList.Moex.Sector\":\"Sector\",\"TradingBoard.Widgets.WatchList.Moex.StartTrading\":\"Start Trading\",\"TradingBoard.Widgets.WatchList.Moex.Symbol\":\"Symbol\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.Monthly\":\"Monthly\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.SixMonths\":\"Six Months\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.ThreeMonths\":\"Three Months\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.Today\":\"Today\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.Week\":\"Week\",\"TradingBoard.Widgets.WatchList.Moex.TimeInterval.Year\":\"Year\",\"TradingBoardSettings.GridType\":\"Grid Type\",\"TradingBoardSettings.Margin\":\"Margin\",\"Transaction was not finished\":\"Transaction was not finished\",\"Transaction.Tab.InternalTransfer\":\"Internal Transfer\",\"Transactions.AccountNamePrefix.Wallet\":\"Wallet\",\"Transactions.EmptyListMessage\":\"EmptyListMessage\",\"Mam.AccountList.AccountGroupDescription.Follower.MT4\":\"\",\"Mam.AccountList.AccountGroupDescription.Follower.MT5\":\"\",\"Mam.AccountList.AccountGroupDescription.Master.MT4\":\"\",\"Mam.AccountList.AccountGroupDescription.Master.MT5\":\"\",\"Metatrader.AccountList.ProductDetailsDialog.NoDetails.Message\":\"\",\"Pamm.AccountList.AccountGroupDescription.Follower.MT5\":\"\",\"Pamm.AccountList.AccountGroupDescription.Master.MT5\":\"\",\"B2trader.Widgets.OrderHistory.Title\":\"\",\"Briefcase.BriefcaseInfo.Accounts.Create.Link\":\"eee\",\"Briefcase.BriefcaseInfo.Accounts.Title\":\"eeeeeee\",\"B2Margin.Group.History.Title\":\"History\",\"B2Margin.Group.Tools.Title\":\"Tools\",\"B2Margin.Group.Trading.Title\":\"Trading\",\"B2Margin.Widgets.Account.Title\":\"Account\",\"Briefcase.CreateNewAccount.Wizard.OpenNewAccountSign.Title\":\"\",\"Briefcase.Title\":\"\",\"BriefcaseInfo.Title\":\"\",\"Catalog.Title\":\"\",\"Common.Widgets.MoexPlaceOrder.Title\":\"\",\"Common.Widgets.MyAccounts.Title\":\"\",\"Common.Widgets.MyBriefcase.Title\":\"\",\"Common.Widgets.PbsAnalytics.Title\":\"\",\"Eqwire.Consent.Components.ConsentCallbackBase.PaymentReference\":\"\",\"Eqwire.Consent.Components.ConsentCancelConfirmDialog.Cancel\":\"\",\"Eqwire.Consent.Components.ConsentCancelConfirmDialog.Confirm\":\"\",\"Eqwire.Consent.Components.ConsentCancelConfirmDialog.Message\":\"\",\"Eqwire.Consent.Components.ConsentConfirmBase.Balance\":\"\",\"Eqwire.Consent.Components.ConsentConfirmBase.Cancel\":\"\",\"Eqwire.Consent.Components.ConsentConfirmBase.Confirm\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.Amount\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.PayeeName\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.PaymentDetailsLabel\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.PaymentReference\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.SelectAccountsLabel\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.SuccessConfirmMessage\":\"\",\"Eqwire.Consent.Components.PaymentAccountConfirm.Title\":\"\",\"Eqwire.Onboarding.Const.FormConfig.LegalEntity.Ie\":\"\",\"Eqwire.Onboarding.Const.FormConfig.TradingAddressIsSame\":\"\",\"Eqwire.Onboarding.Const.StepperConfig.AboutInfo\":\"\",\"Eqwire.Onboarding.Const.StepperConfig.HomeAddress\":\"\",\"Eqwire.Onboarding.ErrorRequest.Message\":\"\",\"Eqwire.Onboarding.ErrorRequest.Title\":\"\",\"Eqwire.Onboarding.ErrorRequest.UpdateButton\":\"\",\"Eqwire.Onboarding.SuccessfullyRequest.Message\":\"\",\"Eqwire.Onboarding.SuccessfullyRequest.RequestSuccessfullySent\":\"\",\"Eqwire.Onboarding.Wizards.RegistrationType.BusinessAccount\":\"\",\"Eqwire.Onboarding.Wizards.RegistrationType.Continue\":\"\",\"Eqwire.Onboarding.Wizards.RegistrationType.IndividualAccount\":\"\",\"Eqwire.Onboarding.Wizards.RegistrationType.Title\":\"\",\"Eqwire.PaymentsAndTransfers.AccountsSelect.Label.From\":\"\",\"Eqwire.PaymentsAndTransfers.AccountsSelect.Placeholder.SelectAccount\":\"\",\"Eqwire.PaymentsAndTransfers.Amount.Placeholder\":\"\",\"Eqwire.PaymentsAndTransfers.CodeConfirmDialog.ConfirmButton\":\"\",\"Eqwire.PaymentsAndTransfers.CodeConfirmDialog.Description\":\"\",\"Eqwire.PaymentsAndTransfers.CodeConfirmDialog.Footer [minutes]\":\"\",\"Eqwire.PaymentsAndTransfers.CodeConfirmDialog.Title\":\"\",\"Eqwire.PaymentsAndTransfers.MyAccounts.Label.Account\":\"\",\"Eqwire.PaymentsAndTransfers.MyAccounts.Placeholder.SelectAccount\":\"\",\"Eqwire.PaymentsAndTransfers.MyAccounts.ReferenceMessage\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.AccountNumber\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.Bic\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.Iban\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.Name\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.ReferenceMessage\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.SortCode\":\"\",\"Eqwire.PaymentsAndTransfers.NewPayment.Type\":\"\",\"Eqwire.PaymentsAndTransfers.NoDataMessage\":\"\",\"Eqwire.PaymentsAndTransfers.SavedBeneficiary.Label.Beneficiary\":\"\",\"Eqwire.TellMoney.Login.Button.Label.SignIn\":\"\",\"Eqwire.TellMoney.Login.Description\":\"\",\"Eqwire.TellMoney.Login.GoBack\":\"\",\"Eqwire.TellMoney.Login.Title.SignIn\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.AccountAlias\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.AddNewAccount\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.Cancel\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.Currency\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.Hint\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.Product\":\"\",\"EqwireAccounts.Components.AddNewAccountDialog.Title\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.AccountAlias\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.AccountId\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.AccountNumber\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Amount\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Currency\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Description\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.DownloadButton\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Other\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Reference\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.SendDate\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.SenderDetails\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.SortCode\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Status\":\"\",\"EqwireAccounts.Components.TransactionDetailDialog.Type\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.Amount\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.Bank\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.Close\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.Currency\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.FailedTitle\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.ReferenceMessage\":\"\",\"EqwireAccounts.Components.TransactionStatusInfoDialog.SuccessTitle\":\"\",\"EqwireAccounts.Components.TransactionsFilter.ApplyFilters\":\"\",\"EqwireAccounts.Components.TransactionsFilter.FromAmountPlaceholder\":\"\",\"EqwireAccounts.Components.TransactionsFilter.PaidInPlaceholder\":\"\",\"EqwireAccounts.Components.TransactionsFilter.Placeholder.FromAmount\":\"\",\"EqwireAccounts.Components.TransactionsFilter.Placeholder.PaidIn\":\"\",\"EqwireAccounts.Components.TransactionsFilter.Placeholder.SearchDetails\":\"\",\"EqwireAccounts.Components.TransactionsFilter.SearchDetailsPlaceholder\":\"\",\"EqwireAccounts.Components.TransactionsFilter.ToAmountPlaceholder\":\"\",\"EqwireAccounts.EqwireAccountCard.Account\":\"\",\"EqwireAccounts.EqwireAccountCard.AccountId\":\"\",\"EqwireAccounts.EqwireAccountCard.Alias\":\"\",\"EqwireAccounts.EqwireAccountCard.Balance\":\"\",\"EqwireAccounts.EqwireAccountCard.Bic\":\"\",\"EqwireAccounts.EqwireAccountCard.Currency\":\"\",\"EqwireAccounts.EqwireAccountCard.Iban\":\"\",\"EqwireAccounts.EqwireAccountCard.PaymentSystems\":\"\",\"EqwireAccounts.EqwireAccountCard.SortCodeAndAccountNumber\":\"\",\"EqwireAccounts.EqwireAccountDetail.Account\":\"\",\"EqwireAccounts.EqwireAccountDetail.AccountId\":\"\",\"EqwireAccounts.EqwireAccountDetail.PaymentSystems\":\"\",\"EqwireAccounts.EqwireAccountDetail.SortCode\":\"\",\"EqwireAccounts.EqwireAccountDetail.Table.Date\":\"\",\"EqwireAccounts.EqwireAccountDetail.Table.GbpBalance\":\"\",\"EqwireAccounts.EqwireAccountDetail.Table.PaidIn\":\"\",\"EqwireAccounts.EqwireAccountDetail.Table.Reference\":\"\",\"EqwireAccounts.EqwireAccountDetail.Table.Status\":\"\",\"EqwireAccounts.EqwireAccountDetail.Title\":\"\",\"EqwireAccounts.EqwireAccountsList.AddNewAccount\":\"\",\"EqwireAccounts.EqwireAccountsList.SearchInputPlaceholder\":\"\",\"EqwireAccounts.EqwireAccountsList.SortOptions.Default\":\"\",\"EqwireAccounts.EqwireAccountsList.SortPlaceholder\":\"\",\"EqwireAccounts.EqwireAccountsList.Title\":\"\",\"EqwireAccounts.EqwireBeneficiaries.SortOptions.Default\":\"\",\"EqwireAccounts.Onboarding.Wizards.BusinessInfo.CurrencyPlaceholder\":\"\",\"EventsAndAssignments.AllAccounts\":\"\",\"EventsAndAssignments.Shared.BrokerageAccountsSelector.BrokerageAccounts\":\"\",\"EventsAndAssignments.Shared.Consts.AllAccounts\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.AllTime\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.Manual\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.NineMonth\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.OneMonth\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.OneYear\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.SixMonth\":\"\",\"EventsAndAssignments.Shared.Consts.DateFilter.ThreeMonth\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.April\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.August\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.December\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.February\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.January\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.July\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.June\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.March\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.May\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.November\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.October\":\"\",\"EventsAndAssignments.Shared.Consts.MonthsTranslations.September\":\"\",\"EventsAndAssignments.Shared.DateFilter.AllTime\":\"\",\"EventsAndAssignments.Shared.DateFilter.Manual\":\"\",\"EventsAndAssignments.Shared.DateFilter.NineMonth\":\"\",\"EventsAndAssignments.Shared.DateFilter.OneMonth\":\"\",\"EventsAndAssignments.Shared.DateFilter.OneYear\":\"\",\"EventsAndAssignments.Shared.DateFilter.SixMonth\":\"\",\"EventsAndAssignments.Shared.DateFilter.ThreeMonth\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.April\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.August\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.December\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.February\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.January\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.July\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.June\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.March\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.May\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.November\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.October\":\"\",\"EventsAndAssignments.Shared.MonthsToTranslations.September\":\"\",\"EventsAndAssignments.Shared.OperationsGroupHeader.Today\":\"\",\"EventsAndAssignments.Shared.TransactionItem.Deposit\":\"Deposit\",\"EventsAndAssignments.Shared.TransactionItem.Transfer\":\"\",\"EventsAndAssignments.Shared.TransactionItem.Withdrawal\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.AdditionalAccrualCommissionToMinimumSize\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.AdditionalBrokerageCommission\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.AgentRemuneration\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.AmendmentRevocationOfInstructionsFee\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.BankCommission\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.BlockedCash\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.BlockedSecuritiesOnDepositoryAccount\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.BrokerageCommissionForRepoTrade\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CashDeposit\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CashNetting\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CashWithdrawal\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForAssetTransfer\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForCashLimits\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForCashLimitsOverPastDays\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForConversion\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForLimitsOnSecurities\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionForLimitsOnSecuritiesOverPastDays\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionOfAssetManager\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommissionOfExternalBrokerAndTradingSystem\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommoditiesDeposit\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.CommoditiesWithdrawal\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.ConversionOfSecurities\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.NonStandardClientReportFee\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.NonTradeTransactionOfCashPositionByReconciliationResults\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.NonTradeTransactionOfSecurityPositionByReconciliationResults\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.OfferingOfAdditionalShares\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.OrderBuy\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.OrderSell\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.OtherIncome\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.Penalties\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.PenaltyForDebtOnCollateralIm\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.RedemptionOfBondsToBrokerAccount\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.RefundOfCommission\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.RefundOfLoanedSecurities\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.ReimbursableExpensesOfBroker\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.SecurityDeposit\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.SecurityNetting\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.SecurityWithdrawal\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.Tax\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.TaxPaymentFromBrokerageAccount\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.TradeBuy\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.TradeSell\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.TransferOfCash\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.VariationMargin\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.VariationMarginIntermediateClearing\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.WithdrawalOfCashFee\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.WithdrawalOfFinancialInstrumentsFee\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.WriteOffLoanInterest\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Consts.TradeEventTypeTranslateMap.WriteOffSecuritiesAfterRedemption\":\"\",\"EventsAndAssignments.TransactionsAndRequests.NoTradeEvents\":\"\",\"EventsAndAssignments.TransactionsAndRequests.SearchPlaceholder\":\"\",\"EventsAndAssignments.TransactionsAndRequests.ShowMore\":\"\",\"EventsAndAssignments.TransactionsAndRequests.Title\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventFilterType.Label.ActiveOrders\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventFilterType.Label.AllEvents\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventFilterType.Label.CanceledOrders\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventFilterType.Label.NonTradeOperations\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventFilterType.Label.TradeOperations\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventItem.Commission\":\"\",\"EventsAndAssignments.TransactionsAndRequests.TradeEventItem.Qty\":\"\",\"EventsAndAssignments.WithdrawalOrders.BrokerageAccounts\":\"\",\"EventsAndAssignments.WithdrawalOrders.Consts.TransactionTypeFilter.All\":\"\",\"EventsAndAssignments.WithdrawalOrders.Consts.TransactionTypeFilter.Deposit\":\"\",\"EventsAndAssignments.WithdrawalOrders.Consts.TransactionTypeFilter.Payout\":\"\",\"EventsAndAssignments.WithdrawalOrders.Consts.TransactionTypeFilter.Transfer\":\"\",\"EventsAndAssignments.WithdrawalOrders.NoDataWithCurrentFilter\":\"\",\"EventsAndAssignments.WithdrawalOrders.NoTransactionItems\":\"\",\"EventsAndAssignments.WithdrawalOrders.ShowMore\":\"\",\"EventsAndAssignments.WithdrawalOrders.Title\":\"\",\"EventsAndAssignments.WithdrawalOrders.Today\":\"\",\"EventsAndAssignments.WithdrawalOrders.TransactionItem.From\":\"\",\"EventsAndAssignments.WithdrawalOrders.TransactionItem.To\":\"\",\"ExpansionPanel.BrokerAccount.Title\":\"\",\"ExpansionPanel.Iis.Title\":\"\",\"ExpansionPanel.SubAccount.Title\":\"\",\"External Footer Text\":\"\",\"Features.AdvancedUi\":\"\",\"Features.Briefcase\":\"\",\"Features.Catalog\":\"\",\"Features.CreditsAndTransfers\":\"\",\"Features.EventsAndAssignments\":\"\",\"Features.NewsAndReviews\":\"\",\"Features.ProfileAndSettings\":\"\",\"Features.ProfileSettings\":\"\",\"Features.ReportsAndCertificates\":\"\",\"Features.TechnicalSupport\":\"\",\"Features.TradingIdeas\":\"\",\"Form.File.FileMessage.ImageRequirements\":\"\",\"Form.File.FileMessage.PerFile\":\"\",\"Gridster.Widgets.Tabs.InstrumentSynchronization.IntroductionMessage\":\"\",\"Gridster.Widgets.Tabs.InstrumentSynchronization.IntroductionTitle\":\"\",\"GroupList.Button.Add\":\"\",\"GroupList.Button.Delete\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeContacts\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeContacts.Address\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeContacts.Address.Value\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeContacts.Phone\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeContacts.Phone.Value\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeHours\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeHours.Weekdays\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeHours.Weekdays.Timetable\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeHours.Weekend\":\"\",\"HelpDesk.SupportMessage.Pbs.OfficeHours.Weekend.Timetable\":\"\",\"HelpDesk.SupportMessage.Pbs.SupportServiceHours\":\"\",\"HelpDesk.SupportMessage.Pbs.SupportServiceHours.Timetable.FirstMessage\":\"\",\"HelpDesk.SupportMessage.Pbs.SupportServiceHours.Timetable.SecondMessage\":\"\",\"HelpDesk.SupportMessage.Pbs.Title\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.BalanceAfterLabel\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.Button.ChangeAmount\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.Button.Proceed\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.Table.MasterAccHeader\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.Table.MinDepositHeader\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.TableLabel\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.Title\":\"\",\"History.Transactions.UnsubscribeConfirmationDialog.UserSubscriptionsLabel [count]\":\"\",\"Hong Kong\":\"\",\"Linux.dxtrade\":\"Linux dxtrade\",\"Linux.match_trader\":\"\",\"Linux.mt4\":\"Linux.mt4\",\"Linux.mt5\":\"Linux.mt5\",\"MacOS.ctrader\":\"\",\"MacOS.dxtrade\":\"MacOS dxtrade\",\"ID: {id}\":\"ID: {{id}}\",\"IOC\":\"IOC\",\"If yes please provide details\":\"If yes please provide details\",\"Image requirements\":\"Image requirements\",\"Important\":\"Important\",\"Incorrect order\":\"Incorrect order\",\"Indicative amount\":\"Indicative amount\",\"Individual account\":\"Individual account\",\"Info\":\"Info\",\"InfoDemoCTrader\":\"Info Demo CTrader\",\"InfoDemoMT4\":\"This is an example of the Demo <a href=\\\"https://my.example.net/trade/platform/MetaTrader4\\\">description link</a>.\",\"InfoDemoMT5\":\"This is an example of the Demo <a href=\\\"https://my.example.net/trade/platform/MetaTrader5\\\">description link</a>.\",\"InfoLiveCTrader\":\"Info Live CTrader\",\"InfoLiveMT4\":\"This is an example of the Live <a href=\\\"https://my.example.net/trade/platform/MetaTrader4\\\">description link</a>.\",\"InfoLiveMT5\":\"This is an example of the Live <a href=\\\"https://my.example.net/trade/platform/MetaTrader5\\\">description link</a>.\",\"Instrument\":\"Instrument\",\"Insufficient funds\":\"Insufficient funds\\t\",\"Internal Transfer Confirmation\":\"Internal Transfer Confirmation\\t\",\"InternalTransfer.Button.GoToHomePage\":\"Go to home page\",\"PageTitle.BrokerageAccountEventsHistory\":\"\",\"PageTitle.Conversion\":\"Conversion_test\",\"PageTitle.Eqwire\":\"\",\"PageTitle.Eqwire.AccountDetails\":\"AccountDetails_test\",\"PageTitle.Eqwire.Accounts\":\"Accounts_test\",\"PageTitle.Eqwire.AccountsList\":\"AccountsList_test\",\"PageTitle.Eqwire.Beneficiaries\":\"Beneficiaries_test\",\"PageTitle.Eqwire.Consent\":\"Consent_test\",\"PageTitle.Eqwire.Login\":\"Login_test\",\"PageTitle.Eqwire.Onboarding\":\"Onboarding_test\",\"PageTitle.Eqwire.PaymentsAndTransfers\":\"PaymentsAndTransfers_test\",\"PageTitle.Error\":\"\",\"PageTitle.External\":\"\",\"PageTitle.Guest\":\"\",\"PageTitle.Home\":\"\",\"PageTitle.InternalTransferConversion\":\"\",\"PageTitle.MAM\":\"MAM_test\",\"PageTitle.Pamm\":\"Pamm_test\",\"PageTitle.Payment\":\"\",\"PageTitle.PaymentConversion\":\"\",\"PageTitle.Payout\":\"\",\"PageTitle.PayoutConversion\":\"\",\"PageTitle.Pbsr.Briefcase\":\"Briefcase_test\",\"PageTitle.Pbsr.BriefcaseInfo\":\"BriefcaseInfo_test\",\"PageTitle.Pbsr.Catalog\":\"\",\"PageTitle.Pbsr.CreateNewAccount\":\"\",\"PageTitle.Pbsr.Deposit\":\"\",\"PageTitle.Pbsr.Documents\":\"\",\"PageTitle.Pbsr.EnrolmentAndTransfers\":\"\",\"PageTitle.Pbsr.EventsAndAssignments\":\"\",\"PageTitle.Pbsr.HelpAndFaq\":\"\",\"PageTitle.Pbsr.NewsAndOverviews\":\"\",\"PageTitle.Pbsr.PersonalInfo\":\"\",\"PageTitle.Pbsr.PlaceOrder\":\"\",\"PageTitle.Pbsr.ReportsAndCertificates\":\"\",\"PageTitle.Pbsr.Testing\":\"\",\"PageTitle.Pbsr.TradeIdeas\":\"\",\"PageTitle.Pbsr.TransactionsAndRequests\":\"\",\"PageTitle.Pbsr.Transfer\":\"\",\"PageTitle.Pbsr.VerificationSteps\":\"\",\"PageTitle.Pbsr.Withdraw\":\"\",\"PageTitle.Pbsr.WithdrawalOrders\":\"\",\"PageTitle.Public\":\"\",\"PageTitle.Tr\":\"\",\"PageTitle.TradeAccountSettings\":\"\",\"PageTitle.TransferConversion\":\"\",\"PLACED\":\"Placed\",\"POSITION_CANCELED\":\"Cancelled\",\"Partner.Create.Single.Description\":\"Description\",\"Partner.Create.Single.Text\":\"Single.Text\",\"Partner.PartnerBoard.Widgets.AverageTraderLifetime.Description.DaysMax [daysMax]\":\"\",\"Partner.PartnerBoard.Widgets.AverageTraderLifetime.Description.HoursMin [hoursMin]\":\"\",\"Partner.PartnerBoard.Widgets.AverageTraderLifetime.Title.DaysAvg [daysAvg]\":\"\",\"Partner.PartnerBoard.Widgets.LeadConversion.Description.Leads [leads]\":\"\",\"Partner.PartnerBoard.Widgets.LeadConversion.Description.Traders [traders]\":\"\",\"Partner.PartnerBoard.Widgets.TrafficSource.FormulaCom\":\"\",\"Partner.PartnerBoard.Widgets.TrafficSource.Rank [rank]\":\"\",\"Partner.Reports.Deposits.ColumnLabel.ClientEmail\":\"\",\"Partner.Reports.Trades.ColumnLabel.Symbol\":\"\",\"Partner.Reports.Withdrawals.ColumnLabel.ClientEmail\":\"\",\"Payment.BankRequisites.BeneficiaryBankName\":\"\",\"Payment.BankRequisites.BeneficiaryBankSwift\":\"\",\"Payment.BankRequisites.Bik\":\"\",\"Payment.BankRequisites.CorrespondentAccount\":\"\",\"Payment.BankRequisites.Currency\":\"\",\"Payment.BankRequisites.Inn\":\"\",\"Payment.BankRequisites.Kpp\":\"\",\"Payment.BankRequisites.PaymentPurpose\":\"\",\"Payment.BankRequisites.Recipient\":\"\",\"Payment.BankRequisites.SettlementAccount\":\"\",\"Payment.PaymentPurpose.Tooltip\":\"\",\"Payment.RequisitesTable.Title\":\"\",\"Payout.Wizards.Withdraw.Error.InsufficientFunds\":\"Insufficient Funds\",\"Payout.Wizards.Withdraw.Error.PreviousRequestInProgress\":\"Previous Request In Progress\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.BrokerageAccount\":\"Brokerage Account\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.Iis\":\"Iis\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.SubAccount\":\"Sub Account\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.SubAccount.Label.BrokerageCurrencyMarket\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.SubAccount.Label.BrokerageStockMarket\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.SubAccount.Label.IisCurrencyMarket\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AccountInfo.SubAccount.Label.IisStockMarket\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AssetInfoCard.CurrencyName.Eur\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AssetInfoCard.CurrencyName.Rub\":\"\",\"Pbsr.Briefcase.BriefcaseInfo.AssetInfoCard.CurrencyName.Usd\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Filters.Primary.FilterType.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Filters.Primary.FilterType.Deposit\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Filters.Primary.FilterType.Payout\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Filters.Primary.FilterType.Transfer\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Secondary.Filters.Status.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Secondary.Filters.Status.Failed\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Secondary.Filters.Status.Pending\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Consts.Secondary.Filters.Status.Success\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.FilteredOutAll\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Models.BrokerageOrder.Type.Payment\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Models.BrokerageOrder.Type.Payout\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Models.BrokerageOrder.Type.Transfer\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.NoItems\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.ShowMore\":\"\",\"Pbsr.BrokerageAccountEventsHistory.BrokerageOrders.Title\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Primary.FilterType.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Primary.FilterType.LimitAndMarketOrders\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Primary.FilterType.NonTradingOperation\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Primary.FilterType.StopOrders\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Primary.FilterType.Trades\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Side.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Side.Buy\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Side.Sell\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Status.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Status.Failed\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Status.Pending\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Status.Success\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Type.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Type.Limit\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Type.Market\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Type.Stop\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Consts.Filters.Secondary.Type.StopLimit\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.FilteredOutAll\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Order.Limit\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Order.Market\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Order.Stop\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Order.StopLimit\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Trade.Buy\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Models.Trade.Sell\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.NoItems\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.ShowMore\":\"\",\"Pbsr.BrokerageAccountEventsHistory.OperationsAndOrders.Title\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.Account\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.AccountFromTo\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.Amount\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.AmountWithCommission\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.Name\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.Quantity\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventCard.QuantityTitle\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.April\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.August\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.December\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.February\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.January\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.July\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.June\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.March\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.May\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.November\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.October\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Consts.MonthsTranslationsKeys.September\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.BrokerageAccountEventList.Today\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.Apply\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.BrokerageAccountsSelector.Label\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.BrokerageAccountsSelector.Services.BrokerageAccountsOptionsService.Option.All\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.FilterTitle\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.Reset\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Components.FilterDialog.SearchInstrumentLabel\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.AllTime\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.Manual\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.NineMonth\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.OneMonth\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.OneYear\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.SixMonth\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.ThreeMonth\":\"\",\"Pbsr.BrokerageAccountEventsHistory.Shared.Consts.DateFilters.Today\":\"\",\"Pbsr.Directives.LockWarpOrderPlacing.NonTradable\":\"\",\"Pbsr.Directives.LockWarpOrderPlacing.NonTradableNow [time]\":\"\",\"Pbsr.Models.NonTradingOperation.AdditionalAccrualCommissionToMinimumSize\":\"\",\"Pbsr.Models.NonTradingOperation.AdditionalBrokerageCommission\":\"\",\"Pbsr.Models.NonTradingOperation.AgentRemuneration\":\"\",\"Pbsr.Models.NonTradingOperation.AmendmentRevocationOfInstructionsFee\":\"\"}}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 29 Jul 2024 04:20:20 GMT"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 195, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 321291}, "startedDateTime": "2024-07-29T13:20:16.416Z", "time": 759, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 759}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v1/verification/levels"}, "response": {"bodySize": 26137, "content": {"mimeType": "application/json", "size": 26137, "text": "{\"status\":200,\"meta\":{\"behaviours\":[],\"status\":200},\"data\":[{\"id\":1,\"caption\":\"Level 0\",\"localizedCaption\":\"Level 0\",\"description\":\"<p>         <strong>Verification Level 0</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>     <p>                 <br />                 <strong>KYC Requirements for Level 0:</strong>         </p>         <ul>                 <li>E-mail verification</li>  <li>Phone number verification</li>       </ul>         <p>                 <strong>To get access to trading and crypto deposits/withdrawals please follow the Next Step</strong>         </p>\",\"mobile_description\":\"\",\"localizedDescription\":\"<p>         <strong>Verification Level 0</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>     <p>                 <br />                 <strong>KYC Requirements for Level 0:</strong>         </p>         <ul>                 <li>E-mail verification</li>  <li>Phone number verification</li>       </ul>         <p>                 <strong>To get access to trading and crypto deposits/withdrawals please follow the Next Step</strong>         </p>\",\"localizedMobileDescription\":\"\",\"index\":0,\"quizzes\":[{\"id\":55,\"verification_level_id\":1,\"quiz_id\":26,\"created_at\":\"2023-06-29T10:36:39.000000Z\",\"updated_at\":\"2023-06-29T10:36:39.000000Z\"},{\"id\":56,\"verification_level_id\":1,\"quiz_id\":29,\"created_at\":\"2023-06-29T10:36:39.000000Z\",\"updated_at\":\"2023-06-29T10:36:39.000000Z\"}]},{\"id\":2,\"caption\":\"Level 1\",\"localizedCaption\":\"Level 1\",\"description\":\"<p>         <strong>Verification Level 1</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>  \\r\\n\",\"mobile_description\":\"{\\r\\n\\t\\\"features\\\": [\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto-to-Crypto Trading\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": true,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 1\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto-to-Fiat Trading\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": true,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 1\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto Deposits\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto Withdrawals\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Fiat Deposits\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Fiat Withdrawals\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t}\\r\\n\\t],\\r\\n\\t\\\"levelDescription\\\": \\\"KYC Requirements for Level 0: E-mail verification , Phone number verification\\\"\\r\\n}\",\"localizedDescription\":\"<p>         <strong>Verification Level 1</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>  \\r\\n\",\"localizedMobileDescription\":\"{\\r\\n\\t\\\"features\\\": [\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto-to-Crypto Trading\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": true,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 1\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto-to-Fiat Trading\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": true,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 1\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto Deposits\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Crypto Withdrawals\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Fiat Deposits\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t},\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"name\\\": \\\"Fiat Withdrawals\\\",\\r\\n\\t\\t\\t\\\"isAvailable\\\": false,\\r\\n\\t\\t\\t\\\"availableAtLevel\\\": \\\"Level 2\\\"\\r\\n\\t\\t}\\r\\n\\t],\\r\\n\\t\\\"levelDescription\\\": \\\"KYC Requirements for Level 0: E-mail verification , Phone number verification\\\"\\r\\n}\",\"index\":1,\"quizzes\":[]},{\"id\":3,\"caption\":\"Level 2\",\"localizedCaption\":\"Level 2\",\"description\":\"<p>         <strong>Verification Level 2</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>   \\r\\n\",\"mobile_description\":\"\",\"localizedDescription\":\"<p>         <strong>Verification Level 2</strong>         <br />         <br />        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features</strong>                                 </td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed</strong>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"> </span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits</td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals</td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-success\\\" aria-hidden=\\\"true\\\"></span>                                         </center>                                 </td>                         </tr>                 </tbody>         </table>   \\r\\n\",\"localizedMobileDescription\":\"\",\"index\":2,\"quizzes\":[]},{\"id\":4,\"caption\":\"test\",\"localizedCaption\":\"test\",\"description\":\"test\",\"mobile_description\":\"\",\"localizedDescription\":\"test\",\"localizedMobileDescription\":\"\",\"index\":3,\"quizzes\":[]}],\"modules\":[]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:20 GMT"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 195, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 26332}, "startedDateTime": "2024-04-15T11:27:19.990Z", "time": 362, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 362}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/profile"}, "response": {"bodySize": 10158, "content": {"mimeType": "application/json", "size": 10158, "text": "{\"id\":779,\"email\":\"<EMAIL>\",\"locale\":\"en_US\",\"status\":\"active\",\"nickname\":null,\"photo\":\"f112b8e5b91b54b2cae39f67fe8877ad.jpeg\",\"maskedEmail\":\"v*****************@********.com\",\"clientUiConfig\":{\"showId\":true,\"allowChangeUserPic\":true,\"allowChangeNickname\":true,\"allowShowEmail\":true},\"info\":{\"firstName\":\"\",\"lastName\":\"\",\"middleName\":\"\",\"birthday\":null},\"phone\":{\"id\":826,\"phone\":\"+79501234567\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"****** ***-**-67\"},\"phones\":[{\"id\":826,\"phone\":\"+79501234567\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"****** ***-**-67\"},{\"id\":824,\"phone\":\"+817090407440\",\"ext\":null,\"type\":null,\"confirm\":true,\"default\":false,\"maskedPhone\":\"+81 70-9***-**40\"}],\"addresses\":[],\"type\":{\"id\":1,\"default\":true,\"enabled\":true,\"name\":\"Individual\",\"group\":\"individual\",\"wizard\":false},\"country\":null,\"verificationLevel\":{\"id\":1,\"index\":0,\"name\":\"Level 0\",\"description\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"localizedDescription\":\"<p>         <strong>Verification Level 0<\\/strong>         <br \\/>         <br \\/>        <table class=\\\"table table-striped\\\" role=\\\"grid\\\">                 <tbody>                         <tr>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <strong>Features<\\/strong>                                 <\\/td>                                 <td style=\\\"     background-color: #151d24; \\\">                                         <center>                                                 <strong>Allowed<\\/strong>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto-to-Crypto Trading<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"> <\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto-to-Fiat Trading<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Crypto Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Crypto Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #3e4e5a; \\\">Fiat Deposits<\\/td>                                 <td style=\\\"     background-color: #3e4e5a; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                         <tr>                                 <td style=\\\"     background-color: #212e38; \\\">Fiat Withdrawals<\\/td>                                 <td style=\\\"     background-color: #212e38; \\\">                                         <center>                                                 <span class=\\\"glyphicon glyphicon-error\\\" aria-hidden=\\\"true\\\"><\\/span>                                         <\\/center>                                 <\\/td>                         <\\/tr>                 <\\/tbody>         <\\/table>     <p>                 <br \\/>                 <strong>KYC Requirements for Level 0:<\\/strong>         <\\/p>         <ul>                 <li>E-mail verification<\\/li>  <li>Phone number verification<\\/li>       <\\/ul>         <p>                 <strong>To get access to trading and crypto deposits\\/withdrawals please follow the Next Step<\\/strong>         <\\/p>\",\"mailDescription\":\"\",\"mobileDescription\":\"\",\"localizedMobileDescription\":\"\",\"nextLevel\":2,\"roleId\":1,\"wizard\":\"B2B\\\\TCA\\\\Verification\\\\Wizards\\\\DocumentsWizard\",\"default\":false,\"visible\":true,\"sustainable\":false,\"limits\":{\"total\":1,\"data\":[{\"id\":109,\"currencyCode\":840,\"dailyDeposit\":\"-1.000000000000000000\",\"dailyWithdrawal\":\"-1.000000000000000000\",\"dailyBuy\":\"0.000000000000000000\",\"dailySell\":\"0.000000000000000000\",\"dailyInternalTransfer\":\"0.000000000000000000\",\"monthlyWithdrawal\":\"-1.000000000000000000\",\"monthlyBuy\":\"0.000000000000000000\",\"monthlySell\":\"0.000000000000000000\",\"autoWithdrawal\":\"0.000000000000000000\",\"transferMin\":\"10.000000000000000000\"}]},\"options\":{\"document_groups\":\"corporate\"}},\"lastLoginTime\":\"2024-04-15T02:27:17+00:00\",\"createTime\":\"2024-01-16T08:38:29+00:00\",\"updateTime\":\"2024-04-15T02:27:17+00:00\",\"limits\":{\"maxDemoTradingAccounts\":null,\"maxLiveTradingAccounts\":null}}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:20 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "594"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 10427}, "startedDateTime": "2024-04-15T11:27:19.990Z", "time": 371, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 371}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/2fa"}, "response": {"bodySize": 151, "content": {"mimeType": "application/json", "size": 151, "text": "{\"total\":2,\"data\":[{\"caption\":\"Google Authenticator\",\"name\":\"google\",\"isEnabled\":false},{\"caption\":\"SMS Confirmation\",\"name\":\"sms\",\"isEnabled\":false}]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:22 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "593"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 420}, "startedDateTime": "2024-04-15T11:27:21.769Z", "time": 332, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 332}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "limit", "value": "20"}, {"name": "offset", "value": "0"}], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v1/payouts/whitelist?limit=20&offset=0"}, "response": {"bodySize": 205, "content": {"mimeType": "application/json", "size": 205, "text": "{\"status\":500,\"meta\":{\"behaviours\":[],\"status\":500},\"data\":{\"code\":18,\"message\":\"Unknown error FORBIDDEN with code 18\",\"localizedMessage\":\"Unknown error FORBIDDEN with code 18\",\"previous\":[]},\"modules\":[]}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:22 GMT"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 195, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 400}, "startedDateTime": "2024-04-15T11:27:21.768Z", "time": 330, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 330}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/anti-phishing-code"}, "response": {"bodySize": 13, "content": {"mimeType": "application/json", "size": 13, "text": "{\"code\":null}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:22 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "592"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 282}, "startedDateTime": "2024-04-15T11:27:21.768Z", "time": 335, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 335}}, {"cache": {}, "request": {"bodySize": 0, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "GET", "queryString": [{"name": "businessProcess", "value": "changePassword"}], "totalSize": 596, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/2fa/challenge?businessProcess=changePassword"}, "response": {"bodySize": 107, "content": {"mimeType": "application/json", "size": 107, "text": "{\"token\":\"3a0f39fa-b633-477d-a6fb-2e7fda948768\",\"provider\":\"email\",\"expiresAt\":\"2024-04-15T02:37:28+00:00\"}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, no-store, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:28 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "591"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 269, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 376}, "startedDateTime": "2024-04-15T11:27:27.711Z", "time": 509, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 509}}, {"cache": {}, "request": {"bodySize": 63, "cookies": [], "headers": [{"name": "Authorization", "value": "**"}], "headersSize": 596, "httpVersion": "h2", "method": "POST", "postData": {"mimeType": "application/json; charset=utf-8", "text": "{\"token\":\"3a0f39fa-b633-477d-a6fb-2e7fda948768\",\"code\":\"52446\"}"}, "queryString": [], "totalSize": 659, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/2fa/challenge"}, "response": {"bodySize": 95, "content": {"mimeType": "application/json", "size": 95, "text": "{\"successToken\":\"f7e911e6-143f-4a90-9ab9-8f11e4a36952\",\"expiresAt\":\"2024-04-15T02:32:45+00:00\"}"}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:27:45 GMT"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 207, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 302}, "startedDateTime": "2024-04-15T11:27:44.926Z", "time": 422, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 422}}, {"cache": {}, "request": {"bodySize": 23, "cookies": [], "headers": [{"name": "Successtoken", "value": "f7e911e6-143f-4a90-9ab9-8f11e4a36952"}, {"name": "Authorization", "value": "**"}], "headersSize": 648, "httpVersion": "h2", "method": "POST", "postData": {"mimeType": "application/json; charset=utf-8", "text": "{\"password\":\"Test123@\"}"}, "queryString": [], "totalSize": 671, "url": "https://api.testmobile-android.b2broker.tech/api/v2/my/password/change"}, "response": {"bodySize": 0, "content": {"mimeType": "application/json", "size": 0}, "cookies": [], "headers": [{"name": "server", "value": "openresty"}, {"name": "content-type", "value": "application/json"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "cache-control", "value": "no-cache, private"}, {"name": "date", "value": "Mon, 15 Apr 2024 02:28:10 GMT"}, {"name": "x-ratelimit-limit", "value": "600"}, {"name": "x-ratelimit-remaining", "value": "590"}, {"name": "x-api-version", "value": "2.1.0"}, {"name": "access-control-allow-credentials", "value": "true"}], "headersSize": 259, "httpVersion": "h2", "redirectURL": "", "status": 200, "statusText": "", "totalSize": 259}, "startedDateTime": "2024-04-15T11:28:09.493Z", "time": 501, "timings": {"comment": "The information described by this object is incomplete.", "connect": 0, "receive": 0, "send": 0, "wait": 501}}], "version": "1.2"}}