package com.b2broker.b2core.event

import cafe.adriel.voyager.core.registry.ScreenRegistry
import cafe.adriel.voyager.core.screen.Screen
import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.BlankRoute
import com.b2broker.b2core.model.auth.AuthState
import com.b2broker.b2core.model.update.Update
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.navigation.push
import com.b2broker.b2core.navigation.replaceAll
import com.b2broker.b2core.presentation.event.default.SnackbarMessageEvent
import com.b2broker.b2core.presentation.event.handler.EventHandler
import com.b2broker.b2core.text.asTextResource
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class MainActivityEventHandlerTest {

    private val anyScreen: Screen = mockk()
    private val blankScreen: BlankRoute = mockk()
    private val startScreen: Screen = mockk()
    private val signInScreen: Screen = mockk()
    private val mainScreen: Screen = mockk()
    private val appUpdate: Screen = mockk()
    private val lockScreen: Screen = mockk()
    private val biometricAuthScreen: Screen = mockk()
    private val navigator: Navigator = mockk(relaxed = true)
    private val eventHandler = MainActivityEventHandler(navigator)
    private val testHandler: EventHandler = mockk<EventHandler>(relaxed = true)

    @BeforeEach
    fun setUp() {
        ScreenRegistry.register<SharedRoute.Start> { startScreen }
        ScreenRegistry.register<SharedRoute.SignIn> { signInScreen }
        ScreenRegistry.register<SharedRoute.Main> { mainScreen }
        ScreenRegistry.register<SharedRoute.AppUpdate> { appUpdate }
        ScreenRegistry.register<SharedRoute.Lock> { lockScreen }
        ScreenRegistry.register<SharedRoute.BiometricAuth> { biometricAuthScreen }
    }

    @Test
    fun `GIVEN user in Authorized state, lastItem BlankScreen WHEN handle THEN MainScreen opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.Authorized)
        every { navigator.lastItem } returns blankScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.Main()) }
    }

    @Test
    fun `GIVEN user in Authorized state, lastItem BiometricAuthScreen WHEN handle THEN MainScreen opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.Authorized)
        every { navigator.lastItem } returns biometricAuthScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.Main()) }
    }

    @Test
    fun `GIVEN user in Unauthorized state, lastItem BlankScreen WHEN handle THEN Start opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.Unauthorized)
        every { navigator.lastItem } returns blankScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.Start) }
    }

    @Test
    fun `GIVEN user in NeedBioAuthenticate state, lastItem BlankScreen WHEN handle THEN BiometricAuth opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.NeedBioAuthenticate)
        every { navigator.lastItem } returns blankScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.BiometricAuth) }
    }

    @Test
    fun `GIVEN user in AuthorizedWithoutBiometry state, lastItem BlankScreen WHEN handle THEN SignIn sequence opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.AuthorizedWithoutBiometry)
        every { navigator.lastItem } returns blankScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.Start, SharedRoute.SignIn) }
    }

    @Test
    fun `GIVEN user in Unauthorized state, lastItem is random Screen WHEN handle THEN SignIn sequence opened`() {
        // GIVEN
        val event = AuthStateChangedEvent(AuthState.Unauthorized)
        every { navigator.lastItem } returns anyScreen

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replaceAll(SharedRoute.Start, SharedRoute.SignIn) }
    }

    @Test
    fun `GIVEN PendingUpdateEvent WHEN handle THEN push AppUpdate screen`() = runTest {
        // GIVEN
        val event = PendingUpdateEvent(Update.fixture())

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.push(SharedRoute.AppUpdate(event.update)) }
    }

    @Test
    fun `GIVEN OsSoonWillBeUnsupportedEvent WHEN message shown`() = runTest {
        // GIVEN
        val message = "Message about OS".asTextResource()
        val event = OsSoonWillBeUnsupportedEvent(message)
        eventHandler.setNext(testHandler)

        // WHEN
        eventHandler.handle(event)

        // THEN
        val msgEvent = SnackbarMessageEvent(message)
        verify(exactly = 1) { testHandler.handle(msgEvent) }
    }

    @Test
    fun `GIVEN ShouldLockAppEvent WHEN handle THEN push Lock screen`() = runTest {
        // GIVEN
        val event = ShouldLockAppEvent

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.push(lockScreen) }
    }
}
