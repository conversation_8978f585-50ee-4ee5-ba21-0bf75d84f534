import com.android.build.api.dsl.ApplicationExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import utils.BuildTypes
import utils.applyPlugin
import utils.configureAndroidKotlin
import utils.configureOnDeviceTests
import utils.configureUnitTests
import utils.getIntVersion
import utils.libs
import utils.parseVersion
import utils.qa
import utils.applicationDistributionFlavors

class ApplicationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            applyPlugin("android.application")
            applyPlugin("kotlin.android")
            applyPlugin("b2broker.detekt")

            extensions.configure<ApplicationExtension> {
                configureAndroidKotlin(this)

                defaultConfig {
                    val version = parseVersion()
                    versionName = version.name
                    versionCode = version.code

                    targetSdk = libs.getIntVersion("targetSdk")

                    vectorDrawables.useSupportLibrary = true
                }

                configureBuildTypes()

                buildFeatures {
                    buildConfig = true
                }

                packaging {
                    resources {
                        excludes += "/META-INF/{AL2.0,LGPL2.1}"
                    }
                }

                testOptions.animationsDisabled = true
            }

            applicationDistributionFlavors()

            dependencies {
                add("implementation", project(":core:logger"))
            }

            // Workaround for KT-49187
            tasks.withType<org.jetbrains.kotlin.gradle.internal.KaptWithoutKotlincTask>()
                .configureEach {
                    listOf("util", "file", "main", "jvm", "processing", "comp", "tree", "api", "parser", "code")
                        .flatMap { listOf("--add-opens", "jdk.compiler/com.sun.tools.javac.$it=ALL-UNNAMED") }
                        .forEach(kaptProcessJvmArgs::addAll)
                }
            configureUnitTests()
            configureOnDeviceTests()
        }
    }

    private fun ApplicationExtension.configureBuildTypes() {
        buildTypes {
            debug {
                applicationIdSuffix = ".dev"
                versionNameSuffix = "-debug"
                signingConfig = signingConfigs.getByName("debug")
            }

            release {
                isMinifyEnabled = true
                isShrinkResources = true

                proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                )
            }

            qa {
                initWith(getByName(BuildTypes.RELEASE))

                applicationIdSuffix = ".dev"
                versionNameSuffix = "-qa"
                signingConfig = signingConfigs.getByName("debug")
            }
        }
    }
}
