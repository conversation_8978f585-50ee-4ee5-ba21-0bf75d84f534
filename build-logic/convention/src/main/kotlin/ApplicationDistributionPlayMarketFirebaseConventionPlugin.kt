import base.DependentOnPlugin
import com.google.firebase.appdistribution.gradle.firebaseAppDistribution
import org.gradle.api.Project
import utils.Distributions
import utils.applyPlugin
import utils.findParameter
import utils.isFirebaseDistributionEnabled
import utils.whitelabel.applicationVendorFlavors
import utils.whitelabel.consolidateUploadDistributionTasks
import utils.whitelabel.fixIdInUploadDistributionTasks

class ApplicationDistributionPlayMarketFirebaseConventionPlugin :
    DependentOnPlugin("b2broker.application.whitelabel") {

    override fun applyAfterDependency(target: Project) {
        with(target) {
            // Disable locally and on MR to speed up builds
            if (!target.isFirebaseDistributionEnabled().get()) {
                logger.quiet("Firebase distribution disabled")
                return
            }

            applyPlugin("firebase.appdistribution")

            applicationVendorFlavors { vendor ->
                firebaseAppDistribution {
                    artifactType = findParameter("firebaseArtifactType") ?: "APK"
                    serviceCredentialsFile = vendor.firebaseCredentialsFile.path
                    groups = findParameter("firebaseGroup") ?: "feature-testing"
                }
            }

            fixIdInUploadDistributionTasks()
            consolidateUploadDistributionTasks(Distributions.PLAY_MARKET.flavorName)
        }
    }
}
