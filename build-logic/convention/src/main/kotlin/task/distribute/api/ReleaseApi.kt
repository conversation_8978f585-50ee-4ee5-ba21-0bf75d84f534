package task.distribute.api

import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Call
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import task.distribute.model.AuthResponse
import task.distribute.model.UploadResponse
import java.util.concurrent.TimeUnit

internal interface ReleaseApi {

    @FormUrlEncoded
    @POST("api/users/login")
    fun loginUser(
        @Field("email") email: String,
        @Field("password") password: String
    ): Call<AuthResponse>

    @Multipart
    @POST("api/v1/add-release/{id}")
    fun addRelease(
        @Header("Authorization") authorization: String,
        @Path("id") id: String,
        @Part file: MultipartBody.Part,
        @Part("enabled") enabled: RequestBody,
        @Part("rolloutPercent") rolloutPercent: RequestBody,
        @Part("newVersionName") newVersionName: RequestBody,
        @Part("newVersionCode") newVersionCode: RequestBody,
        @Part("releaseNotes") releaseNotes: RequestBody?,
        @Part("minSdk") minSdk: RequestBody,
    ): Call<UploadResponse>
}

internal fun ReleaseApi(baseUrl: String): ReleaseApi {
    val json = Json
    val contentType = "application/json".toMediaType()
    val jsonConverterFactory = json.asConverterFactory(contentType)
    val logger = HttpLoggingInterceptor { println("Retrofit: $it") }
    logger.level = HttpLoggingInterceptor.Level.BASIC

    val okHttpClient = OkHttpClient.Builder()
        .writeTimeout(10, TimeUnit.MINUTES)
        .addInterceptor(logger)
        .build()

    return Retrofit.Builder()
        .baseUrl(baseUrl)
        .addConverterFactory(jsonConverterFactory)
        .client(okHttpClient)
        .build()
        .create(ReleaseApi::class.java)
}
