package utils

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.api.provider.ListProperty
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension
import org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions
import org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile

/**
 * Configure base Kotlin + Android options
 */
internal fun Project.configureAndroidKotlin(commonExtension: CommonExtension<*, *, *, *, *, *>) {
    commonExtension.apply {
        compileSdk = libs.getIntVersion("compileSdk")
        buildToolsVersion = libs.getStringVersion("buildTools")

        defaultConfig {
            minSdk = libs.getIntVersion("minSdk")
        }

        compileOptions {
            isCoreLibraryDesugaringEnabled = true
            sourceCompatibility = JavaVersion.VERSION_11
            targetCompatibility = JavaVersion.VERSION_11
        }
    }

    configure<KotlinAndroidProjectExtension> {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    configureJvmCompilerOptions {
        // 1.9 Until JetBrains fixes the bug (https://youtrack.jetbrains.com/issue/KT-72036/K2-Expected-expression-FirNamedArgumentExpressionImpl-to-be-resolved)
        apiVersion.set(org.jetbrains.kotlin.gradle.dsl.KotlinVersion.KOTLIN_2_1)
        languageVersion.set(org.jetbrains.kotlin.gradle.dsl.KotlinVersion.KOTLIN_2_1)
        jvmTarget.set(JvmTarget.JVM_11)
    }

    enableExperimentalCoroutinesApi()

    dependencies {
        add("coreLibraryDesugaring", libs.findLibrary("android-desugar").get())
    }
}

internal fun Project.configureJvmCompilerOptions(
    action: KotlinJvmCompilerOptions.() -> Unit,
) {
    tasks.withType<KotlinJvmCompile> {
        compilerOptions {
            action()
        }
    }
}

operator fun ListProperty<String>.plusAssign(value: String) {
    add(value)
}

operator fun ListProperty<String>.plusAssign(values: List<String>) {
    addAll(values)
}

fun Project.enableExperimentalMaterial3Api() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api"
    }
}

fun Project.enableExperimentalComposeUiApi() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi"
    }
}

fun Project.enableExperimentalFoundationApi() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi"
    }
}

fun Project.enableExperimentalCoroutinesApi() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
    }
}

fun Project.enableFlowPreview() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=kotlinx.coroutines.FlowPreview"
    }
}

fun Project.enableExperimentalLayoutApi() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi"
    }
}

fun Project.enableExperimentalVoyagerApi() {
    configureJvmCompilerOptions {
        freeCompilerArgs += "-opt-in=cafe.adriel.voyager.core.annotation.ExperimentalVoyagerApi"
    }
}

