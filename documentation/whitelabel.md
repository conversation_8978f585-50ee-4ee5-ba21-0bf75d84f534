## Whitelabel

B2Brokers provides Android whitelabel B2Core application as part of B2Core package to our clients
(hereafter vendors). We may have hundreds of vendors and our project should be able to build, test
and distribute applications for any desired set of vendors, locally and on CI.

Whitelabel application must be customized for a specific vendor, including:

- Launcher icons
- Splash screen
- Theme colors
- App name
- App Id
- Release signature
- 3rd party tokens
- Deeplink hosts
- etc.

Current approach:<br/><PERSON><PERSON><PERSON> generates dynamic vendor flavors based on input parameters and looks
for a specific vendor configuration files in the `vendors/` dir.

### Vendor configuration

- Vendor id (`vendorId`) should be in lowercase and without any breaks.
- Configuration files for each vendor must be located in the separate folder in `vendors/` dir.
- Vendor configuration folder name == vendor id. Path will look like `vendors/{vendorId}/`.
  <br/>Structure:
  - `config.properties` - vendor configuration values (i.e `applicationId=com.vendor1.app`).
  - `app/` - resources like icons and strings for the `app` module
  - `auth/` - resources like logo and strings for the `auth` module
  - `designsystem/` - colors for `designsystem` module
  - `distribution/` - directory for for credentials `*.json` file for the distribution.
  - `google-services/` - directory for `google-services.json`

<b>Disclaimer:</b> vendor configuration structure is not final and will be changed during
application development process.

#### Vendor config.properties

Some vendor specific parameters should be specified in `config.properties` placed in the root of
vendor folder.

| Parameter                           | Description                                                                                              |
|:------------------------------------|:---------------------------------------------------------------------------------------------------------|
| `application.id`                    | Vendor Android application id.                                                                           |
| `update.project.id`                 | Release portal vendor project id.                                                                        |
| `firebase.distribution.credentials` | Firebase credentials file name. Should be placed in distribution folder. (Will be move later to secrets) |
| `base.url`                          | Vendor backend base url.                                                                                 |

### Input parameters

Any input parameters could be set in (it's also a lookup order):

- Gradle parameters (i.e: `./gradlew assembleQa -PvendorIds="all"`)
- `local.properties` (i.e: `vendorIds=vendor1,vendor2`)
- Environment variables (i.e: `export firebaseArtifactType="APK"`)

#### Common input parameters

| Parameter   | Required | Values                                                     | Default    | Description                                                                                                                                                                                                                              |
|:------------|:---------|:-----------------------------------------------------------|:-----------|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `vendorIds` | Optional | `all`<br/>`vendor1` <br/>`vendor1,vendor2,..,vendorN`<br/> | `b2broker` | List of `vendorId` divided by `,` that will be added to the project configuration.<br/><br/>Vendor id should be equal to a vendor directory name in `vendors` directory.<br/><br/>Default value is always appended to a list of vendors. |

#### Build input parameters

| Parameter                    | Required  | Description                                                                           |
|:-----------------------------|:----------|:--------------------------------------------------------------------------------------|
| `{vendorId}KeyStorePath`     | Mandatory | Path to a release key store file. <br/>Parameter name example: `b2brokerKeyStorePath` |
| `{vendorId}KeyStorePassword` | Mandatory | Release key store password                                                            |
| `{vendorId}KeyAlias`         | Mandatory | Release key alias                                                                     |
| `{vendorId}KeyPassword`      | Mandatory | Release key password                                                                  |

#### Firebase distribution parameters

`ApplicationDistributionPlayMarketFirebaseConventionPlugin` is responsible for apk
distribution to firebase. It creates distribution tasks for each vendor with
name `appDistributionUpload{vendorId}{buildType}`. It also consolidates all vendor distribution
tasks into `appDistributionUpload{buildType}`.

| Parameter              | Required | Values                                                    | Default           | Description                                               |
|:-----------------------|:---------|:----------------------------------------------------------|:------------------|:----------------------------------------------------------|
| `firebaseArtifactType` | Optional | `APK`<br/>`AAB`                                           | `APK`             | Specifies your app's file type for firebase distribution. |
| `firebaseGroup`        | Optional | `feature-testing`<br/>`feature-testing, qa-team`<br/>etc. | `feature-testing` | The tester groups you want to distribute builds to.       |

#### Release portal distribution parameters

`ApplicationDistributionApkRegistryConventionPlugin` is responsible for apk
distribution to release portal. It creates distribution tasks for each vendor with
name `distributeApk{vendorId}Release`. It also consolidates all vendor distribution tasks
into `distributeApkRelease`.

| Parameter                | Required  | Values       | Default    | Description                                                                                           |
|:-------------------------|:----------|:-------------|:-----------|:------------------------------------------------------------------------------------------------------|
| `releasePortalBaseUrl`   | Mandatory |              |            | Base url for release portal                                                                           |
| `releaseManagerEmail`    | Mandatory |              |            | User credentials for release portal. (Should have robot role)                                         |
| `releaseManagerPassword` | Mandatory |              |            | User credentials for release portal.                                                                  |
| `releaseEnabled`         | Optional  | `true\false` | `false`    | Turn on / turn off the release                                                                        |
| `releaseStanding`        | Optional  | `true\false` | `false`    | Block this version of the app. Use it in unexpected cases with the backend.                           |
| `releaseRolloutPercent`  | Optional  | `0-100`      | `0`        | Percentage of rollout per user.                                                                       |
| `releaseMinSdkWarning`   | Optional  |              | `{minSdk}` | Version of Android where we warn that the application on this version of the OS will soon be blocked. |

### Current limitations

- Flavors other than vendor flavors currently not supported
- `vendors/` dir should not contain non-vendor directories
- `google-services.json` should contain config for both `release` and `dev` applicationIds.
- Changes in `local.properties` not tracked by Android Studio and may require manual gradle
  synchronization.
