package com.b2broker.b2core.data.confirmation

import arrow.core.left
import arrow.core.right
import arrow.retrofit.adapter.either.networkhandling.UnexpectedCallError
import com.b2broker.b2core.data.accountsecurity.mapper.TwoFaErrorMapper
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.accountsecurity.twofa.TwoFaError
import com.b2broker.b2core.model.confirmation.Confirmation
import com.b2broker.b2core.network.confirmation.ConfirmationApi
import com.b2broker.b2core.network.confirmation.model.ConfirmRequest
import com.b2broker.b2core.network.confirmation.model.ConfirmResponse
import com.b2broker.b2core.network.error.DefaultErrorMapper
import com.b2broker.b2core.text.TextResource
import com.google.common.truth.Truth
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class B2CoreConfirmationRepositoryTest {

    private val confirmationApi: ConfirmationApi = mockk()
    private val twoFaErrorMapper: TwoFaErrorMapper = mockk()
    private val defaultErrorMapper: DefaultErrorMapper = mockk()
    private val repository: B2CoreConfirmationRepository = B2CoreConfirmationRepository(
        confirmationApi,
        twoFaErrorMapper,
        defaultErrorMapper,
    )

    @BeforeEach
    fun setUp() {
        every { twoFaErrorMapper.map(any()) } returns TwoFaError.SourceError(TextResource.UNKNOWN_ERROR)
        every { defaultErrorMapper.map(any()) } returns DefaultError(TextResource.UNKNOWN_ERROR)
    }

    @Test
    fun `GIVEN resend api success WHEN resend THEN return Unit`() = runTest {
        // GIVEN
        coEvery { confirmationApi.resendConfirmationCode(any()) } returns Unit.right()

        // WHEN
        val actual = repository.resendConfirmationCode(UUID.randomUUID().toString())

        // THEN
        Truth.assertThat(actual).isEqualTo(Unit.right())
    }

    @Test
    fun `GIVEN resend api failure WHEN resend THEN return DefaultError`() = runTest {
        // GIVEN
        coEvery { confirmationApi.resendConfirmationCode(any()) } returns UnexpectedCallError(RuntimeException()).left()

        // WHEN
        val actual = repository.resendConfirmationCode(UUID.randomUUID().toString())

        // THEN
        Truth.assertThat(actual.isLeft()).isTrue()
        Truth.assertThat(actual.leftOrNull()).isInstanceOf(DefaultError::class.java)
    }

    @Test
    fun `GIVEN confirm api success WHEN confirm THEN return Result`() = runTest {
        // GIVEN
        val confirmRequest = ConfirmRequest.fixture()
        val confirmResponse = ConfirmResponse.fixture()
        coEvery { confirmationApi.confirm(confirmRequest) } returns confirmResponse.right()

        // WHEN
        val actual = repository.confirm(uuid = confirmRequest.uuid, code = confirmRequest.code)

        // THEN
        Truth.assertThat(actual.isRight()).isTrue()
        val expected = Confirmation.Result(uuid = confirmResponse.uuid, workflow = confirmResponse.workflow)
        Truth.assertThat(actual).isEqualTo(expected.right())
    }

    @Test
    fun `GIVEN confirm api failure WHEN confirm THEN return Error`() = runTest {
        // GIVEN
        val confirmRequest = ConfirmRequest.fixture()
        coEvery { confirmationApi.confirm(confirmRequest) } returns UnexpectedCallError(RuntimeException()).left()

        // WHEN
        val actual = repository.confirm(uuid = confirmRequest.uuid, code = confirmRequest.code)

        // THEN
        Truth.assertThat(actual.isLeft()).isTrue()
        Truth.assertThat(actual.leftOrNull()).isInstanceOf(TwoFaError::class.java)
    }
}
