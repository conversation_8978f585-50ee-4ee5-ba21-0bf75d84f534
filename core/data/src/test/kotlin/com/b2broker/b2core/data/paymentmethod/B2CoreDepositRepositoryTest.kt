package com.b2broker.b2core.data.paymentmethod

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.currency.CurrencyMapper
import com.b2broker.b2core.data.deposit.mapper.CreateDepositResponseMapper
import com.b2broker.b2core.data.deposit.mapper.DepositPreviewMapper
import com.b2broker.b2core.data.fields.fieldDtoMapper
import com.b2broker.b2core.data.funds.CreateFormErrorMapper
import com.b2broker.b2core.data.utils.ContentResolverWrapper
import com.b2broker.b2core.model.deposit.DepositPreview
import com.b2broker.b2core.model.deposit.DepositResult
import com.b2broker.b2core.model.deposit.DynamicForm
import com.b2broker.b2core.model.deposit.DynamicFormData
import com.b2broker.b2core.model.deposit.LegacyFields
import com.b2broker.b2core.model.fields.DynamicField
import com.b2broker.b2core.model.fields.DynamicFieldValue
import com.b2broker.b2core.network.deposit.DepositApi
import com.b2broker.b2core.network.deposit.model.CreateDepositResponse
import com.b2broker.b2core.network.deposit.model.DepositMethodsResponse
import com.b2broker.b2core.network.deposit.model.DepositPreviewRequest
import com.b2broker.b2core.network.deposit.model.DepositPreviewResponse
import com.b2broker.b2core.network.deposit.model.depositMethodDtoFixture
import com.b2broker.b2core.network.dynamicforms.model.CreateFormRequest
import com.b2broker.b2core.network.dynamicforms.model.CreateFormResponse
import com.b2broker.b2core.network.dynamicforms.model.CreatePSSDepositRequest
import com.b2broker.b2core.network.dynamicforms.model.PSSDataDto
import com.b2broker.b2core.network.error.DefaultErrorMapper
import com.google.common.truth.Truth
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test

class B2CoreDepositRepositoryTest {

    private val depositApi: DepositApi = mockk<DepositApi>()
    private val dispatcher = UnconfinedTestDispatcher()
    private val defaultErrorMapper = mockk<DefaultErrorMapper>()
    private val createFormResponseMapper = mockk<CreateFormResponseMapper>()
    private val depositPreviewMapper = mockk<DepositPreviewMapper>()
    private val createDepositResponseMapper = mockk<CreateDepositResponseMapper>()
    private val contentResolverWrapper = mockk<ContentResolverWrapper>()
    private val json = Json { }
    private val depositMethodDtoMapper: MethodDtoMapper = MethodDtoMapper(
        fieldDtoMapper = fieldDtoMapper,
        currencyMapper = CurrencyMapper(),
        defaultDispatcher = UnconfinedTestDispatcher(),
    )
    private val methodsDtoMapper: MethodsDtoMapper = MethodsDtoMapper(
        paymentMethodDtoMapper = depositMethodDtoMapper,
        defaultDispatcher = UnconfinedTestDispatcher(),
    )
    private val createFormErrorMapper = mockk<CreateFormErrorMapper>()
    private val depositRepository = B2CoreDepositRepository(
        depositApi = depositApi,
        defaultDispatcher = dispatcher,
        defaultErrorMapper = defaultErrorMapper,
        methodsDtoMapper = methodsDtoMapper,
        createFormResponseMapper = createFormResponseMapper,
        depositPreviewMapper = depositPreviewMapper,
        createDepositResponseMapper = createDepositResponseMapper,
        contentResolverWrapper = contentResolverWrapper,
        createFormErrorMapper = createFormErrorMapper,
        json = json,
    )
    private val mockResponse = listOf(
        depositMethodDtoFixture(id = 1),
        depositMethodDtoFixture(id = 2),
    )

    @Test
    fun `WHEN getDepositMethods is called for old flow THEN return a list of methods using getMethods`() = runTest {
        // GIVEN
        val expectedDomainMethods = methodsDtoMapper.map(mockResponse)
        coEvery { depositApi.getDepositsMethods() } returns mockResponse.right()

        // WHEN
        val actual = depositRepository.getMethods(false).getOrNull()

        // THEN
        Truth.assertThat(actual).isEqualTo(expectedDomainMethods)
        coVerify { depositApi.getDepositsMethods() }
        confirmVerified(depositApi)
    }

    @Test
    fun `WHEN getDepositMethods is called for new flow THEN return a list of methods using getMethods`() = runTest {
        // GIVEN
        val expectedDomainMethods = methodsDtoMapper.map(mockResponse)
        val response = DepositMethodsResponse(mockResponse)
        coEvery { depositApi.getNewFlowDepositMethods() } returns response.right()

        // WHEN
        val actual = depositRepository.getMethods(true).getOrNull()

        // THEN
        Truth.assertThat(actual).isEqualTo(expectedDomainMethods)
        coVerify { depositApi.getNewFlowDepositMethods() }
        confirmVerified(depositApi)
    }

    @Test
    fun `WHEN createForm is called THEN return deposit form`() = runTest {
        // GIVEN
        val createFormResponse = mockk<CreateFormResponse>()
        val form = mockk<Either<LegacyFields, DynamicForm>>()
        coEvery { depositApi.depositForm(any(), any()) } returns createFormResponse.right()
        coEvery { createFormResponseMapper.map(createFormResponse) } returns form
        val methodId = 321
        val accountId = "accountId"
        val currencyCode = 123
        val timeZone = "timeZone"
        val locale = "locale"

        // WHEN
        val actual = depositRepository.createForm(
            methodId = methodId,
            accountId = accountId,
            currencyCode = currencyCode,
            timeZone = timeZone,
            locale = locale,
        ).getOrNull()

        // THEN
        Truth.assertThat(actual).isEqualTo(form)
        coVerify {
            depositApi.depositForm(
                method = methodId,
                body = CreateFormRequest(
                    accountId = accountId,
                    currencyCode = currencyCode,
                    deviceTimezone = timeZone,
                    locale = locale,
                ),
            )
        }
        confirmVerified(depositApi)
    }

    @Test
    fun `WHEN depositPreview is called THEN return deposit preview`() = runTest {
        // GIVEN
        val response = mockk<DepositPreviewResponse>()
        val depositPreview = mockk<DepositPreview>()
        coEvery { depositApi.depositPreview(any()) } returns response.right()
        every { depositPreviewMapper.map(any()) } returns depositPreview

        val methodId = 1
        val accountId = "account_id"
        val currencyCode = 2
        val amount = "100"

        // WHEN
        val actual = depositRepository.depositPreview(
            methodId = methodId,
            accountId = accountId,
            currencyCode = currencyCode,
            amount = amount,
        )

        // THEN
        Truth.assertThat(actual).isEqualTo(depositPreview.right())
        coVerify {
            depositApi.depositPreview(
                DepositPreviewRequest(
                    methodId = methodId,
                    accountId = accountId,
                    currencyCode = currencyCode,
                    amount = amount,
                )
            )
        }
        confirmVerified(depositApi)
    }

    @Test
    fun `GIVEN deposit create data with fields WHEN createDeposit THEN correct endpoint called and mapped to result`() = runTest {
        // GIVEN
        val response = mockk<CreateDepositResponse>()
        val depositResult = mockk<DepositResult>()
        coEvery { depositApi.createLegacyDeposit(any()) } returns response.right()
        coEvery { createDepositResponseMapper.map(any()) } returns depositResult

        val methodId = 1
        val accountId = "account_id"
        val currencyCode = 2
        val amount = "100"

        // WHEN
        val actual = depositRepository.createDeposit(
            methodId = methodId,
            accountId = accountId,
            currencyCode = currencyCode,
            amount = amount,
            fieldsValue = emptyMap<DynamicField, DynamicFieldValue>().left()
        )

        // THEN
        Truth.assertThat(actual).isEqualTo(depositResult.right())
        coVerify { depositApi.createLegacyDeposit(any()) }
        confirmVerified(depositApi)
    }

    @Test
    fun `GIVEN deposit create data with form WHEN createDeposit THEN correct endpoint called and mapped to result`() = runTest {
        // GIVEN
        val response = mockk<CreateDepositResponse>()
        val depositResult = mockk<DepositResult>()
        coEvery { depositApi.createPSSDeposit(any()) } returns response.right()
        coEvery { createDepositResponseMapper.map(any()) } returns depositResult

        val methodId = 1
        val accountId = "account_id"
        val currencyCode = 2
        val amount = "100"
        val form = DynamicFormData(
            id = "id",
            formId = "form_id",
            fieldValues = mapOf(
                "a" to listOf("1"),
                "b" to listOf("2", "3"),
            ),
        )

        // WHEN
        val actual = depositRepository.createDeposit(
            methodId = methodId,
            accountId = accountId,
            currencyCode = currencyCode,
            amount = amount,
            fieldsValue = form.right()
        )

        // THEN
        Truth.assertThat(actual).isEqualTo(depositResult.right())
        val expectedRequest = CreatePSSDepositRequest(
            methodId = methodId,
            accountId = accountId,
            currencyCode = currencyCode,
            amount = amount,
            data = PSSDataDto(
                id = form.id,
                formId = form.formId,
                input = """{"a":["1"],"b":["2","3"]}""",
            ),
        )
        coVerify { depositApi.createPSSDeposit(expectedRequest) }
        confirmVerified(depositApi)
    }
}
