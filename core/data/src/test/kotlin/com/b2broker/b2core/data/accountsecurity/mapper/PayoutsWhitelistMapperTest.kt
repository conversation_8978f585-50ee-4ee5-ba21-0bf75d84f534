package com.b2broker.b2core.data.accountsecurity.mapper

import com.b2broker.b2core.network.accountsecurity.model.dto.WhitelistAddressDto
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.Test
import kotlin.random.Random

class PayoutsWhitelistMapperTest {

    @Test
    fun `GIVEN WhitelistAddressDto with wallet address more then 15 symbols WHEN toDomain is mapped THEN it correctly transforms to WhitelistAddress`() {
        // GIVEN
        val dto = WhitelistAddressDto.fixture(
            walletAddress = "****************",
        )

        // WHEN
        val domain = dto.toDomain()

        // THEN
        assertThat(domain.walletAddressFormat).isEqualTo("123456...123456")
        assertThat(domain.walletAddress).isEqualTo(dto.walletAddress)
        assertThat(domain.destinationTag).isEqualTo(dto.destinationTag)
        assertThat(domain.alpha).isEqualTo(dto.currency.alpha)
        assertThat(domain.caption).isEqualTo(dto.currency.localizedCaption)
        assertThat(domain.id).isEqualTo(dto.id)
    }

    @Test
    fun `GIVEN WhitelistAddressDto with wallet address less or equal 15 symbols WHEN toDomain is mapped THEN it correctly transforms to WhitelistAddress`() {
        // GIVEN
        val dto = WhitelistAddressDto.fixture(
            walletAddress = Random.nextLong(0, 999_999_999_999_999).toString(),
        )

        // WHEN
        val domain = dto.toDomain()

        // THEN
        assertThat(domain.walletAddressFormat).isEqualTo(dto.walletAddress)
        assertThat(domain.walletAddress).isEqualTo(dto.walletAddress)
        assertThat(domain.destinationTag).isEqualTo(dto.destinationTag)
        assertThat(domain.alpha).isEqualTo(dto.currency.alpha)
        assertThat(domain.caption).isEqualTo(dto.currency.localizedCaption)
        assertThat(domain.id).isEqualTo(dto.id)
    }
}
