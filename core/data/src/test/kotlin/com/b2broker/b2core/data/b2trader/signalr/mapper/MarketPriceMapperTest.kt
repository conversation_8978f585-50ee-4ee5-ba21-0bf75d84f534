package com.b2broker.b2core.data.b2trader.signalr.mapper

import com.b2broker.b2core.data.b2trader.notification.mapper.SubscriptionConditionMapper
import com.b2broker.b2core.model.b2trader.notification.MarketPrice
import com.b2broker.b2core.model.b2trader.notification.SubscriptionCondition
import com.b2broker.b2core.network.b2trader.ws.model.MarketPriceDto
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal

internal class MarketPriceMapperTest {

    private val conditionMapper: SubscriptionConditionMapper = SubscriptionConditionMapper()
    private val mapper = MarketPriceMapper(conditionMapper)

    @Test
    fun `GIVEN MarketPriceData WHEN map THEN return correct MarketPrice`() {
        // GIVEN
        val data = MarketPriceDto.fixture(
            marketId = "btc_usdt",
            price = "64.35",
            condition = "Gte",
            createdAt = "2024-02-15T12:52:43.085Z",
        )

        // WHEN
        val result = mapper.map(data)

        // THEN
        val expected = MarketPrice(
            marketId = "btc_usdt",
            price = BigDecimal("64.35"),
            condition = SubscriptionCondition.GREATER_THAN_OR_EQUAL,
            createdAt = "2024-02-15T12:52:43.085Z",
        )
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `GIVEN MarketPriceData with invalid price WHEN map THEN return MarketPrice with zero price`() {
        // GIVEN
        val data = MarketPriceDto.fixture(
            marketId = "btc_usdt",
            price = "invalid",
            condition = "Gte",
            createdAt = "2024-02-15T12:52:43.085Z",
        )

        // WHEN
        val result = mapper.map(data)

        // THEN
        val expected = MarketPrice(
            marketId = "btc_usdt",
            price = BigDecimal.ZERO,
            condition = SubscriptionCondition.GREATER_THAN_OR_EQUAL,
            createdAt = "2024-02-15T12:52:43.085Z",
        )
        assertThat(result).isEqualTo(expected)
    }
}
