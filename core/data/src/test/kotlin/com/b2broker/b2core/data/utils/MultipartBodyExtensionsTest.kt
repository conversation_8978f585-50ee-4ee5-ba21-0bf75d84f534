package com.b2broker.b2core.data.utils

import android.net.Uri
import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.model.fields.AmountFieldValue
import com.b2broker.b2core.model.fields.DateField
import com.b2broker.b2core.model.fields.DateFieldValue
import com.b2broker.b2core.model.fields.DynamicField
import com.b2broker.b2core.model.fields.DynamicFieldValue
import com.b2broker.b2core.model.fields.EmptyFieldValue
import com.b2broker.b2core.model.fields.FieldOption
import com.b2broker.b2core.model.fields.FileField
import com.b2broker.b2core.model.fields.FileFieldValue
import com.b2broker.b2core.model.fields.FileInfo
import com.b2broker.b2core.model.fields.InputField
import com.b2broker.b2core.model.fields.InputFieldValue
import com.b2broker.b2core.model.fields.PhoneField
import com.b2broker.b2core.model.fields.PhoneFieldValue
import com.b2broker.b2core.model.fields.ReadonlyField
import com.b2broker.b2core.model.fields.SelectField
import com.b2broker.b2core.model.fields.SelectFieldValue
import com.b2broker.b2core.model.phonenumber.PhoneCountryData
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.junit.jupiter.api.Test
import java.io.ByteArrayInputStream

class MultipartBodyExtensionsTest {

    @Test
    fun `GIVEN multipart WHEN addFields THEN correct values should be added`() = runTest {
        // GIVEN
        val multiPart = mockk<MultipartBody.Builder>(relaxed = true)
        val contentResolverWrapper = mockk<ContentResolverWrapper>(relaxed = true)
        val mimeType = "image/jpg"
        val uri = mockk<Uri>(relaxed = true)
        every { contentResolverWrapper.getMimeType(any()) } returns mimeType
        val byteArray = byteArrayOf(1, 2, 3, 4, 5)
        coEvery { contentResolverWrapper.openInputStream(any()) } returns ByteArrayInputStream(byteArray)
        val fields = mapOf<DynamicField, DynamicFieldValue>(
            InputField.fixture(name = "input") to InputFieldValue("input_value"),
            InputField.fixture(name = "amount") to InputFieldValue("100"),
            InputField.fixture(name = "amount_field") to AmountFieldValue(Currency.USD, "100"),
            PhoneField.fixture(name = "phone") to PhoneFieldValue(
                countryData = PhoneCountryData(
                    code = "",
                    countryPhoneCode = "+1",
                    countryName = "",
                ),
                numberWithoutCode = "234567"
            ),
            SelectField.fixture(name = "select") to SelectFieldValue(FieldOption.fixture(value = "option")),
            ReadonlyField.fixture(name = "readonly", initialValue = "readonly_value") to EmptyFieldValue,
            FileField.fixture(name = "file") to FileFieldValue(listOf(FileInfo.fixture(uri = uri, fileName = "test.jpg"))),
            DateField.fixture(name = "date") to DateFieldValue(epochMs = 0, uiFormat = "", serverFormat = "2000-05-12"),
        )

        // WHEN
        multiPart.addFields(fields, contentResolverWrapper)

        // THEN
        coVerify {
            multiPart.addFormDataPart("data[input]", "input_value")
            multiPart.addFormDataPart("data[amount]", "100")
            multiPart.addFormDataPart("data[amount_field]", "100")
            multiPart.addFormDataPart("data[phone]", "+1234567")
            multiPart.addFormDataPart("data[select]", "option")
            multiPart.addFormDataPart("data[readonly]", "readonly_value")
            multiPart.addPart(
                eq(
                    value = MultipartBody.Part.createFormData(
                        name = "data[file][0]",
                        filename = "test.jpg",
                        body = byteArrayOf(1, 2, 3, 4, 5).toRequestBody(mimeType.toMediaType())
                    ),
                    inverse = true,
                )
            )
            multiPart.addFormDataPart("data[date]", "2000-05-12")

            contentResolverWrapper.getMimeType(uri)
            contentResolverWrapper.openInputStream(uri)
        }
        confirmVerified(multiPart, contentResolverWrapper)
    }
}
