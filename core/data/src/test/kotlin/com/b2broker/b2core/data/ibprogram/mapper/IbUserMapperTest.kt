package com.b2broker.b2core.data.ibprogram.mapper

import com.b2broker.b2core.model.ibprogram.IbActiveProgram
import com.b2broker.b2core.network.ibprogram.model.IbUserDto
import com.b2broker.b2core.network.ibprogram.model.IbUsersPageDto
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.Test

internal class IbUserMapperTest {
    private val mapper = IbUserMapper()

    @Test
    fun `GIVEN IbUsersPageDto with Users WHEN map THEN list of user`() {
        // GIVEN
        val response = IbUsersPageDto.fixture(
            users = listOf(
                IbUserDto.fixture(
                    userId = "1",
                    groupName = "groupName",
                    groupDescription = null,
                ),
            ),
        )
        val expected = listOf(
            IbActiveProgram(
                programId = "1",
                groupName = "groupName",
                groupDescription = null,
            ),
        )

        // WHEN
        val actual = mapper.map(response)

        // THEN
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `GIVEN IbUsersPageDto without Users WHEN map THEN empty`() {
        // GIVEN
        val response = IbUsersPageDto.fixture()
        val expected = emptyList<IbActiveProgram>()

        // WHEN
        val actual = mapper.map(response)

        // THEN
        assertThat(actual).isEqualTo(expected)
    }
}
