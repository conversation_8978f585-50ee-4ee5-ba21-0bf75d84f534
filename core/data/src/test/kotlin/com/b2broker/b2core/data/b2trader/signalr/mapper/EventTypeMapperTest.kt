package com.b2broker.b2core.data.b2trader.signalr.mapper

import com.b2broker.b2core.model.b2trader.notification.NotificationEventType
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class EventTypeMapperTest {

    private lateinit var mapper: EventTypeMapper

    @BeforeEach
    fun setUp() {
        mapper = EventTypeMapper()
    }

    @Test
    fun `GIVEN valid event type SubscriptionCreated WHEN map THEN returns SUBSCRIPTION_CREATED`() {
        // GIVEN
        val input = "SubscriptionCreated"

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isEqualTo(NotificationEventType.SUBSCRIPTION_CREATED)
    }

    @Test
    fun `GIVEN valid event type SubscriptionDeleted WHEN map THEN returns SUBSCRIPTION_DELETED`() {
        // GIVEN
        val input = "SubscriptionDeleted"

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isEqualTo(NotificationEventType.SUBSCRIPTION_DELETED)
    }

    @Test
    fun `GIVEN valid event type with mixed case WHEN map THEN returns correct event type`() {
        // GIVEN
        val input = "subScriptionCreaTed"

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isEqualTo(NotificationEventType.SUBSCRIPTION_CREATED)
    }

    @Test
    fun `GIVEN invalid event type WHEN map THEN returns null`() {
        // GIVEN
        val input = "INVALID"

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isNull()
    }

    @Test
    fun `GIVEN null input WHEN map THEN returns null`() {
        // GIVEN
        val input = null

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isNull()
    }

    @Test
    fun `GIVEN empty string WHEN map THEN returns null`() {
        // GIVEN
        val input = ""

        // WHEN
        val result = mapper.map(input)

        // THEN
        assertThat(result).isNull()
    }
}
