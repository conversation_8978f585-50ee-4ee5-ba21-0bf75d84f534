package com.b2broker.b2core.data.b2trader.notification.mapper

import com.b2broker.b2core.model.b2trader.notification.MarketPriceSubscription
import com.b2broker.b2core.model.b2trader.notification.MarketPriceSubscriptionItem
import com.b2broker.b2core.model.b2trader.notification.SubscriptionCondition
import com.b2broker.b2core.network.b2trader.rest.notification.model.MarketPriceSubscriptionDto
import com.b2broker.b2core.network.b2trader.rest.notification.model.MarketPriceSubscriptionItemDto
import com.google.common.truth.Truth.assertThat
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class MarketPriceSubscriptionMapperTest {

    private val marketPriceSubscriptionItemMapper = mockk<MarketPriceSubscriptionItemMapper>()
    private lateinit var mapper: MarketPriceSubscriptionMapper

    @BeforeEach
    fun setUp() {
        mapper = MarketPriceSubscriptionMapper(marketPriceSubscriptionItemMapper)
    }

    @Test
    fun `GIVEN MarketPriceSubscriptionDto WHEN map THEN return correct MarketPriceSubscription`() {
        // GIVEN
        val subscriptionItemDto1 = MarketPriceSubscriptionItemDto.fixture(
            id = "sub_1",
            price = "10000.0",
            condition = "Gte",
        )
        val subscriptionItemDto2 = MarketPriceSubscriptionItemDto.fixture(
            id = "sub_2",
            price = "9000.0",
            condition = "Lte",
        )
        val dto = MarketPriceSubscriptionDto.fixture(
            marketId = "btc_usdt",
            subscriptions = listOf(subscriptionItemDto1, subscriptionItemDto2),
        )

        val mappedItem1 = MarketPriceSubscriptionItem(
            id = "sub_1",
            price = "10000.0".toBigDecimal(),
            condition = SubscriptionCondition.GREATER_THAN_OR_EQUAL,
        )
        val mappedItem2 = MarketPriceSubscriptionItem(
            id = "sub_2",
            price = "9000.0".toBigDecimal(),
            condition = SubscriptionCondition.LESS_THAN_OR_EQUAL,
        )

        every { marketPriceSubscriptionItemMapper.map(subscriptionItemDto1) } returns mappedItem1
        every { marketPriceSubscriptionItemMapper.map(subscriptionItemDto2) } returns mappedItem2

        // WHEN
        val result = mapper.map(dto)

        // THEN
        assertThat(result).isEqualTo(
            MarketPriceSubscription(
                marketId = "btc_usdt",
                subscriptions = listOf(mappedItem1, mappedItem2),
            ),
        )
    }

    @Test
    fun `GIVEN MarketPriceSubscriptionDto with empty subscriptions WHEN map THEN return subscription with empty list`() {
        // GIVEN
        val dto = MarketPriceSubscriptionDto.fixture(
            marketId = "eth_usdt",
            subscriptions = emptyList(),
        )

        // WHEN
        val result = mapper.map(dto)

        // THEN
        assertThat(result).isEqualTo(
            MarketPriceSubscription(
                marketId = "eth_usdt",
                subscriptions = emptyList(),
            ),
        )
    }
}
