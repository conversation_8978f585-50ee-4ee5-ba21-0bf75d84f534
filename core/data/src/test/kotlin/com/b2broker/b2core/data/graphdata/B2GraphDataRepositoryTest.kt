package com.b2broker.b2core.data.graphdata

import arrow.core.left
import arrow.core.right
import arrow.retrofit.adapter.either.networkhandling.UnexpectedCallError
import com.b2broker.b2core.data.graphdata.mapper.GraphDataMapper
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.network.GenericV1Response
import com.b2broker.b2core.network.di.NETWORK_JSON
import com.b2broker.b2core.network.error.DefaultErrorMapper
import com.b2broker.b2core.network.graphdata.GraphDataApi
import com.b2broker.b2core.network.graphdata.model.GraphDataResponse
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

internal class B2GraphDataRepositoryTest {

    private val api = mockk<GraphDataApi>()
    private val mapper = GraphDataMapper()
    private val repository = B2GraphDataRepository(api, mapper, DefaultErrorMapper(NETWORK_JSON))

    @Test
    fun `GIVEN start date, api error WHEN getGraphData THEN return error`() = runTest {
        // GIVEN
        val start = 1000L

        coEvery { api.getGraphData(start) } returns UnexpectedCallError(Exception()).left()

        // WHEN
        val actual = repository.getGraphData(start).leftOrNull()

        // THEN
        assertThat(actual).isInstanceOf(DefaultError::class.java)
    }

    @Test
    fun `GIVEN start date, api data WHEN getGraphData THEN return error`() = runTest {
        // GIVEN
        val start = 1000L

        val response = GraphDataResponse.fixture()
        val expected = mapper.map(response)
        coEvery { api.getGraphData(start) } returns GenericV1Response.Data(data = response).right()

        // WHEN
        val actual = repository.getGraphData(start).getOrNull()

        // THEN
        assertThat(actual).isEqualTo(expected)
    }
}
