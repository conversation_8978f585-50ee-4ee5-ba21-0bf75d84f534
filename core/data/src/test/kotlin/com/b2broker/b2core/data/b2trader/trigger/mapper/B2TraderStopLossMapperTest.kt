package com.b2broker.b2core.data.b2trader.trigger.mapper

import com.b2broker.b2core.model.b2trader.common.B2TraderStopLoss
import com.b2broker.b2core.network.b2trader.rest.trigger.model.B2TraderStopLossDto
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.Test

class B2TraderStopLossMapperTest {
    private val mapper = B2TraderStopLossMapper()

    @Test
    fun `GIVEN B2TraderStopLossDto WHEN map THEN return correct B2TraderStopLoss`() {
        // GIVEN
        val dto = B2TraderStopLossDto.fixture()

        // WHEN
        val result = mapper.map(dto)

        // THEN
        val expected = B2TraderStopLoss.fixture()
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `GIVEN B2TraderStopLossDto with invalid price WHEN map THEN return null`() {
        // GIVEN
        val dto = B2TraderStopLossDto.fixture(price = "invalid")

        // WHEN
        val result = mapper.map(dto)

        // THEN
        assertThat(result).isNull()
    }
}
