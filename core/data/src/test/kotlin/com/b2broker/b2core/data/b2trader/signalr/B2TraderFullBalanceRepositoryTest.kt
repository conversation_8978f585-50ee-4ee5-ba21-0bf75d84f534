package com.b2broker.b2core.data.b2trader.signalr

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.b2trader.assets.mapper.FullBalancesResponseToBalancesMapper
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError
import com.b2broker.b2core.model.b2trader.assets.AssetBalance
import com.b2broker.b2core.model.b2trader.assets.AssetId
import com.b2broker.b2core.network.b2trader.ws.hub.FrontOfficeV4AccountDataHub
import com.b2broker.b2core.network.b2trader.ws.model.FullBalancesResponse
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class B2TraderFullBalanceRepositoryTest {

    private lateinit var b2TraderFullBalanceRepository: B2TraderFullBalanceRepository
    private val frontOfficeV4Hub = mockk<FrontOfficeV4AccountDataHub>()

    @BeforeEach
    fun setUp() {
        b2TraderFullBalanceRepository = B2TraderFullBalanceRepository(
            fullBalancesResponseToBalancesMapper = FullBalancesResponseToBalancesMapper(
                defaultDispatcher = UnconfinedTestDispatcher(),
            ),
            frontOfficeV4Hub = frontOfficeV4Hub,
        )
    }

    @Test
    fun `GIVEN response WHEN observeFullBalanceV4 THEN returns correct data`() = runTest {
        // GIVEN
        val fullBalancesResponse = FullBalancesResponse.fixture()
        val expectedBalance = mapOf(
            AssetId(fullBalancesResponse.assetId) to AssetBalance(
                total = fullBalancesResponse.total.toBigDecimal(),
                onHold = fullBalancesResponse.locked.toBigDecimal(),
                free = fullBalancesResponse.available.toBigDecimal(),
            ),
        )
        coEvery { frontOfficeV4Hub.observeFullBalance(ACCOUNT_ID) } returns flowOf(arrayOf(fullBalancesResponse).right())

        // WHEN
        val balanceFlow = b2TraderFullBalanceRepository.observeFullBalanceV4(ACCOUNT_ID)
        val balance = balanceFlow.first().getOrNull()

        // THEN
        assertEquals(1, balance?.size)
        assertEquals(expectedBalance, balance)
    }

    @Test
    fun `GIVEN response and update WHEN observeFullBalanceV4 THEN returns correct data`() = runTest {
        // GIVEN
        val fullBalancesResponse = FullBalancesResponse.fixture(assetId = "1")
        val balanceUpdate = FullBalancesResponse.fixture(assetId = "2")
        coEvery { frontOfficeV4Hub.observeFullBalance(ACCOUNT_ID) } returns flowOf(arrayOf(fullBalancesResponse).right(), arrayOf(balanceUpdate).right())

        // WHEN
        val balanceFlow = b2TraderFullBalanceRepository.observeFullBalanceV4(ACCOUNT_ID)
        val balance = balanceFlow.last().getOrNull()

        // THEN
        val expectedBalance = mapOf(
            AssetId(fullBalancesResponse.assetId) to AssetBalance(
                total = fullBalancesResponse.total.toBigDecimal(),
                onHold = fullBalancesResponse.locked.toBigDecimal(),
                free = fullBalancesResponse.available.toBigDecimal(),
            ),
            AssetId(balanceUpdate.assetId) to AssetBalance(
                total = balanceUpdate.total.toBigDecimal(),
                onHold = balanceUpdate.locked.toBigDecimal(),
                free = balanceUpdate.available.toBigDecimal(),
            ),
        )
        assertEquals(2, balance?.size)
        assertEquals(expectedBalance, balance)
    }

    @Test
    fun `GIVEN error WHEN observeFullBalanceV4 fails THEN returns error`() = runTest {
        // GIVEN
        val expectedError = B2TraderWebSocketError.UnknownError("Test error message")
        coEvery { frontOfficeV4Hub.observeFullBalance(ACCOUNT_ID) } returns flowOf(expectedError.left())

        // WHEN
        val balanceFlow = b2TraderFullBalanceRepository.observeFullBalanceV4(ACCOUNT_ID)
        val actualError = balanceFlow.first().leftOrNull()

        // THEN
        assertEquals(actualError, actualError)
    }

    companion object {
        private const val ACCOUNT_ID = "testAccountId"
    }
}
