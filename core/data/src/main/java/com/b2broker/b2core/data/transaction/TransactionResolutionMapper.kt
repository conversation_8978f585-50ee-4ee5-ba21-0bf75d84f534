package com.b2broker.b2core.data.transaction

import com.b2broker.b2core.common.mapper.Mapper
import com.b2broker.b2core.model.transaction.TransactionResolution
import com.b2broker.b2core.network.transaction.model.TransactionResolutionDto
import javax.inject.Inject

internal class TransactionResolutionMapper @Inject constructor() : Mapper<TransactionResolutionDto, TransactionResolution> {

    override fun map(from: TransactionResolutionDto): TransactionResolution {
        return with(from) {
            TransactionResolution(
                id = id,
                name = name,
            )
        }
    }
}
