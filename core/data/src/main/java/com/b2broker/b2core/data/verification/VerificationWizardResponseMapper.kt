package com.b2broker.b2core.data.verification

import arrow.core.Either
import arrow.core.left
import arrow.core.raise.either
import arrow.core.right
import com.b2broker.b2core.common.mapper.AsyncMapper
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.logger.Logger
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.UnknownError
import com.b2broker.b2core.model.verification.SumSubInitData
import com.b2broker.b2core.model.verification.VerificationWizardStep
import com.b2broker.b2core.network.di.NetworkJson
import com.b2broker.b2core.network.verification.model.StartWorkflow
import com.b2broker.b2core.network.verification.model.SumSubInfoDto
import com.b2broker.b2core.network.verification.model.VerificationStartWizardResponseDto
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import javax.inject.Inject

internal class VerificationWizardResponseMapper @Inject constructor(
    @NetworkJson private val json: Json,
    @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
) : AsyncMapper<VerificationStartWizardResponseDto, Either<DefaultError, VerificationWizardStep>>(defaultDispatcher) {

    override suspend fun internalMap(from: VerificationStartWizardResponseDto): Either<DefaultError, VerificationWizardStep> = either {
        return when (from.workflow) {
            StartWorkflow.SUM_SUB.value -> parseVerificationData(from.data).map { VerificationWizardStep.SumSub(it) }
            StartWorkflow.TERMINATE.value -> VerificationWizardStep.Terminate.right()
            else -> VerificationWizardStep.Unknown.right()
        }
    }

    private fun parseVerificationData(
        data: JsonElement?
    ): Either<DefaultError, SumSubInitData> {
        if (data == null) return UnknownError().left()
        if (data !is JsonObject) return UnknownError().left()
        return try {
            val dto = json.decodeFromJsonElement(SumSubInfoDto.serializer(), data)
            dto.toVerificationInitData().right()
        } catch (e: Exception) {
            Logger.e(e, "Couldn't parse VerificationData")
            UnknownError().left()
        }
    }

    private fun SumSubInfoDto.toVerificationInitData(): SumSubInitData {
        return SumSubInitData(
            accessToken = this.accessToken,
            email = this.email,
        )
    }
}
