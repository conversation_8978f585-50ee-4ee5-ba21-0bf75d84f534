package com.b2broker.b2core.data.b2trader.margin.settings

import arrow.core.Either
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.margin.B2TraderAccountMarginSettingsAssets

interface B2TraderMarginSettingsRepository {

    suspend fun getAccountMarginSettingsAssets(accountId: String): Either<DefaultError, B2TraderAccountMarginSettingsAssets>

    suspend fun enableAssetAsMarginCollateral(accountId: String, assetId: String): Either<DefaultError, Unit>

    suspend fun disableAssetAsMarginCollateral(accountId: String, assetId: String): Either<DefaultError, Unit>
}
