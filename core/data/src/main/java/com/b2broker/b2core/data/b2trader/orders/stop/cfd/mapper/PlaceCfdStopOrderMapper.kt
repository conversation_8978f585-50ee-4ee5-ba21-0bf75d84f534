package com.b2broker.b2core.data.b2trader.orders.stop.cfd.mapper

import com.b2broker.b2core.data.b2trader.trigger.mapper.B2TraderStopLossDtoMapper
import com.b2broker.b2core.data.b2trader.trigger.mapper.B2TraderTakeProfitDtoMapper
import com.b2broker.b2core.model.b2trader.orders.stop.StopOrderStatus.Companion.fromStringOrNull
import com.b2broker.b2core.model.b2trader.orders.stop.cfd.CfdStopOrder
import com.b2broker.b2core.model.b2trader.orders.stop.cfd.PlaceCfdStopOrder
import com.b2broker.b2core.network.b2trader.rest.orders.v4.model.stop.PlaceCfdStopOrderDto
import com.b2broker.b2core.network.b2trader.rest.orders.v4.model.stop.PlaceCfdStopOrderRequest
import com.b2broker.b2core.network.b2trader.rest.orders.v4.model.stop.PlaceCfdStopOrderResponse
import javax.inject.Inject

internal class PlaceCfdStopOrderMapper @Inject constructor(
    private val b2TraderTakeProfitDtoMapper: B2TraderTakeProfitDtoMapper,
    private val b2TraderStopLossDtoMapper: B2TraderStopLossDtoMapper
) {
    fun mapToRequest(from: PlaceCfdStopOrder): PlaceCfdStopOrderRequest {
        return PlaceCfdStopOrderRequest(
            order = PlaceCfdStopOrderDto(
                marketId = from.marketId,
                orderType = from.orderType.name,
                side = from.side.name,
                requestedPrice = from.requestedPrice?.toString(),
                activationPrice = from.activationPrice.toString(),
                requestedLotAmount = from.requestedLotAmount.toString(),
                timeInForce = from.timeInForce.name,
                cancellationDate = from.cancellationDate,
                leverage = from.leverage,
                takeProfit = from.takeProfit?.let(b2TraderTakeProfitDtoMapper::map),
                stopLoss = from.stopLoss?.let(b2TraderStopLossDtoMapper::map),
            ),
        )
    }

    fun mapFromResponse(from: PlaceCfdStopOrderResponse): CfdStopOrder? {
        val status = fromStringOrNull(from.order.status) ?: return null
        return CfdStopOrder(
            orderId = from.order.orderId,
            status = status,
        )
    }
}
