package com.b2broker.b2core.data.di

import com.b2broker.b2core.coroutines.di.IoDispatcher
import com.b2broker.b2core.data.country.B2CountriesRepository
import com.b2broker.b2core.data.country.CountriesRepository
import com.b2broker.b2core.data.country.CountryOptionMapper
import com.b2broker.b2core.data.country.ListCountryDtoMapper
import com.b2broker.b2core.data.fields.B2FieldOptionsRepository
import com.b2broker.b2core.data.fields.B2FileInfoRepository
import com.b2broker.b2core.data.fields.FieldOptionsRepository
import com.b2broker.b2core.data.fields.FileInfoRepository
import com.b2broker.b2core.data.utils.ContentResolverWrapper
import com.b2broker.b2core.network.countries.CountriesApi
import com.b2broker.b2core.network.error.DefaultErrorMapper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher

@Module
@InstallIn(SingletonComponent::class)
internal object FieldsModule {

    @Provides
    fun provideFileInfoRepository(
        contentResolverWrapper: ContentResolverWrapper,
        @IoDispatcher coroutineDispatcher: CoroutineDispatcher,
    ): FileInfoRepository {
        return B2FileInfoRepository(
            contentResolverWrapper = contentResolverWrapper,
            coroutineDispatcher = coroutineDispatcher,
        )
    }

    @Provides
    fun provideCountriesRepository(
        countriesApi: CountriesApi,
        defaultErrorMapper: DefaultErrorMapper,
        listCountryDtoMapper: ListCountryDtoMapper,
    ): CountriesRepository {
        return B2CountriesRepository(
            countriesApi = countriesApi,
            defaultErrorMapper = defaultErrorMapper,
            listCountryDtoMapper = listCountryDtoMapper,
        )
    }

    @Provides
    fun provideFieldOptionsRepository(
        countriesRepository: CountriesRepository,
        countryOptionMapper: CountryOptionMapper,
    ): FieldOptionsRepository {
        return B2FieldOptionsRepository(
            countriesRepository = countriesRepository,
            countryOptionMapper = countryOptionMapper,
        )
    }
}
