package com.b2broker.b2core.data.accounts.mapper

import com.b2broker.b2core.model.accounts.CreateAccountResponse
import com.b2broker.b2core.network.account.model.AccountDto
import com.b2broker.b2core.network.di.NetworkJson
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import java.net.HttpURLConnection.HTTP_ACCEPTED
import javax.inject.Inject

internal class AccountCreationResponseMapper @Inject constructor(
    private val accountOverviewMapper: AccountOverviewMapper,
    @NetworkJson private val json: Json,
) {

    fun map(code: Int, from: JsonObject): CreateAccountResponse {
        if (code == HTTP_ACCEPTED) {
            return CreateAccountResponse.RequestCreated
        }
        val accountDto = json.decodeFromJsonElement(AccountDto.serializer(), from)
        return accountOverviewMapper.map(accountDto)
    }
}
