package com.b2broker.b2core.presentation

import androidx.lifecycle.ViewModel
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.event.dispatcher.QueueEventDispatcher
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.b2broker.b2core.presentation.state.UiStateHolder
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.atomic.AtomicReference

class ViewModelKtTest {

    private data class ProgressUiState(
        val isInProgress: Boolean? = null,
    ) : HasProgress<ProgressUiState> {
        override fun updateProgress(isInProgress: Boolean) =
            copy(isInProgress = isInProgress)
    }

    private class TestViewModel(
        uiStateHolder: UiStateHolder<ProgressUiState>,
        eventDispatcher: EventDispatcher,
    ) : ViewModel(),
        UiStateHolder<ProgressUiState> by uiStateHolder,
        EventDispatcher by eventDispatcher

    private lateinit var viewModel: TestViewModel

    @BeforeEach
    fun setUp() {
        val uiStateHolder = DefaultUiStateHolder(ProgressUiState())
        val eventDispatcher = QueueEventDispatcher()
        viewModel = TestViewModel(uiStateHolder, eventDispatcher)
    }

    @Test
    fun `WHEN launch THEN progress shown and hidden`() = runTest {
        val testDispatcher = UnconfinedTestDispatcher(testScheduler)
        Dispatchers.setMain(testDispatcher)

        // WHEN
        val progressStates = mutableListOf<Boolean?>()
        progressStates += viewModel.uiState.isInProgress

        viewModel.launch {
            progressStates += viewModel.uiState.isInProgress
            delay(1000)
        }

        advanceUntilIdle()
        progressStates += viewModel.uiState.isInProgress

        // THEN
        assertThat(progressStates).containsExactly(null, true, false)
    }

    @Test
    fun `WHEN launch THEN exceptions are mapped as events`() = runTest {
        val testDispatcher = UnconfinedTestDispatcher(testScheduler)
        Dispatchers.setMain(testDispatcher)

        val actualEvents = mutableListOf<Event>()
        val job = launch {
            viewModel.events.collect {
                actualEvents.add(it)
            }
        }

        val actualThrowableRef = AtomicReference<Throwable>()
        val exceptionHandler = CoroutineExceptionHandler { _, t -> actualThrowableRef.set(t) }

        // WHEN
        viewModel.launch(exceptionHandler) {
            throw IllegalStateException("Test exception")
        }
        advanceUntilIdle()

        // THEN
        assertThat(actualThrowableRef.get()).isInstanceOf(IllegalStateException::class.java)
        job.cancel()
    }
}
