package com.b2broker.b2core.designsystem.appspecific.fields

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.b2broker.b2core.model.fields.DateError
import com.b2broker.b2core.model.fields.InputError
import com.b2broker.b2core.text.CoreStrings

@Composable
internal fun InputError.toText(): String = when (this) {
    is InputError.OnlyDigitAllowed -> stringResource(id = CoreStrings.dynamic_field_numbers)
    is InputError.MinSize -> stringResource(id = CoreStrings.dynamic_field_min_length, this.value)
    is InputError.Required -> stringResource(id = CoreStrings.dynamic_field_is_required)
    is InputError.MaxSize -> stringResource(id = CoreStrings.dynamic_field_max_length, this.value)
    is InputError.MaxValue -> stringResource(id = CoreStrings.dynamic_field_max_value, this.value)
    is InputError.MinValue -> stringResource(id = CoreStrings.dynamic_field_min_value, this.value)
    is InputError.ShouldBeSame -> stringResource(id = CoreStrings.dynamic_field_same_should_match)
    is InputError.Validation -> this.value
    is InputError.PhoneNumberInvalid -> stringResource(id = CoreStrings.dynamic_field_phone_number_invalid)
    is InputError.AmountLessThanFee -> stringResource(id = CoreStrings.dynamic_field_amount_less_than_fee)
}

@Composable
internal fun DateError.toText(): String = when (this) {
    is DateError.AgeUnder -> stringResource(id = CoreStrings.dynamic_field_age_under_error_format, years)
}
