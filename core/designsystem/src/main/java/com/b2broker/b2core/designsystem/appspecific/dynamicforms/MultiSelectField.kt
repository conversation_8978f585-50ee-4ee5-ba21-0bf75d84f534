package com.b2broker.b2core.designsystem.appspecific.dynamicforms

import androidx.compose.runtime.Composable
import com.b2broker.b2core.designsystem.appspecific.fields.B2MultiSelector
import com.b2broker.b2core.model.dynamicforms.DynamicFormsEvent
import com.b2broker.b2core.model.dynamicforms.StatePosition
import com.b2broker.b2core.model.dynamicforms.state.FieldState

@Composable
internal fun MultiSelectField(
    field: FieldState.MultiFieldState,
    statePosition: StatePosition,
    onEvent: (DynamicFormsEvent) -> Unit,
) {
    val error = field.visibleError()

    B2MultiSelector(
        label = field.label,
        value = field.selectedText,
        count = field.selectedCount,
        onClick = {
            onEvent(DynamicFormsEvent.OpenMultiSelectDialog(statePosition))
        },
        supportingText = errorText(error),
        isError = error != null,
    )
}
