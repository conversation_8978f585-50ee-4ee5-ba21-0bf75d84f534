package com.b2broker.b2core.designsystem.appspecific.dynamicforms

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import com.b2broker.b2core.designsystem.component.textfield.B2PhoneField
import com.b2broker.b2core.model.dynamicforms.DynamicFormsEvent
import com.b2broker.b2core.model.dynamicforms.StatePosition
import com.b2broker.b2core.model.dynamicforms.state.FieldState

@Composable
internal fun PhoneField(
    field: FieldState.PhoneFieldState,
    statePosition: StatePosition,
    onEvent: (DynamicFormsEvent) -> Unit,
) {
    var receivedFocus by rememberSaveable { mutableStateOf(false) }

    B2PhoneField(
        countryData = field.countryData,
        numberWithoutCode = field.phoneWithoutCode,
        onSelectCountryPhoneCodeClick = {
            onEvent(DynamicFormsEvent.CountryPhoneChange(statePosition))
        },
        onPhoneChange = { phoneWithoutCode ->
            onEvent(DynamicFormsEvent.PhoneFieldChanged(phoneWithoutCode, statePosition))
        },
        label = field.label,
        error = errorText(field.visibleError()),
        onFocusChanged = { focusState ->
            if (focusState.hasFocus) {
                receivedFocus = true
            } else {
                if (receivedFocus) {
                    onEvent(DynamicFormsEvent.ShowErrorChanged(statePosition))
                }
            }
        }
    )
}
