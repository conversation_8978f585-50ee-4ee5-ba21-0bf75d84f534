package com.b2broker.b2core.designsystem.component.card

import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme

@Composable
public fun B2FilledCard(
    modifier: Modifier = Modifier,
    containerColor: Color = B2Theme.colors.surfaceContainer,
    shape: Shape = B2Theme.shapes.medium,
    contentPaddings: PaddingValues = PaddingValues(16.dp),
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit,
) {
    val interactionSource = remember { MutableInteractionSource() }

    val clickableModifier = if (onClick != null) {
        Modifier.combinedClickable(
            interactionSource = interactionSource,
            indication = ripple(bounded = true),
            onClick = onClick,
        )
    } else {
        Modifier
    }

    Card(
        modifier = modifier
            .clip(shape)
            .then(clickableModifier),
        colors = CardDefaults.cardColors(containerColor = containerColor),
        shape = shape,
        content = {
            Column(
                modifier = Modifier.padding(contentPaddings),
                content = content,
            )
        },
    )
}

@PreviewLightDark
@Composable
private fun B2FilledCardPreview() = PreviewContainer(
    modifier = Modifier.padding(16.dp),
) {
    B2FilledCard {
        Text("Card Preview")
    }
}
