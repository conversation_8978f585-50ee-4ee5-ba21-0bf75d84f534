<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="116dp"
    android:height="30dp"
    android:viewportWidth="116"
    android:viewportHeight="30">
  <path
      android:pathData="M1.12,1.18C0.83,1.48 0.65,1.96 0.65,2.58V24.7C0.65,25.32 0.83,25.79 1.12,26.1L1.19,26.17L13.58,13.78V13.49L1.19,1.1L1.12,1.18Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="105.6"
          android:startY="2.19"
          android:endX="100.81"
          android:endY="39.66"
          android:type="linear">
        <item android:offset="0" android:color="#FF00A0FF"/>
        <item android:offset="0.01" android:color="#FF00A1FF"/>
        <item android:offset="0.26" android:color="#FF00BEFF"/>
        <item android:offset="0.51" android:color="#FF00D2FF"/>
        <item android:offset="0.76" android:color="#FF00DFFF"/>
        <item android:offset="1" android:color="#FF00E3FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.71,17.92L13.58,13.79V13.64V13.49L17.71,9.36L17.8,9.42L22.7,12.2C24.09,12.99 24.09,14.29 22.7,15.08L17.8,17.86L17.71,17.92Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="124.04"
          android:startY="15"
          android:endX="-148.99"
          android:endY="15"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFE000"/>
        <item android:offset="0.41" android:color="#FFFFBD00"/>
        <item android:offset="0.78" android:color="#FFFFA500"/>
        <item android:offset="1" android:color="#FFFF9C00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.8,17.86L13.58,13.64L1.12,26.1C1.58,26.59 2.34,26.65 3.19,26.16L17.8,17.86Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="99.57"
          android:startY="5.85"
          android:endX="70.22"
          android:endY="97.08"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF3A44"/>
        <item android:offset="1" android:color="#FFC31162"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.8,9.41L3.2,1.11C2.34,0.63 1.58,0.69 1.12,1.18L13.58,13.64L17.8,9.41Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="-20.92"
          android:startY="-14.63"
          android:endX="-7.81"
          android:endY="26.11"
          android:type="linear">
        <item android:offset="0" android:color="#FF32A071"/>
        <item android:offset="0.07" android:color="#FF2DA771"/>
        <item android:offset="0.48" android:color="#FF15CF74"/>
        <item android:offset="0.8" android:color="#FF06E775"/>
        <item android:offset="1" android:color="#FF00F076"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M62.83,7.28C62.24,7.89 61.5,8.19 60.63,8.19C59.75,8.19 59.02,7.89 58.43,7.28C57.84,6.68 57.54,5.94 57.54,5.06C57.54,4.18 57.84,3.44 58.43,2.83C59.02,2.23 59.75,1.92 60.63,1.92C61.5,1.92 62.23,2.23 62.82,2.84C63.41,3.45 63.71,4.19 63.71,5.06C63.71,5.94 63.42,6.68 62.83,7.28ZM59,6.78C59.44,7.23 59.98,7.46 60.63,7.46C61.27,7.46 61.81,7.23 62.26,6.78C62.7,6.33 62.92,5.76 62.92,5.06C62.92,4.36 62.7,3.79 62.26,3.34C61.81,2.89 61.27,2.66 60.63,2.66C59.98,2.66 59.44,2.89 59,3.34C58.55,3.79 58.33,4.36 58.33,5.06C58.33,5.76 58.55,6.33 59,6.78Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M36.64,5.3C36.64,6.14 36.39,6.81 35.89,7.31C35.33,7.9 34.59,8.19 33.69,8.19C32.82,8.19 32.08,7.89 31.48,7.29C30.87,6.69 30.57,5.95 30.57,5.06C30.57,4.17 30.87,3.43 31.48,2.83C32.08,2.23 32.82,1.93 33.69,1.93C34.12,1.93 34.53,2.01 34.92,2.18C35.31,2.35 35.62,2.57 35.86,2.85L35.33,3.38C34.93,2.9 34.38,2.66 33.69,2.66C33.05,2.66 32.51,2.89 32.05,3.33C31.59,3.77 31.36,4.35 31.36,5.06C31.36,5.77 31.59,6.35 32.05,6.79C32.51,7.24 33.05,7.46 33.69,7.46C34.36,7.46 34.92,7.23 35.36,6.79C35.65,6.5 35.82,6.09 35.87,5.57H33.69V4.85H36.59C36.62,5.01 36.64,5.16 36.64,5.3Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M41.25,2.8H38.51V4.7H40.98V5.42H38.51V7.32H41.25V8.06H37.74V2.06H41.25V2.8Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M43.72,8.06H44.5V2.8H46.17V2.06H42.05V2.8H43.72V8.06Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M49.16,8.06V2.06H49.93V8.06H49.16Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M52.57,8.06H53.34V2.8H55.02V2.06H50.9V2.8H52.57V8.06Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M64.79,2.06V8.06H65.56V4.32L65.53,3.17H65.56L68.61,8.06H69.42V2.06H68.65V5.57L68.68,6.73H68.65L65.73,2.06H64.79Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M57.66,16.9C55.31,16.9 53.39,18.69 53.39,21.15C53.39,23.6 55.31,25.41 57.66,25.41C60.02,25.41 61.93,23.6 61.93,21.15C61.93,18.69 60.02,16.9 57.66,16.9ZM57.66,23.73C56.37,23.73 55.26,22.67 55.26,21.15C55.26,19.62 56.37,18.57 57.66,18.57C58.95,18.57 60.06,19.62 60.06,21.15C60.06,22.67 58.95,23.73 57.66,23.73ZM48.35,16.9C46,16.9 44.08,18.69 44.08,21.15C44.08,23.6 46,25.41 48.35,25.41C50.7,25.41 52.62,23.6 52.62,21.15C52.62,18.69 50.7,16.9 48.35,16.9ZM48.35,23.73C47.06,23.73 45.95,22.67 45.95,21.15C45.95,19.62 47.06,18.57 48.35,18.57C49.64,18.57 50.75,19.62 50.75,21.15C50.75,22.67 49.64,23.73 48.35,23.73ZM37.27,20.01H41.59C41.46,21.02 41.12,21.76 40.61,22.28C39.98,22.91 39,23.6 37.27,23.6C34.61,23.6 32.54,21.46 32.54,18.8C32.54,16.14 34.61,14 37.27,14C38.71,14 39.75,14.56 40.53,15.29L41.8,14.02C40.72,12.98 39.29,12.19 37.27,12.19C33.63,12.19 30.57,15.16 30.57,18.8C30.57,22.44 33.63,25.4 37.27,25.4C39.24,25.4 40.72,24.76 41.88,23.55C43.07,22.36 43.44,20.68 43.44,19.33C43.44,18.91 43.41,18.52 43.35,18.2H37.27V20.01ZM82.58,19.61C82.23,18.65 81.15,16.9 78.94,16.9C76.75,16.9 74.93,18.62 74.93,21.15C74.93,23.54 76.73,25.4 79.15,25.4C81.1,25.4 82.23,24.21 82.69,23.52L81.24,22.55C80.76,23.26 80.1,23.73 79.15,23.73C78.2,23.73 77.52,23.29 77.09,22.44L82.77,20.09L82.58,19.61ZM76.78,21.02C76.73,19.38 78.05,18.54 79,18.54C79.74,18.54 80.37,18.91 80.58,19.44L76.78,21.02ZM72.16,25.15H74.02V12.65H72.16V25.15ZM69.09,17.85H69.03C68.61,17.35 67.81,16.9 66.79,16.9C64.66,16.9 62.72,18.77 62.72,21.17C62.72,23.55 64.66,25.4 66.79,25.4C67.81,25.4 68.61,24.95 69.03,24.44H69.09V25.05C69.09,26.68 68.22,27.55 66.82,27.55C65.68,27.55 64.97,26.72 64.68,26.03L63.05,26.71C63.52,27.84 64.76,29.22 66.82,29.22C69.01,29.22 70.87,27.93 70.87,24.79V17.16H69.09V17.85ZM66.95,23.73C65.66,23.73 64.58,22.65 64.58,21.17C64.58,19.67 65.66,18.57 66.95,18.57C68.23,18.57 69.22,19.67 69.22,21.17C69.22,22.65 68.23,23.73 66.95,23.73ZM91.33,12.65H86.86V25.15H88.73V20.41H91.33C93.4,20.41 95.43,18.91 95.43,16.53C95.43,14.14 93.4,12.65 91.33,12.65ZM91.38,18.67H88.73V14.39H91.38C92.78,14.39 93.57,15.54 93.57,16.53C93.57,17.5 92.78,18.67 91.38,18.67ZM102.91,16.88C101.56,16.88 100.16,17.47 99.58,18.79L101.24,19.48C101.6,18.79 102.25,18.57 102.95,18.57C103.91,18.57 104.89,19.14 104.91,20.17V20.3C104.57,20.11 103.85,19.82 102.96,19.82C101.18,19.82 99.36,20.8 99.36,22.63C99.36,24.31 100.82,25.38 102.46,25.38C103.72,25.38 104.41,24.82 104.84,24.16H104.91V25.13H106.71V20.33C106.71,18.11 105.05,16.88 102.91,16.88ZM102.69,23.73C102.08,23.73 101.22,23.42 101.22,22.67C101.22,21.7 102.29,21.33 103.2,21.33C104.02,21.33 104.41,21.51 104.91,21.75C104.76,22.91 103.76,23.73 102.69,23.73ZM113.27,17.15L111.13,22.57H111.07L108.85,17.15H106.84L110.17,24.72L108.27,28.94H110.21L115.35,17.15H113.27ZM96.46,25.15H98.33V12.65H96.46V25.15Z"
      android:fillColor="#ffffff"/>
</vector>
