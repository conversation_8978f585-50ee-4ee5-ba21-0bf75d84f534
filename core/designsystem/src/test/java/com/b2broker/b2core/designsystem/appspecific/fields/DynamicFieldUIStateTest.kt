package com.b2broker.b2core.designsystem.appspecific.fields

import com.b2broker.b2core.model.fields.InputError
import com.b2broker.b2core.model.fields.InputField
import com.b2broker.b2core.model.fields.InputField.Companion.AMOUNT_FIELD_NAME
import com.b2broker.b2core.model.fields.InputFieldValue
import com.b2broker.b2core.model.funds.FieldErrorItem
import com.google.common.truth.Truth.assertThat
import kotlinx.collections.immutable.persistentListOf
import org.junit.jupiter.api.Test

internal class DynamicFieldUIStateTest {

    @Test
    fun `GIVEN empty errors WHEN addValidateError is called THEN return original list`() {
        // GIVEN
        val originalList = persistentListOf(
            DynamicFieldUIState(
                field = InputField.fixture(name = "field1"),
                value = InputFieldValue("field1"),
                subFields = persistentListOf(),
            ),
            DynamicFieldUIState(
                field = InputField.fixture(name = "field2"),
                value = InputFieldValue("field2"),
                subFields = persistentListOf(),
            ),
        )
        val errors = emptyList<FieldErrorItem>()

        // WHEN
        val result = originalList.addValidateError(errors)

        // THEN
        assertThat(result).isEqualTo(originalList)
    }

    @Test
    fun `GIVEN errors with matching field names WHEN addValidateError is called THEN return updated list with errors`() {
        // GIVEN
        val originalList = persistentListOf(
            DynamicFieldUIState(
                field = InputField.fixture(name = "field1"),
                value = InputFieldValue("field1"),
                subFields = persistentListOf(),
            ),
            DynamicFieldUIState(
                field = InputField.fixture(name = "field2"),
                value = InputFieldValue("field2"),
                subFields = persistentListOf(),
            ),
            DynamicFieldUIState(
                field = InputField.fixture(name = "field3"),
                value = InputFieldValue("field3"),
                subFields = persistentListOf(),
            ),
        )
        val errors = listOf(
            FieldErrorItem("field2", "Error for field2"),
            FieldErrorItem("field3", "Error for field3"),
            FieldErrorItem("field1", "Error for field1"),
        )

        // WHEN
        val result = originalList.addValidateError(errors)

        // THEN
        assertThat(result).hasSize(3)
        assertThat(result[0].error).isEqualTo(InputError.Validation("Error for field1"))
        assertThat(result[1].error).isEqualTo(InputError.Validation("Error for field2"))
        assertThat(result[2].error).isEqualTo(InputError.Validation("Error for field3"))
    }

    @Test
    fun `GIVEN errors with non-matching field names WHEN addValidateError is called THEN return list with only matching errors`() {
        // GIVEN
        val originalList = persistentListOf(
            DynamicFieldUIState(
                field = InputField.fixture(name = "field1"),
                value = InputFieldValue("field1"),
                subFields = persistentListOf(),
            ),
            DynamicFieldUIState(
                field = InputField.fixture(name = "field2"),
                value = InputFieldValue("field2"),
                subFields = persistentListOf(),
            ),
        )
        val errors = listOf(
            FieldErrorItem("field1", "error1"),
            FieldErrorItem("field3", "Error for field3"),
        )

        // WHEN
        val result = originalList.addValidateError(errors)

        // THEN
        assertThat(result).hasSize(2)
        assertThat(result.first { it.field.name == "field1" }.error).isEqualTo(InputError.Validation("error1"))
        assertThat(result.first { it.field.name == "field2" }.error).isNull()
    }

    @Test
    fun `GIVEN errors, subfields WHEN addValidateError THEN recursive list with errors`() {
        // GIVEN
        val originalList = persistentListOf(
            DynamicFieldUIState(
                field = InputField.fixture(name = "field1"),
                value = InputFieldValue("field1"),
                subFields = persistentListOf(),
            ),
            DynamicFieldUIState(
                field = InputField.fixture(name = "field2"),
                value = InputFieldValue("field2"),
                subFields = persistentListOf(
                    DynamicFieldUIState(
                        field = InputField.fixture(name = "field3"),
                        value = InputFieldValue("field3"),
                        subFields = persistentListOf(),
                    ),
                ),
            ),
        )
        val errors = listOf(
            FieldErrorItem("field1", "error1"),
            FieldErrorItem("field2.field3", "error3"),
        )

        // WHEN
        val result = originalList.addValidateError(errors)

        // THEN
        assertThat(result).hasSize(2)
        assertThat(result.first { it.field.name == "field1" }.error).isEqualTo(InputError.Validation("error1"))
        assertThat(result.first { it.field.name == "field2" }.error).isNull()
        assertThat(result.findRecursively { it.field.name == "field3" }?.error).isEqualTo(InputError.Validation("error3"))
    }

    @Test
    fun `GIVEN amount input and fee error WHEN addAmountFeeError THEN amount field error should be set`() {
        // GIVEN
        val field1 = DynamicFieldUIState(
            field = InputField.fixture(name = "field1"),
            value = InputFieldValue("field1"),
            subFields = persistentListOf(),
        )
        val amountField = DynamicFieldUIState(
            field = InputField.fixture(name = AMOUNT_FIELD_NAME),
            value = InputFieldValue("0.2"),
            subFields = persistentListOf(),
        )
        val field3 = DynamicFieldUIState(
            field = InputField.fixture(name = "field3"),
            value = InputFieldValue("field3"),
            subFields = persistentListOf(),
        )
        val fields = persistentListOf(
            field1,
            amountField,
            field3,
        )

        // WHEN
        val actual = fields.addAmountFeeError()

        // THEN
        val expected = persistentListOf(
            field1,
            amountField.copy(
                error = InputError.AmountLessThanFee,
            ),
            field3,
        )

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `GIVEN no amount input and fee error WHEN addAmountFeeError THEN amount field error should be set`() {
        // GIVEN
        val field1 = DynamicFieldUIState(
            field = InputField.fixture(name = "field1"),
            value = InputFieldValue("field1"),
            subFields = persistentListOf(),
        )
        val field3 = DynamicFieldUIState(
            field = InputField.fixture(name = "field3"),
            value = InputFieldValue("field3"),
            subFields = persistentListOf(),
        )
        val fields = persistentListOf(
            field1,
            field3,
        )

        // WHEN
        val actual = fields.addAmountFeeError()

        // THEN
        assertThat(actual).isEqualTo(fields)
    }
}
