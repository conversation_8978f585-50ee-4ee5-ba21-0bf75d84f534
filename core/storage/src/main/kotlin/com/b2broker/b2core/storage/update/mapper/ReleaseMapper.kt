package com.b2broker.b2core.storage.update.mapper

import com.b2broker.b2core.common.mapper.Mapper
import com.b2broker.b2core.model.update.Release
import com.b2broker.b2core.storage.update.model.ReleaseEntity
import kotlinx.datetime.Instant
import javax.inject.Inject

internal class ReleaseEntityToReleaseMapper
@Inject constructor() : Mapper<ReleaseEntity, Release> {

    override fun map(from: ReleaseEntity): Release {
        return Release(
            id = from.id,
            enabled = from.enabled,
            rolloutPercent = from.rolloutPercent,
            newVersionName = from.newVersionName,
            newVersionCode = from.newVersionCode,
            newVersionSizeBytes = from.newVersionSizeBytes,
            checkSum = from.checkSum,
            url = from.url,
            minSdk = from.minSdk,
            title = from.title,
            description = from.description,
            forceUpdateTitle = from.forceUpdateTitle,
            forceUpdateDescription = from.forceUpdateDescription,
            releaseNotes = from.releaseNotes,
            createdAt = Instant.fromEpochMilliseconds(from.createdAt),
        )
    }
}

internal class ReleaseToReleaseEntityMapper @Inject constructor() : Mapper<Release, ReleaseEntity> {

    override fun map(from: Release): ReleaseEntity {
        return ReleaseEntity(
            id = from.id,
            enabled = from.enabled,
            rolloutPercent = from.rolloutPercent,
            newVersionName = from.newVersionName,
            newVersionCode = from.newVersionCode,
            newVersionSizeBytes = from.newVersionSizeBytes,
            checkSum = from.checkSum,
            url = from.url,
            minSdk = from.minSdk,
            title = from.title,
            description = from.description,
            forceUpdateTitle = from.forceUpdateTitle,
            forceUpdateDescription = from.forceUpdateDescription,
            releaseNotes = from.releaseNotes,
            createdAt = from.createdAt.toEpochMilliseconds(),
        )
    }
}
