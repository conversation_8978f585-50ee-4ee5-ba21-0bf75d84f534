package com.b2broker.b2core.storage

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.model.DefaultError
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import java.util.concurrent.ConcurrentHashMap
import kotlin.time.Duration
import kotlin.time.Duration.Companion.nanoseconds

private typealias CacheMap = MutableMap<Storage.Key<*>, Value<*>>
private typealias FlowMap = MutableMap<Storage.Key<*>, MutableStateFlow<Value<*>?>>

@Suppress("UNCHECKED_CAST")
internal class InMemCacheStorage(
    private val clock: Clock,
    private val defaultTtl: Duration,
    private val dispatcher: CoroutineDispatcher,
) : CacheStorage {

    private val cache: CacheMap = ConcurrentHashMap()
    private val flows: FlowMap = ConcurrentHashMap()

    override fun <T : Any> observe(key: Storage.Key<T>): Flow<T?> {
        return getOrCreateFlow(key)
            .map { cache.getNonExpired(key).unwrap() }
    }

    override suspend fun <T : Any> get(key: Storage.Key<T>): Either<DefaultError, T?> = either {
        cache.getNonExpired(key).unwrap<T>()
    }

    override suspend fun <T : Any> set(key: Storage.Key<T>, value: T?): Either<DefaultError, Unit> = withContext(dispatcher) {
        set(key, value, defaultTtl)
    }

    override suspend fun <T : Any> set(key: Storage.Key<T>, value: T?, ttl: Duration): Either<DefaultError, Unit> = either {
        val wrappedValue = value.wrap(ttl)
        cache.putOrRemoveIfNull(key, wrappedValue)
        getOrCreateFlow(key).update { wrappedValue }
    }

    override suspend fun <T : Any> remove(key: Storage.Key<T>): Either<DefaultError, Unit> = either {
        withContext(dispatcher) { cache.remove(key) }
        flows[key]?.update { null }
    }

    override suspend fun clear(): Either<DefaultError, Unit> = either {
        withContext(dispatcher) { cache.clear() }
        flows.values.forEach { value -> value.update { null } }
    }

    private fun <T : Any> Value<T>.isExpired(): Boolean = clock.now() >= expiresAt

    private fun <T : Any> T?.wrap(ttl: Duration): Value<T>? {
        if (this == null) return null
        if (ttl <= 0.nanoseconds) return null

        return Value(
            value = this,
            expiresAt = clock.now() + ttl,
        )
    }

    private fun <T> Value<*>?.unwrap(): T? {
        if (this == null) return null
        if (isExpired()) return null

        return value as T
    }

    private fun <T : Any> getOrCreateFlow(key: Storage.Key<T>) = flows.getOrPut(key) { MutableStateFlow(null) }

    private suspend fun <T : Any> CacheMap.getNonExpired(
        key: Storage.Key<T>
    ): Value<T>? = withContext(dispatcher) {
        val wrappedValue = this@getNonExpired[key] ?: return@withContext null

        if (wrappedValue.isExpired()) {
            remove(key)
            return@withContext null
        }

        return@withContext wrappedValue as Value<T>
    }

    private suspend fun CacheMap.putOrRemoveIfNull(
        key: Storage.Key<*>,
        value: Value<*>?
    ) = withContext(dispatcher) {
        if (value != null) put(key, value) else remove(key)
    }
}

private data class Value<T : Any>(
    val value: T,
    val expiresAt: Instant
)
