package com.b2broker.b2core.storage.b2trader.notification

import androidx.annotation.VisibleForTesting
import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.notification.PriceSubscription
import com.b2broker.b2core.storage.CacheStorage
import com.b2broker.b2core.storage.Storage
import com.b2broker.b2core.storage.di.B2TraderScoped
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

internal class CachedB2TraderNotificationStorage
@Inject constructor(
    @B2TraderScoped private val cache: CacheStorage,
) : B2TraderNotificationStorage {

    override fun observePriceSubscriptions(
        accountId: String
    ): Flow<Either<DefaultError, LinkedHashMap<String, PriceSubscription>>> {
        return cache.observe(PriceSubscriptionsKey(accountId))
            .map { response ->
                Either.Right(
                    response?.filterValues { subscription ->
                        !subscription.deleted
                    } as? LinkedHashMap<String, PriceSubscription> ?: linkedMapOf(),
                )
            }
    }

    override suspend fun getPriceSubscriptions(
        accountId: String
    ): Either<DefaultError, LinkedHashMap<String, PriceSubscription>> = either {
        cache.get(PriceSubscriptionsKey(accountId)).bind() as? LinkedHashMap<String, PriceSubscription> ?: linkedMapOf()
    }

    override suspend fun setPriceSubscriptions(
        accountId: String,
        subscriptions: LinkedHashMap<String, PriceSubscription>
    ): Either<DefaultError, LinkedHashMap<String, PriceSubscription>> = either {
        val existingSubscriptions = getPriceSubscriptions(accountId).bind()
        val mergedSubscriptions = LinkedHashMap(existingSubscriptions)
        mergedSubscriptions.putAll(subscriptions)
        cache.set(PriceSubscriptionsKey(accountId), mergedSubscriptions).bind()
        mergedSubscriptions
    }

    override suspend fun addPriceSubscription(
        accountId: String,
        subscription: PriceSubscription
    ): Either<DefaultError, LinkedHashMap<String, PriceSubscription>> = either {
        val cachedSubscriptions = getPriceSubscriptions(accountId).bind()

        val newSubscriptions = LinkedHashMap(cachedSubscriptions).apply {
            put(subscription.subscriptionId, subscription)
        }

        cache.set(PriceSubscriptionsKey(accountId), newSubscriptions).bind()
        newSubscriptions
    }

    override suspend fun replacePriceSubscription(
        accountId: String,
        afterSubscriptionId: String,
        newSubscription: PriceSubscription
    ): Either<DefaultError, LinkedHashMap<String, PriceSubscription>> = either {
        val cachedSubscriptions = getPriceSubscriptions(accountId).bind()

        // First, mark the "after" subscription as deleted
        val existingSubscription = cachedSubscriptions[afterSubscriptionId]
        if (existingSubscription != null) {
            val updatedExistingSubscription = existingSubscription.copy(deleted = true)
            cachedSubscriptions[afterSubscriptionId] = updatedExistingSubscription
        }

        // Create new map maintaining order but adding the new subscription after the specified one
        val newSubscriptions = LinkedHashMap<String, PriceSubscription>()
        var added = false

        cachedSubscriptions.forEach { (id, subscription) ->
            newSubscriptions[id] = subscription

            // Add new subscription immediately after the specified one
            if (id == afterSubscriptionId && !added) {
                newSubscriptions[newSubscription.subscriptionId] = newSubscription
                added = true
            }
        }

        // If afterSubscriptionId wasn't found, just add to the end
        if (!added) {
            newSubscriptions[newSubscription.subscriptionId] = newSubscription
        }

        cache.set(PriceSubscriptionsKey(accountId), newSubscriptions).bind()
        newSubscriptions
    }

    override suspend fun markPriceSubscriptionAsDeleted(
        accountId: String,
        subscriptionId: String,
        deleted: Boolean
    ): Either<DefaultError, PriceSubscription?> = either {
        val cachedSubscriptions = getPriceSubscriptions(accountId).bind()
        val subscription = cachedSubscriptions[subscriptionId] ?: return@either null

        val updatedSubscription = subscription.copy(deleted = deleted)
        val updatedSubscriptions = LinkedHashMap(cachedSubscriptions).apply {
            put(subscriptionId, updatedSubscription)
        }

        cache.set(PriceSubscriptionsKey(accountId), updatedSubscriptions).bind()
        updatedSubscription
    }

    override suspend fun removePriceSubscription(
        accountId: String,
        subscriptionId: String
    ): Either<DefaultError, PriceSubscription?> = either {
        val cachedSubscriptions = getPriceSubscriptions(accountId).bind()
        val removedSubscription = cachedSubscriptions[subscriptionId]

        if (removedSubscription != null) {
            val updatedSubscriptions = LinkedHashMap(cachedSubscriptions).apply {
                remove(subscriptionId)
            }
            cache.set(PriceSubscriptionsKey(accountId), updatedSubscriptions).bind()
        }
        removedSubscription
    }

    override suspend fun removeMarketSubscriptions(
        accountId: String,
        marketId: String
    ): Either<DefaultError, List<PriceSubscription>> = either {
        val cachedSubscriptions = getPriceSubscriptions(accountId).bind()
        val removedSubscriptions = cachedSubscriptions.values.filter { it.marketId == marketId }

        if (removedSubscriptions.isNotEmpty()) {
            val updatedSubscriptions = LinkedHashMap(cachedSubscriptions).apply {
                removedSubscriptions.forEach {
                    remove(it.subscriptionId)
                }
            }
            cache.set(PriceSubscriptionsKey(accountId), updatedSubscriptions).bind()
        }
        removedSubscriptions
    }

    @VisibleForTesting
    internal data class PriceSubscriptionsKey(val accountId: String) : Storage.Key<LinkedHashMap<String, PriceSubscription>>
}
