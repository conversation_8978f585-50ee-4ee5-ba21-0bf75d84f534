package com.b2broker.b2core.storage.b2trader.settings

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import arrow.core.Either
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.B2TraderSettings
import com.b2broker.b2core.model.marketchart.MarketsChartType
import com.b2broker.b2core.storage.runOrDefaultError
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import javax.inject.Inject

internal class DataStoreB2TraderSettingsStorage @Inject constructor(
    private val storageProvider: B2TraderSettingsStorageProvider,
) : B2TraderSettingsStorage {

    override fun observeSettings(): Flow<B2TraderSettings> =
        storageProvider.getB2TraderSettingsStorage().data
            .distinctUntilChanged()
            .map { preferences ->
                val areZeroAssetsHidden = preferences[KEY_ASSETS_ZERO_ASSETS_HIDDEN] ?: false

                val marketChartType = preferences[KEY_MARKETS_MARKET_CHART_TYPE]?.let { type ->
                    runCatching { MarketsChartType.valueOf(type) }
                        .getOrElse { MarketsChartType.Candlestick }
                } ?: MarketsChartType.Candlestick

                val marketChartVisible = preferences[KEY_MARKETS_MARKET_CHART_VISIBLE] ?: true
                val marketChartPnlVisible = preferences[KEY_MARKET_CHART_PNL_VISIBLE] ?: true
                val marketChartTriggersVisible = preferences[KEY_MARKET_CHART_TRIGGERS_VISIBLE] ?: true
                val marketChartExecutedOrdersVisible = preferences[KEY_MARKET_CHART_EXECUTED_ORDERS_VISIBLE] ?: true
                val marketChartOpenOrdersVisible = preferences[KEY_MARKET_CHART_OPEN_ORDER_VISIBLE] ?: true

                B2TraderSettings(
                    areZeroAssetsHidden = areZeroAssetsHidden,
                    marketChartType = marketChartType,
                    marketChartVisible = marketChartVisible,
                    marketChartPnlVisible = marketChartPnlVisible,
                    marketChartTriggersVisible = marketChartTriggersVisible,
                    marketChartOpenOrdersVisible = marketChartOpenOrdersVisible,
                    marketChartExecutedOrdersVisible = marketChartExecutedOrdersVisible,
                )
            }

    override suspend fun setMarketChartType(
        newType: MarketsChartType
    ): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKETS_MARKET_CHART_TYPE] = newType.name
        }
    }

    override suspend fun setMarketChartVisible(isVisible: Boolean): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKETS_MARKET_CHART_VISIBLE] = isVisible
        }
    }

    override suspend fun setPnlVisible(isVisible: Boolean): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKET_CHART_PNL_VISIBLE] = isVisible
        }
    }

    override suspend fun setTriggersVisible(isVisible: Boolean): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKET_CHART_TRIGGERS_VISIBLE] = isVisible
        }
    }

    override suspend fun setOpenOrdersVisible(isVisible: Boolean): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKET_CHART_OPEN_ORDER_VISIBLE] = isVisible
        }
    }

    override suspend fun setExecutedOrdersVisible(isVisible: Boolean): Either<DefaultError, Unit> = runOrDefaultError {
        storageProvider.getB2TraderSettingsStorage().edit { preferences ->
            preferences[KEY_MARKET_CHART_EXECUTED_ORDERS_VISIBLE] = isVisible
        }
    }

    override suspend fun clear(): Either<DefaultError, Unit> =
        runOrDefaultError {
            storageProvider.getB2TraderSettingsStorage().edit { preferences -> preferences.clear() }
        }

    companion object {
        private val KEY_ASSETS_ZERO_ASSETS_HIDDEN = booleanPreferencesKey("assets_zero_assets_hidden")
        private val KEY_MARKETS_MARKET_CHART_TYPE = stringPreferencesKey("markets_market_chart_type")
        private val KEY_MARKETS_MARKET_CHART_VISIBLE = booleanPreferencesKey("markets_market_chart_visible")
        private val KEY_MARKET_CHART_PNL_VISIBLE = booleanPreferencesKey("market_chart_pnl_visible")
        private val KEY_MARKET_CHART_TRIGGERS_VISIBLE = booleanPreferencesKey("market_chart_triggers_visible")
        private val KEY_MARKET_CHART_OPEN_ORDER_VISIBLE = booleanPreferencesKey("market_chart_open_orders_visible")
        private val KEY_MARKET_CHART_EXECUTED_ORDERS_VISIBLE = booleanPreferencesKey("market_chart_executed_orders_visible")
    }
}
