package com.b2broker.b2core.storage.b2trader.accounts.di

import com.b2broker.b2core.storage.b2trader.accounts.B2TraderAccountStorage
import com.b2broker.b2core.storage.b2trader.accounts.B2TraderCurrentAccountStorage
import com.b2broker.b2core.storage.b2trader.accounts.CachedB2TraderAccountStorage
import com.b2broker.b2core.storage.b2trader.accounts.CachedB2TraderCurrentAccountStorage
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
internal interface B2TraderAccountStorageModule {

    @Binds
    fun bindB2TraderAccountStorage(storage: CachedB2TraderAccountStorage): B2TraderAccountStorage

    @Binds
    fun bindB2TraderCurrentAccountStorage(storage: CachedB2TraderCurrentAccountStorage): B2TraderCurrentAccountStorage
}
