package com.b2broker.b2core.storage.b2trader.platform

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.auth.B2TraderPlatform
import com.b2broker.b2core.storage.b2trader.platform.mapper.B2TraderPlatformAuthMapper
import com.b2broker.b2core.storage.b2trader.platform.mapper.B2TraderPlatformEntityMapper
import com.b2broker.b2core.storage.b2trader.platform.model.B2TraderPlatformEntity
import com.b2broker.b2core.storage.di.EncryptedDataStore
import com.b2broker.b2core.storage.di.StorageJson
import com.b2broker.b2core.storage.runOrDefaultError
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

internal class DataStoreB2TraderPlatformsStorage
@Inject constructor(
    @EncryptedDataStore private val dataStore: DataStore<Preferences>,
    @StorageJson private val json: Json,
    @DefaultDispatcher private val mappingDispatcher: CoroutineDispatcher,
    private val b2TraderPlatformAuthStorage: B2TraderPlatformAuthStorage,
    private val b2TraderPlatformEntityMapper: B2TraderPlatformEntityMapper,
    private val b2TraderPlatformAuthMapper: B2TraderPlatformAuthMapper,
) : B2TraderPlatformsStorage {

    override suspend fun setPlatform(b2TraderPlatform: B2TraderPlatform): Either<DefaultError, Unit> = runOrDefaultError {
        val platformId = b2TraderPlatform.platformId
        dataStore.edit { preferences ->
            withContext(mappingDispatcher) {
                preferences[platformTokenKey(platformId)] = b2TraderPlatform.toJsonString()
                preferences[intPreferencesKey(CURRENT_PLATFORM_ID)] = platformId
            }
        }
        updateCachedPlatform(b2TraderPlatform)
    }

    override suspend fun getPlatform(platformId: Int?): Either<DefaultError, B2TraderPlatform?> = runOrDefaultError {
        dataStore.data
            .map { preferences ->
                platformId?.let {
                    preferences[platformTokenKey(platformId)]?.toPlatformAuth()
                }
            }
            .flowOn(mappingDispatcher)
            .first()
    }

    override suspend fun clearPlatforms(): Either<DefaultError, Unit> = runOrDefaultError {
        dataStore.edit { preferences ->
            val tokenKeys = preferences.asMap().keys
                .filter { key -> key.name.contains(PLATFORM_AUTH_KEY_PREFIX) || key.name == CURRENT_PLATFORM_ID }
            tokenKeys.forEach { key -> preferences.remove(key) }
            preferences.remove(intPreferencesKey(CURRENT_PLATFORM_ID))
        }
        updateCachedPlatform(null)
    }

    override suspend fun setCurrentPlatformId(platformId: Int): Either<DefaultError, Unit> = runOrDefaultError {
        dataStore.edit { preferences ->
            withContext(mappingDispatcher) {
                preferences[intPreferencesKey(CURRENT_PLATFORM_ID)] = platformId
            }
        }
    }

    override suspend fun getCurrentPlatformId(): Either<DefaultError, Int?> = runOrDefaultError {
        dataStore.data
            .map { preferences -> preferences[intPreferencesKey(CURRENT_PLATFORM_ID)] }
            .flowOn(mappingDispatcher)
            .first()
    }

    override suspend fun getCurrentPlatform(): Either<DefaultError, B2TraderPlatform?> = either {
        b2TraderPlatformAuthStorage.getCurrentPlatform().bind() ?: getCurrentPlatformFromDataStore().bind()
    }

    private suspend fun getCurrentPlatformFromDataStore(): Either<DefaultError, B2TraderPlatform?> = either {
        val currentPlatformId = getCurrentPlatformId().bind()
        val platform = getPlatform(currentPlatformId).bind()
        updateCachedPlatform(platform)
        platform
    }

    private suspend fun updateCachedPlatform(b2TraderPlatform: B2TraderPlatform?) {
        b2TraderPlatform?.let { b2TraderPlatformAuthStorage.setCurrentPlatform(b2TraderPlatform) } ?: b2TraderPlatformAuthStorage.clear()
    }

    private fun platformTokenKey(platformId: Int) = stringPreferencesKey("$PLATFORM_AUTH_KEY_PREFIX$platformId")

    private fun B2TraderPlatform.toJsonString(): String {
        return json.encodeToString(b2TraderPlatformEntityMapper.map(this))
    }

    private fun String.toPlatformAuth(): B2TraderPlatform {
        return b2TraderPlatformAuthMapper.map(json.decodeFromString<B2TraderPlatformEntity>(this))
    }

    internal companion object {
        const val PLATFORM_AUTH_KEY_PREFIX = "PLATFORM_AUTH_"
        const val CURRENT_PLATFORM_ID = "CURRENT_PLATFORM_ID"
    }
}
