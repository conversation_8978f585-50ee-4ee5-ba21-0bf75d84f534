package com.b2broker.b2core.storage.b2trader.platform.mapper

import com.b2broker.b2core.model.b2trader.auth.B2TraderPlatform
import com.b2broker.b2core.storage.b2trader.platform.model.B2TraderPlatformEntity
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class B2TraderPlatformMapperTest {

    private lateinit var mapper: B2TraderPlatformAuthMapper

    @BeforeEach
    fun setUp() {
        mapper = B2TraderPlatformAuthMapper()
    }

    @Test
    fun `GIVEN B2TraderPlatformEntity WHEN map THEN correct B2TraderPlatform returned`() {
        // GIVEN
        val b2TraderPlatformEntity = B2TraderPlatformEntity(
            accessToken = "accessToken",
            refreshToken = "refreshToken",
            platformId = 1,
            host = "host",
            expiresAt = 1_234_567_890L,
        )

        // WHEN
        val result = mapper.map(b2TraderPlatformEntity)

        // THEN
        val expected = B2TraderPlatform(
            accessToken = b2TraderPlatformEntity.accessToken,
            refreshToken = b2TraderPlatformEntity.refreshToken,
            platformId = b2TraderPlatformEntity.platformId,
            host = b2TraderPlatformEntity.host,
            expiresAt = b2TraderPlatformEntity.expiresAt,
        )
        assertThat(result).isEqualTo(expected)
    }
}
