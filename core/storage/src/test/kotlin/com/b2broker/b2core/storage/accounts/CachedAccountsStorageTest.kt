package com.b2broker.b2core.storage.accounts

import arrow.core.right
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.storage.InMemCacheStorage
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CachedAccountsStorageTest {

    private val cacheStorage: InMemCacheStorage = mockk()
    private lateinit var storage: CachedAccountsStorage

    @BeforeEach
    fun setUp() {
        storage = CachedAccountsStorage(cache = cacheStorage)
    }

    @Test
    fun `GIVEN accounts WHEN setAccounts THEN set called with the right parameters`() = runTest {
        // GIVEN
        val accounts = listOf(Account.fixture())
        coEvery { cacheStorage.set(CachedAccountsStorage.AccountsKey, accounts) } returns Unit.right()

        // WHEN
        storage.setAccounts(accounts)

        // THEN
        coVerify(exactly = 1) { cacheStorage.set(CachedAccountsStorage.AccountsKey, accounts) }
    }

    @Test
    fun `GIVEN accounts WHEN getAccounts THEN accounts saved`() = runTest {
        // GIVEN
        val accounts = listOf(Account.fixture())
        coEvery { cacheStorage.get(CachedAccountsStorage.AccountsKey) } returns accounts.right()

        // WHEN
        val actual = storage.getAccounts()

        // THEN
        assertThat(actual).isEqualTo(accounts.right())
    }

    @Test
    fun `WHEN removeAccounts THEN remove called with the right key`() = runTest {
        // GIVEN
        coEvery { cacheStorage.remove(CachedAccountsStorage.AccountsKey) } returns Unit.right()

        // WHEN
        storage.removeAccounts()

        // THEN
        coVerify(exactly = 1) { cacheStorage.remove(CachedAccountsStorage.AccountsKey) }
    }

    @Test
    fun `GIVEN no accounts WHEN getAccount THEN returns null`() = runTest {
        // GIVEN
        coEvery { cacheStorage.get(CachedAccountsStorage.AccountsKey) } returns null.right()

        // WHEN
        val account = storage.getAccount(TEST_ID)

        // THEN
        assertThat(account).isEqualTo(null.right())
    }

    @Test
    fun `GIVEN account WHEN getAccount THEN returns account with given id`() = runTest {
        // GIVEN
        val accounts = listOf(Account.fixture(id = TEST_ID))
        coEvery { cacheStorage.get(CachedAccountsStorage.AccountsKey) } returns accounts.right()

        // WHEN
        val account = storage.getAccount(TEST_ID).getOrNull()

        // THEN
        assertThat(account).isEqualTo(accounts.first())
    }

    @Test
    fun `GIVEN accounts WHEN updateAccount THEN accounts updated`() = runTest {
        // GIVEN
        val account = Account.fixture(id = TEST_ID, favourite = false)
        coEvery { cacheStorage.get(CachedAccountsStorage.AccountsKey) } returns listOf(account).right()
        coEvery { cacheStorage.set(CachedAccountsStorage.AccountsKey, any()) } returns Unit.right()

        // WHEN
        val newAccount = account.copy(favourite = true)
        storage.updateAccount(newAccount)

        // THEN
        coVerify(exactly = 1) { cacheStorage.set(CachedAccountsStorage.AccountsKey, listOf(newAccount)) }
    }

    companion object {
        private const val TEST_ID = "-42"
    }
}
