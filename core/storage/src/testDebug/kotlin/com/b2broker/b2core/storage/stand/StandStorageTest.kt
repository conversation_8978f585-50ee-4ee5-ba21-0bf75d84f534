package com.b2broker.b2core.storage.stand

import android.content.SharedPreferences
import com.b2broker.b2core.model.stand.StandConfiguration
import com.google.common.truth.Truth
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

class StandStorageTest {
    private lateinit var storage: StandStorage

    private val sharedPreferences: SharedPreferences = mockk(relaxed = true)

    @Test
    fun `GIVEN prefs have value WHEN get current stand THEN received stand from prefs`() = runTest {
        // GIVEN
        val mockPreferencesValue = "Default value"
        val stand = StandConfiguration(url = mockPreferencesValue, name = mockPreferencesValue)

        coEvery { sharedPreferences.getString(any(), any()) } returns mockPreferencesValue
        storage = B2CoreStandStorage(sharedPreferences)

        // WHEN
        val value = storage.currentStand

        // THEN
        Truth.assertThat(value).isEqualTo(stand)
    }

    @Test
    fun `GIVEN storage with prefs WHEN update current stand THEN prefs updated`() = runTest {
        // GIVEN
        val stand = StandConfiguration(url = "url", name = "name")

        storage = B2CoreStandStorage(sharedPreferences)

        // WHEN
        storage.currentStand = stand

        // THEN
        verify(exactly = 1) { sharedPreferences.edit() }
    }

    @Test
    fun `GIVEN storage with default stands WHEN get all stands THEN received values`() = runTest {
        // GIVEN
        storage = B2CoreStandStorage(sharedPreferences)

        // WHEN
        val stands = storage.getAllStands()

        // THEN
        Truth.assertThat(stands.size).isAtLeast(2)
    }
}
