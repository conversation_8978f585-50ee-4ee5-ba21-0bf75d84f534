package com.b2broker.b2core.network.b2trader.rest.orders.v4.error

import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.orders.error.B2TraderOrdersError.LimitPriceRangeError
import com.b2broker.b2core.model.b2trader.orders.error.B2TraderOrdersError.NotEnoughMarginError
import com.b2broker.b2core.network.b2trader.error.B2TraderApiErrorDto
import com.b2broker.b2core.network.b2trader.error.B2TraderApiErrorDtoMapper
import com.b2broker.b2core.network.di.NetworkJson
import kotlinx.serialization.json.Json
import javax.inject.Inject

class B2TraderOrderErrorMapper
@Inject constructor(
    @NetworkJson json: Json
) : B2TraderApiErrorDtoMapper<DefaultError>(
    messageError = ::DefaultError,
    json = json,
) {

    override fun map(code: Int, apiError: B2TraderApiErrorDto): DefaultError {
        return when (apiError.code) {
            "TR0140" -> NotEnoughMarginError()
            "TR0042" -> LimitPriceRangeError()
            else -> super.map(code, apiError)
        }
    }
}
