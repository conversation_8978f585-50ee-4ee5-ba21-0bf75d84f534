package com.b2broker.b2core.network.fields.di

import com.b2broker.b2core.network.countries.CountriesApi
import com.b2broker.b2core.network.di.B2CoreApiV1
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import retrofit2.create

@Module
@InstallIn(SingletonComponent::class)
internal object FieldsNetworkModule {

    @Provides
    fun provideCountriesApi(
        @B2CoreApiV1 retrofit: Retrofit,
    ): CountriesApi {
        return retrofit.create()
    }
}
