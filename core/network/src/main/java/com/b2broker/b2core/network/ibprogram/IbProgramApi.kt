package com.b2broker.b2core.network.ibprogram

import arrow.core.Either
import arrow.retrofit.adapter.either.networkhandling.CallError
import com.b2broker.b2core.network.ibprogram.model.IbClientPageDto
import com.b2broker.b2core.network.ibprogram.model.IbGroupsPageDto
import com.b2broker.b2core.network.ibprogram.model.IbLinkPageDto
import com.b2broker.b2core.network.ibprogram.model.IbPaymentPageDto
import com.b2broker.b2core.network.ibprogram.model.IbProgramJoinRequest
import com.b2broker.b2core.network.ibprogram.model.IbProgramJoinStatusResponse
import com.b2broker.b2core.network.ibprogram.model.IbUsersPageDto
import com.b2broker.b2core.network.ibprogram.model.IbWalletPageDto
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface IbProgramApi {

    @GET("users")
    suspend fun getUsers(): Either<CallError, IbUsersPageDto>

    @GET("groups")
    suspend fun getGroups(): Either<CallError, IbGroupsPageDto>

    // With these parameters the server returns all the records it has
    @GET("users/{userId}/clients")
    suspend fun getAllClients(
        @Path("userId") userId: String,
        @Query("limit") limit: Int,
        @Query("offset") offset: Int,
        @Query("subIb") subIb: Int?,
    ): Either<CallError, IbClientPageDto>

    @GET("users/{userId}/clients/{clientId}/clients")
    suspend fun getSubClients(
        @Path("userId") userId: String,
        @Path("clientId") clientId: String?,
        @Query("limit") limit: Int,
        @Query("offset") offset: Int,
    ): Either<CallError, IbClientPageDto>

    @GET("users/{userId}/links")
    suspend fun getUserLinks(
        @Path("userId") userId: String
    ): Either<CallError, IbLinkPageDto>

    @POST("users")
    suspend fun joinToProgram(
        @Body request: IbProgramJoinRequest
    ): Either<CallError, IbProgramJoinStatusResponse>

    @GET("users/{userId}/wallets")
    suspend fun getUserWallets(
        @Path("userId") userId: String
    ): Either<CallError, IbWalletPageDto>

    @GET("users/{userId}/payments")
    suspend fun getUserPayments(
        @Path("userId") userId: String,
        @Query("limit") limit: Int,
        @Query("offset") offset: Int,
    ): Either<CallError, IbPaymentPageDto>
}
