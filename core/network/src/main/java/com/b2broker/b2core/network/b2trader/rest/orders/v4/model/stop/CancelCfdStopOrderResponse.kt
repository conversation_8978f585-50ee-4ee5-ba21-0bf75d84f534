package com.b2broker.b2core.network.b2trader.rest.orders.v4.model.stop

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CancelCfdStopOrderResponse(
    @SerialName("order") val order: CancelCfdStopOrderDto
) {
    companion object {
        fun fixture(
            order: CancelCfdStopOrderDto = CancelCfdStopOrderDto.fixture()
        ) = CancelCfdStopOrderResponse(order = order)
    }
}
