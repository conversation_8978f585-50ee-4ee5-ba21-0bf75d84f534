package com.b2broker.b2core.network.verification.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class VerificationWizardResponse(
    @SerialName("status") val status: Int,
    @SerialName("data") val data: VerificationWizardDto,
)

@Serializable
data class VerificationWizardDto(
    @SerialName("code") val code: Int? = null,
    @SerialName("message") val message: String? = null,
    @SerialName("localizedMessage") val localizedMessage: String? = null,
)
