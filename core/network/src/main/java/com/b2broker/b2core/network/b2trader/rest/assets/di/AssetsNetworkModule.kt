package com.b2broker.b2core.network.b2trader.rest.assets.di

import com.b2broker.b2core.network.b2trader.rest.assets.AssetsApi
import com.b2broker.b2core.network.b2trader.rest.di.B2TraderPlatformClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import retrofit2.create

@Module
@InstallIn(SingletonComponent::class)
internal object AssetsNetworkModule {

    @Provides
    fun provideAssetsApi(
        @B2TraderPlatformClient retrofit: Retrofit
    ): AssetsApi {
        return retrofit.create()
    }
}
