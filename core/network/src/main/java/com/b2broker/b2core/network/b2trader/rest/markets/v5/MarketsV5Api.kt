package com.b2broker.b2core.network.b2trader.rest.markets.v5

import arrow.core.Either
import arrow.retrofit.adapter.either.networkhandling.CallError
import com.b2broker.b2core.network.b2trader.rest.markets.v5.model.B2TraderFundingHistoryPageDto
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Url

interface MarketsV5Api {

    @GET
    suspend fun getFundingHistory(
        @Url url: String,
        @Header("AccountId") accountId: String,
    ): Either<CallError, B2TraderFundingHistoryPageDto>
}
