package com.b2broker.b2core.network.payments.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class WithdrawalFlowDto(
    @SerialName("uuid") val uuid: String,
    @SerialName("workflow") val workflow: String,
    @SerialName("data") val data: JsonElement? = null,
) {

    companion object {
        fun fixture(
            uuid: String = "163aefb8-e67a-42fd-a428-6ac16fba6060",
            workflow: String = "workflow_2bad",
            data: JsonElement? = null,
        ) = WithdrawalFlowDto(
            uuid = uuid,
            workflow = workflow,
            data = data,
        )
    }
}
