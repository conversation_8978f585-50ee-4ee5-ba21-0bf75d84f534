package com.b2broker.b2core.network.transaction

import arrow.core.Either
import arrow.retrofit.adapter.either.networkhandling.CallError
import com.b2broker.b2core.network.GenericV1Response
import com.b2broker.b2core.network.transaction.model.RejectTransferRequestDto
import retrofit2.http.Body
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.Path

interface TransactionRejectApi {

    @PATCH("payouts/reject/{transactionId}")
    suspend fun rejectWithdrawalTransaction(
        @Path("transactionId") transactionId: Long,
    ): Either<CallError, GenericV1Response<Unit>>

    @POST("transfer/reject")
    suspend fun rejectTransferTransaction(
        @Body request: RejectTransferRequestDto,
    ): Either<CallError, GenericV1Response<Unit>>
}
