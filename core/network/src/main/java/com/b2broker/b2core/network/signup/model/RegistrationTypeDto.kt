package com.b2broker.b2core.network.signup.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RegistrationTypeDto(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("description")
    val description: String? = null,
    @SerialName("type")
    val type: String? = null,
    @SerialName("default")
    val default: Boolean = false,
) {

    companion object {
        fun fixture(
            id: Int = 30,
            name: String = "Individual",
            description: String = "description",
            type: String = "type",
            default: Boolean = true,
        ) = RegistrationTypeDto(
            id = id,
            name = name,
            description = description,
            type = type,
            default = default,
        )
    }
}
