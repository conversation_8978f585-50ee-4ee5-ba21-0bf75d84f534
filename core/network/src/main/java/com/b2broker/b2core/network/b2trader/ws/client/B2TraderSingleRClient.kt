package com.b2broker.b2core.network.b2trader.ws.client

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.logger.Logger
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError.HubConnectionNotExists
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError.PlatformNotAuthorized
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError.Unauthorized
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError.UnknownError
import com.b2broker.b2core.storage.b2trader.platform.B2TraderPlatformsStorage
import com.microsoft.signalr.HttpRequestException
import com.microsoft.signalr.HubConnection
import com.microsoft.signalr.HubConnectionBuilder
import com.microsoft.signalr.HubConnectionState
import io.reactivex.rxjava3.core.Single
import kotlinx.coroutines.delay
import kotlinx.coroutines.rx3.await
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

private const val UNAUTHORIZED_TOKEN_CODE = 401

internal class B2TraderSingleRClient(
    private val b2TraderPlatformsStorage: B2TraderPlatformsStorage,
    private val connectionPath: String,
) : SingleRClient {

    override val connectionState: HubConnectionState
        get() = hubConnection?.connectionState ?: HubConnectionState.DISCONNECTED

    private var hubConnection: HubConnection? = null

    private val connectionMutex = Mutex()

    override suspend fun startConnection(subscriptionKey: String): Either<B2TraderWebSocketError, HubConnection> {
        return connectionMutex.withLock {
            Logger.d("Start connection state: ${hubConnection?.connectionState} for $subscriptionKey")
            if (hubConnection == null || hubConnection?.connectionState == HubConnectionState.DISCONNECTED) {
                val platform = b2TraderPlatformsStorage.getCurrentPlatform().getOrNull()

                val host = platform?.host ?: return@withLock PlatformNotAuthorized().left()
                Logger.d("WebSocket Host: $host for $subscriptionKey")

                hubConnection = HubConnectionBuilder.create(host + connectionPath)
                    .withAccessTokenProvider(Single.just(platform.accessToken))
                    .build()

                hubConnection?.onClosed { error ->
                    Logger.d("WebSocket connection closed for $subscriptionKey with error: ${error.message}")
                }

                tryConnect(subscriptionKey)
            } else {
                hubConnection?.right() ?: HubConnectionNotExists().left()
            }
        }
    }

    override suspend fun safeDisconnect() {
        try {
            disconnect()
            delay(SAFE_DISCONNECT_TIMEOUT)
        } catch (e: Exception) {
            Logger.d("Error during disconnect: ${e.message}")
        }
    }

    private suspend fun disconnect() {
        Logger.d("Disconnecting WebSocket connection")
        if (hubConnection == null || hubConnection?.connectionState == HubConnectionState.DISCONNECTED) {
            Logger.d("WebSocket connection already disconnected")
            return
        }

        connectionMutex.withLock {
            val connection = hubConnection
            if (connection != null) {
                try {
                    if (connection.connectionState == HubConnectionState.CONNECTED) {
                        connection.stop().await()
                    }
                } catch (e: Exception) {
                    Logger.d(e, "Error during disconnect")
                } finally {
                    hubConnection = null
                    Logger.d("WebSocket connection stopped")
                }
            }
        }
    }

    private suspend fun tryConnect(subscriptionKey: String): Either<B2TraderWebSocketError, HubConnection> {
        return try {
            val connection = hubConnection ?: return HubConnectionNotExists().left()

            connection.start()?.await()
            Logger.d("Try connect state: ${connection.connectionState}, subscriptionKey: $subscriptionKey")

            if (connection.connectionState != HubConnectionState.CONNECTED) {
                return UnknownError("Connection not established").left()
            }
            connection.right()
        } catch (e: HttpRequestException) {
            if (e.statusCode == UNAUTHORIZED_TOKEN_CODE) {
                Unauthorized().left()
            } else {
                B2TraderWebSocketError.HttpRequestException("HttpRequestException: ${e.message}").left()
            }
        } catch (e: Exception) {
            UnknownError("Unknown error: ${e.message}").left()
        }
    }

    private companion object {
        const val SAFE_DISCONNECT_TIMEOUT = 1000L
    }
}
