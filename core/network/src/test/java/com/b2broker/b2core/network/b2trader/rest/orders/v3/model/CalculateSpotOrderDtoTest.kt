package com.b2broker.b2core.network.b2trader.rest.orders.v3.model

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class CalculateSpotOrderDtoTest {

    @Test
    fun `GIVEN CalculateOrderDto WHEN serialize THEN get correct json`() {
        // GIVEN
        val dto = CalculateOrderDto.fixture()

        // WHEN
        val json = Json { prettyPrint = true }
        val actual = json.encodeToString(CalculateOrderDto.serializer(), dto)

        // THEN
        val expectedJson = """
            {
                "marketId": "spot.btc_usd",
                "orderType": "limit",
                "side": "buy",
                "requestedPrice": "100.0",
                "requestedBaseAmount": "1.0"
            }
        """.trimIndent()

        assertEquals(expectedJson, actual)
    }

    @Test
    fun `GIVEN json WHEN deserialize THEN get correct CalculateOrderDto`() {
        // GIVEN
        val json = """
            {
                "marketId": "spot.btc_usd",
                "orderType": "limit",
                "side": "buy",
                "requestedPrice": "100.0",
                "requestedBaseAmount": "1.0"
            }
        """.trimIndent()

        // WHEN
        val actual = Json.decodeFromString(CalculateOrderDto.serializer(), json)

        // THEN
        val expected = CalculateOrderDto.fixture()

        assertEquals(expected, actual)
    }
}
