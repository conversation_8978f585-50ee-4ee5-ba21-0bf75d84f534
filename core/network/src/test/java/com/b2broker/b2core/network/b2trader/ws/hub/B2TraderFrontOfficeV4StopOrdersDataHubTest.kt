package com.b2broker.b2core.network.b2trader.ws.hub

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError
import com.b2broker.b2core.network.b2trader.rest.orders.v3.model.stop.StopOrderDto
import com.b2broker.b2core.network.b2trader.ws.client.B2TraderWebSocketClient
import com.b2broker.b2core.network.b2trader.ws.client.B2TraderWebSocketTarget
import com.b2broker.b2core.network.b2trader.ws.hub.responsemerger.StopOrdersResponseMerger
import com.b2broker.b2core.network.b2trader.ws.hub.retry.RetryConfig
import com.b2broker.b2core.network.b2trader.ws.hub.subscription.HubManagerSubscriptionStorage
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.reflect.KClass
import kotlin.time.Duration.Companion.milliseconds

internal class B2TraderFrontOfficeV4StopOrdersDataHubTest {

    private val webSocketClient: B2TraderWebSocketClient = mockk(relaxed = true)
    private val stopOrdersResponseMerger = StopOrdersResponseMerger()

    private lateinit var subscriptionStorage: HubManagerSubscriptionStorage
    private lateinit var hubManager: HubManager
    private lateinit var stopOrdersHub: B2TraderFrontOfficeV4StopOrdersDataHub
    private lateinit var testScope: TestScope

    @BeforeEach
    fun setUp() {
        val testDispatcher = UnconfinedTestDispatcher()
        testScope = TestScope(testDispatcher)
        subscriptionStorage = HubManagerSubscriptionStorage()

        hubManager = HubManager(
            webSocketClient = webSocketClient,
            subscriptionStorage = subscriptionStorage,
            ioDispatcher = testDispatcher,
            retryConfig = RetryConfig(maxAttempts = 0, initialDelay = 0),
            scope = testScope,
            reconnectTimeout = 100.milliseconds,
            stopTimeout = 0,
        )
        stopOrdersHub = B2TraderFrontOfficeV4StopOrdersDataHub(hubManager, stopOrdersResponseMerger)
    }

    @Test
    fun `GIVEN accountId WHEN observeStopOrders THEN returns flow of data`() = runTest {
        // GIVEN
        val data = arrayOf(StopOrderDto.fixture())
        setupAnswer(
            data = data,
            target = B2TraderWebSocketTarget.OPEN_STOP_ORDERS,
        )

        // WHEN
        val result = stopOrdersHub.observeStopOrders(ACCOUNT_ID).first()

        // THEN
        assertThat(result.getOrNull()).isEqualTo(data)
    }

    @Test
    fun `GIVEN error WHEN observeStopOrders THEN returns error`() = runTest {
        // GIVEN
        setupError(Array<StopOrderDto>::class)

        // WHEN
        val result = stopOrdersHub.observeStopOrders(ACCOUNT_ID).first()

        // THEN
        assertThat(result.leftOrNull()).isInstanceOf(B2TraderWebSocketError.StreamException::class.java)
    }

    private inline fun <reified T : Any> setupAnswer(
        data: T,
        target: B2TraderWebSocketTarget,
        args: Array<Any> = arrayOf(ACCOUNT_ID)
    ) {
        coEvery {
            webSocketClient.startConnection(
                subscriptionKey = any(),
                returnType = data::class,
                target = target,
                args = args,
            )
        } returns flowOf(data).right()
    }

    private fun <T : Any> setupError(
        returnType: KClass<T>
    ) {
        coEvery {
            webSocketClient.startConnection(
                subscriptionKey = any(),
                returnType = returnType,
                target = any(),
                args = any(),
            )
        } returns B2TraderWebSocketError.StreamException().left()
    }

    companion object {
        private const val ACCOUNT_ID = "testAccountId"
    }
}
