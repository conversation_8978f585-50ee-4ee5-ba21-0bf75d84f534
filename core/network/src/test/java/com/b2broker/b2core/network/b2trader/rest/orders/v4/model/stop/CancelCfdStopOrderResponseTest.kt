package com.b2broker.b2core.network.b2trader.rest.orders.v4.model.stop

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class CancelCfdStopOrderResponseTest {

    private val json = Json { prettyPrint = true }

    @Test
    fun `GIVEN CancelCfdStopOrderResponse WHEN serialize THEN get correct json`() {
        // GIVEN
        val response = CancelCfdStopOrderResponse.fixture()

        // WHEN
        val actual = json.encodeToString(CancelCfdStopOrderResponse.serializer(), response)

        // THEN
        val expectedJson = """
            {
                "order": {
                    "marketId": "cfd.btc_usdt",
                    "marketDisplayName": "BTC/USDT",
                    "orderId": "65f1990aae5ec088da163949",
                    "orderType": "Market",
                    "side": "Buy",
                    "requestedPrice": "0.002",
                    "activationPrice": "0.002",
                    "requestedAmount": "0.002",
                    "timeInForce": "Gtc",
                    "status": "Cancelled",
                    "createdAt": "2024-03-18T14:15:22Z",
                    "updatedAt": "2024-03-18T14:15:22Z",
                    "cancellationDate": "2024-03-18T14:15:22Z"
                }
            }
        """.trimIndent()

        assertEquals(expectedJson, actual)
    }

    @Test
    fun `GIVEN json WHEN deserialize THEN get correct CancelCfdStopOrderResponse`() {
        // GIVEN
        val jsonString = """
            {
                "order": {
                    "marketId": "cfd.btc_usdt",
                    "marketDisplayName": "BTC/USDT",
                    "orderId": "65f1990aae5ec088da163949",
                    "orderType": "Market",
                    "side": "Buy",
                    "requestedPrice": "0.002",
                    "activationPrice": "0.002",
                    "requestedAmount": "0.002",
                    "timeInForce": "Gtc",
                    "status": "Cancelled",
                    "createdAt": "2024-03-18T14:15:22Z",
                    "updatedAt": "2024-03-18T14:15:22Z",
                    "cancellationDate": "2024-03-18T14:15:22Z"
                }
            }
        """.trimIndent()

        // WHEN
        val actual = json.decodeFromString(CancelCfdStopOrderResponse.serializer(), jsonString)

        // THEN
        val expected = CancelCfdStopOrderResponse.fixture()
        assertEquals(expected, actual)
    }

    @Test
    fun `GIVEN json with null dates WHEN deserialize THEN get correct CancelCfdStopOrderResponse`() {
        // GIVEN
        val jsonString = """
            {
                "order": {
                    "marketId": "cfd.btc_usdt",
                    "marketDisplayName": "BTC/USDT",
                    "orderId": "65f1990aae5ec088da163949",
                    "orderType": "Market",
                    "side": "Buy",
                    "requestedPrice": "0.002",
                    "activationPrice": "0.002",
                    "requestedAmount": "0.002",
                    "timeInForce": "Gtc",
                    "status": "Cancelled",
                    "createdAt": null,
                    "updatedAt": null,
                    "cancellationDate": null
                }
            }
        """.trimIndent()

        // WHEN
        val actual = json.decodeFromString(CancelCfdStopOrderResponse.serializer(), jsonString)

        // THEN
        val expected = CancelCfdStopOrderResponse(
            order = CancelCfdStopOrderDto(
                marketId = "cfd.btc_usdt",
                marketDisplayName = "BTC/USDT",
                orderId = "65f1990aae5ec088da163949",
                orderType = "Market",
                side = "Buy",
                requestedPrice = "0.002",
                activationPrice = "0.002",
                requestedAmount = "0.002",
                timeInForce = "Gtc",
                status = "Cancelled",
                createdAt = null,
                updatedAt = null,
                cancellationDate = null,
            ),
        )
        assertEquals(expected, actual)
    }
}
