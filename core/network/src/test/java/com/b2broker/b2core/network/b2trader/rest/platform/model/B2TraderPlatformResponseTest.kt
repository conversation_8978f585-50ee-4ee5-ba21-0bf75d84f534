package com.b2broker.b2core.network.b2trader.rest.platform.model

import com.b2broker.b2core.network.di.NETWORK_JSON
import com.google.common.truth.Truth.assertThat
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.encodeToJsonElement
import org.junit.jupiter.api.Test

internal class B2TraderPlatformResponseTest {

    @Test
    fun `GIVEN PlatformAuthResponse WHEN serialize THEN get correct json`() {
        // GIVEN
        val response = B2TraderPlatformAuthResponse(
            accessToken = "access_token_example",
            refreshToken = "refresh_token_example",
            host = "host_example",
            ttl = 3600L,
        )

        // WHEN
        val actual = NETWORK_JSON.encodeToJsonElement(response)

        // THEN
        val expectedJson = JsonObject(
            mapOf(
                "token" to <PERSON>sonPrim<PERSON>("access_token_example"),
                "refreshToken" to <PERSON>son<PERSON><PERSON><PERSON>("refresh_token_example"),
                "host" to JsonPrimitive("host_example"),
                "ttl" to JsonPrimitive(3600L),
            ),
        )

        assertThat(actual).isEqualTo(expectedJson)
    }
}
