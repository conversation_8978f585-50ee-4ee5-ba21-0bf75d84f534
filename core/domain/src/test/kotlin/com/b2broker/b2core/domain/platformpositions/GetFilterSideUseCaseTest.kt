package com.b2broker.b2core.domain.platformpositions

import com.b2broker.b2core.model.platformpositions.PositionSide
import com.b2broker.b2core.model.platforms.PlatformType
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

internal class GetFilterSideUseCaseTest {
    private val dispatcher = UnconfinedTestDispatcher(TestCoroutineScheduler())

    private val useCase = GetFilterSideUseCase(dispatcher)

    @Test
    fun `GIVEN Platform is CTrader WHEN invoke THEN return name`() = runTest {
        // GIVEN
        val expected = "BUY"

        // WHEN
        val actual = useCase(platformType = PlatformType.CTrader, side = PositionSide.BUY)

        // THEN
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `GIVEN Platform is no CTrader WHEN invoke THEN return index`() = runTest {
        // GIVEN
        val expected = "0"

        // WHEN
        val actual = useCase(platformType = PlatformType.MetaTrader5, side = PositionSide.BUY)

        // THEN
        assertThat(actual).isEqualTo(expected)
    }
}
