package com.b2broker.b2core.domain.b2trader.notification.mapper

import com.b2broker.b2core.model.b2trader.markets.B2TraderMarket
import com.b2broker.b2core.model.b2trader.markets.MarketId
import com.b2broker.b2core.model.b2trader.markets.MarketType
import com.b2broker.b2core.model.b2trader.notification.MarketPriceSubscription
import com.b2broker.b2core.model.b2trader.notification.MarketPriceSubscriptionItem
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ExtendedMarketPriceSubscriptionMapperTest {

    private lateinit var mapper: ExtendedMarketPriceSubscriptionMapper

    @BeforeEach
    fun setUp() {
        mapper = ExtendedMarketPriceSubscriptionMapper()
    }

    @Test
    fun `GIVEN valid subscription and market WHEN map THEN map all fields correctly`() {
        // GIVEN
        val marketId = "btc_usdt"
        val displayName = "BTC/USDT"
        val type = MarketType.SPOT

        val market = B2TraderMarket.fixture(
            marketId = MarketId(marketId),
            displayName = displayName,
            type = type,
        )

        val subscriptionItems = listOf(
            MarketPriceSubscriptionItem.fixture(id = "sub1"),
            MarketPriceSubscriptionItem.fixture(id = "sub2"),
        )

        val subscription = MarketPriceSubscription.fixture(
            marketId = marketId,
            subscriptions = subscriptionItems,
        )

        // WHEN
        val result = mapper.map(subscription to market)

        // THEN
        assertThat(result.marketId).isEqualTo(marketId)
        assertThat(result.displayName).isEqualTo(displayName)
        assertThat(result.type).isEqualTo(type)
        assertThat(result.subscriptions).isEqualTo(subscriptionItems)
    }

    @Test
    fun `GIVEN null subscription and valid market WHEN map THEN map market fields and null subscriptions`() {
        // GIVEN
        val marketId = "eth_usdt"
        val displayName = "ETH/USDT"
        val type = MarketType.CFD

        val market = B2TraderMarket.fixture(
            marketId = MarketId(marketId),
            displayName = displayName,
            type = type,
        )

        val subscription: MarketPriceSubscription? = null

        // WHEN
        val result = mapper.map(subscription to market)

        // THEN
        assertThat(result.marketId).isEqualTo(marketId)
        assertThat(result.displayName).isEqualTo(displayName)
        assertThat(result.type).isEqualTo(type)
        assertThat(result.subscriptions).isNull()
    }

    @Test
    fun `GIVEN subscription with empty subscriptions list WHEN map THEN map empty subscriptions list`() {
        // GIVEN
        val marketId = "xrp_usdt"
        val displayName = "XRP/USDT"
        val type = MarketType.SPOT

        val market = B2TraderMarket.fixture(
            marketId = MarketId(marketId),
            displayName = displayName,
            type = type,
        )

        val subscription = MarketPriceSubscription.fixture(
            marketId = marketId,
            subscriptions = emptyList(),
        )

        // WHEN
        val result = mapper.map(subscription to market)

        // THEN
        assertThat(result.marketId).isEqualTo(marketId)
        assertThat(result.displayName).isEqualTo(displayName)
        assertThat(result.type).isEqualTo(type)
        assertThat(result.subscriptions).isEmpty()
    }

    @Test
    fun `GIVEN market with null type WHEN map THEN map null type`() {
        // GIVEN
        val marketId = "ltc_usdt"
        val displayName = "LTC/USDT"
        val type: MarketType? = null

        val market = B2TraderMarket.fixture(
            marketId = MarketId(marketId),
            displayName = displayName,
            type = type,
        )

        val subscription = MarketPriceSubscription.fixture(
            marketId = marketId,
        )

        // WHEN
        val result = mapper.map(subscription to market)

        // THEN
        assertThat(result.marketId).isEqualTo(marketId)
        assertThat(result.displayName).isEqualTo(displayName)
        assertThat(result.type).isNull()
        assertThat(result.subscriptions).isNotNull()
    }

    @Test
    fun `GIVEN different market id and subscription id WHEN map THEN use market id in result`() {
        // GIVEN
        val marketId = "ada_usdt"
        val subscriptionMarketId = "different_id"
        val displayName = "ADA/USDT"
        val type = MarketType.PERPETUAL

        val market = B2TraderMarket.fixture(
            marketId = MarketId(marketId),
            displayName = displayName,
            type = type,
        )

        val subscription = MarketPriceSubscription.fixture(
            marketId = subscriptionMarketId,
        )

        // WHEN
        val result = mapper.map(subscription to market)

        // THEN
        assertThat(result.marketId).isEqualTo(marketId)
        assertThat(result.marketId).isNotEqualTo(subscriptionMarketId)
    }
}
