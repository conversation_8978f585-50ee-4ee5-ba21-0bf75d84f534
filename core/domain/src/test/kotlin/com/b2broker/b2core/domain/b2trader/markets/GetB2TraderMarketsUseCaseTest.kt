package com.b2broker.b2core.domain.b2trader.markets

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.b2trader.markets.v4.MarketsRepositoryV4
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.markets.B2TraderMarket
import com.b2broker.b2core.model.b2trader.markets.MarketId
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

class GetB2TraderMarketsUseCaseTest {

    private val marketsRepository: MarketsRepositoryV4 = mockk()
    private val getB2TraderMarketsUseCase = GetB2TraderMarketsUseCase(marketsRepository)

    @Test
    fun `GIVEN repository returns markets WHEN invoke THEN return markets`() = runTest {
        // GIVEN
        val expected = setOf(B2TraderMarket.fixture(), B2TraderMarket.fixture(marketId = MarketId("cfd.btc_usdt")))
        val accountId = "accountId"
        coEvery { marketsRepository.getMarkets(any(), accountId) } returns expected.right()

        // WHEN
        val result = getB2TraderMarketsUseCase(accountId = accountId)

        // THEN
        assertThat(result.getOrNull()).isEqualTo(expected)
    }

    @Test
    fun `GIVEN repository returns error WHEN invoke THEN return error`() = runTest {
        // GIVEN
        val error = DefaultError("Test error")
        val accountId = "accountId"
        coEvery { marketsRepository.getMarkets(any(), accountId) } returns error.left()

        // WHEN
        val result = getB2TraderMarketsUseCase(accountId = accountId)

        // THEN
        assertThat(result.leftOrNull()).isEqualTo(error)
    }
}
