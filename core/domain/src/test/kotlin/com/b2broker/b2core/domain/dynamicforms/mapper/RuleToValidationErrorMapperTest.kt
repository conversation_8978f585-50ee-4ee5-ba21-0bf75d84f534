package com.b2broker.b2core.domain.dynamicforms.mapper

import com.b2broker.b2core.model.dynamicforms.FieldValidationError
import com.b2broker.b2core.model.dynamicforms.Rule
import com.google.common.truth.Truth.assertThat
import kotlinx.collections.immutable.persistentListOf
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal

class RuleToValidationErrorMapperTest {

    private val mapper = RuleToValidationErrorMapper()

    @MethodSource("defaultErrorTestCases")
    @ParameterizedTest
    fun `GIVEN rule WHEN map THEN default error`(rule: Rule, error: FieldValidationError?) {
        // GIVEN

        // WHEN
        val actual = mapper.map(rule)

        // THEN
        assertThat(actual).isEqualTo(error)
    }

    companion object {
        private val values = listOf("v1", "v2")
        private const val joinedValues = "v1, v2"
        private const val intValue = 10
        private const val longValue = 10L
        private val decimalValue = BigDecimal.ZERO

        @JvmStatic
        fun defaultErrorTestCases(): List<Arguments> = listOf(
            Arguments.of(Rule.EqualToFieldValue(values = emptyList(), field = "field", error = null), FieldValidationError.EqualToFieldValue("field")),
            Arguments.of(Rule.Excludes(values = values, error = null), FieldValidationError.Excludes(joinedValues)),
            Arguments.of(Rule.RegEx(values = emptyList(), error = null), FieldValidationError.RegEx),
            Arguments.of(Rule.Contains(values = values, error = null), FieldValidationError.Contains(joinedValues)),
            Arguments.of(Rule.Float(error = null), FieldValidationError.Float),
            Arguments.of(Rule.Integer(error = null), FieldValidationError.Integer),
            Arguments.of(Rule.Email(error = null), FieldValidationError.Email),
            Arguments.of(Rule.MaxLength(value = intValue, error = null), FieldValidationError.MaxLength(intValue)),
            Arguments.of(Rule.MinLength(value = intValue, error = null), FieldValidationError.MinLength(intValue)),
            Arguments.of(Rule.Max(value = decimalValue, error = null), FieldValidationError.Max(decimalValue)),
            Arguments.of(Rule.Min(value = decimalValue, error = null), FieldValidationError.Min(decimalValue)),
            Arguments.of(Rule.HttpUrl(error = null), FieldValidationError.HttpUrl),
            Arguments.of(Rule.Uuid(error = null), FieldValidationError.Uuid),
            Arguments.of(Rule.Phone(error = null), FieldValidationError.Phone),
            Arguments.of(Rule.StartsWith(values = values, error = null), FieldValidationError.StartsWith(joinedValues)),
            Arguments.of(Rule.EndsWith(values = values, error = null), FieldValidationError.EndsWith(joinedValues)),
            Arguments.of(Rule.NotStartsWith(values = values, error = null), FieldValidationError.NotStartsWith(joinedValues)),
            Arguments.of(Rule.NotEndsWith(values = values, error = null), FieldValidationError.NotEndsWith(joinedValues)),
            Arguments.of(Rule.MinDate(value = longValue, error = null), FieldValidationError.MinDate(longValue)),
            Arguments.of(Rule.MaxDate(value = longValue, error = null), FieldValidationError.MaxDate(longValue)),
            Arguments.of(Rule.OneOf(rules = persistentListOf(), error = null), null),
            Arguments.of(Rule.AllOf(rules = persistentListOf(), error = null), null),
            Arguments.of(Rule.Never, null),
            Arguments.of(Rule.Unknown, null),
        )
    }
}
