package com.b2broker.b2core.domain.b2trader.markets

import com.b2broker.b2core.formatter.CurrentTimeProvider
import com.b2broker.b2core.model.b2trader.markets.details.FundingInterval
import com.google.common.truth.Truth.assertThat
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ObserveFundingCountdownUseCaseTest {

    private lateinit var currentTimeProvider: CurrentTimeProvider
    private lateinit var useCase: ObserveFundingCountdownUseCase

    @BeforeEach
    fun setup() {
        currentTimeProvider = mockk()
        useCase = ObserveFundingCountdownUseCase(currentTimeProvider)
    }

    @Test
    fun `GIVEN future funding time WHEN invoke THEN emit correct countdown`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_8

        // Current time is 11:30:00, funding is at 12:00:00, so 30 minutes remaining
        val currentTime = Instant.parse("2023-01-01T11:30:00Z")
        every { currentTimeProvider.getTime() } returns currentTime

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEqualTo("00:30:00")
    }

    @Test
    fun `GIVEN time exactly at funding point WHEN invoke THEN emit zero countdown`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_8

        // Current time is exactly at funding time
        val currentTime = Instant.parse("2023-01-01T12:00:00Z")
        every { currentTimeProvider.getTime() } returns currentTime

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEqualTo("00:00:00")
    }

    @Test
    fun `GIVEN past funding time WHEN invoke THEN calculate next interval time`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_8

        // Current time is 13:00:00, past the funding time, so next is at 20:00:00 (8 hours later)
        val currentTime = Instant.parse("2023-01-01T13:00:00Z")
        every { currentTimeProvider.getTime() } returns currentTime

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEqualTo("07:00:00") // 7 hours until next funding
    }

    @Test
    fun `GIVEN midnight funding time WHEN invoke THEN calculate correctly`() = runTest {
        // GIVEN
        val fundingStartTime = "00:00:00"
        val fundingInterval = FundingInterval.HOURS_24

        // Current time is 23:30:00, funding is at midnight, so 30 minutes remaining
        val currentTime = Instant.parse("2023-01-01T23:30:00Z")
        every { currentTimeProvider.getTime() } returns currentTime

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEqualTo("00:30:00")
    }

    @Test
    fun `GIVEN exception occurs WHEN invoke THEN emit empty string and log error`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_8

        every { currentTimeProvider.getTime() } throws RuntimeException("Test exception")

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `GIVEN time changes while collecting WHEN invoke THEN update countdown`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_1

        // Set up a sequence of times for testing
        val initialTime = Instant.parse("2023-01-01T11:59:00Z")
        val updatedTime = Instant.parse("2023-01-01T11:59:30Z")

        // Return different times in sequence
        every { currentTimeProvider.getTime() } returnsMany listOf(
            initialTime,
            initialTime,
            updatedTime,
            updatedTime
        )

        // WHEN
        val results = useCase(fundingStartTime, fundingInterval).take(2).toList()

        // THEN
        assertThat(results).hasSize(2)
        assertThat(results[0]).isEqualTo("00:01:00") // 1 minute remaining
        assertThat(results[1]).isEqualTo("00:00:30") // 30 seconds remaining
    }

    @Test
    fun `GIVEN countdown reaches zero WHEN invoke THEN emit zero time`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_1

        // First return time just before funding, then at funding time, then after
        val beforeTime = Instant.parse("2023-01-01T11:59:59Z")
        val atTime = Instant.parse("2023-01-01T12:00:00Z")
        val afterTime = Instant.parse("2023-01-01T12:00:01Z")

        // Make each time return twice to ensure stable emissions
        every { currentTimeProvider.getTime() } returnsMany listOf(
            beforeTime, beforeTime,
            atTime, atTime,
            afterTime, afterTime,
        )

        // WHEN
        val results = useCase(fundingStartTime, fundingInterval).take(3).toList()

        // THEN
        assertThat(results).hasSize(3)
        assertThat(results[0]).isEqualTo("00:00:01") // 1 second remaining
        assertThat(results[1]).isEqualTo("00:00:00") // At funding time
        assertThat(results[2]).isEqualTo("00:59:59") // Next interval begins
    }

    @Test
    fun `GIVEN multiple emissions WHEN collecting flow THEN emit continuously`() = runTest {
        // GIVEN
        val fundingStartTime = "12:00:00"
        val fundingInterval = FundingInterval.HOURS_1
        val fixedTime = Instant.parse("2023-01-01T11:00:00Z")

        every { currentTimeProvider.getTime() } returns fixedTime

        // WHEN
        val results = useCase(fundingStartTime, fundingInterval).take(3).toList()

        // THEN
        assertThat(results).hasSize(3)
        assertThat(results).containsExactly("01:00:00", "01:00:00", "01:00:00")
        verify(atLeast = 3) { currentTimeProvider.getTime() }
    }

    @ParameterizedTest
    @EnumSource(FundingInterval::class)
    fun `GIVEN interval WHEN invoke THEN calculate correctly for each interval`(interval: FundingInterval) =
        runTest {
            // GIVEN
            val fundingStartTime = "12:00:00"
            val currentTime = Instant.parse("2023-01-01T13:30:00Z")
            every { currentTimeProvider.getTime() } returns currentTime

            // WHEN
            val result = useCase(fundingStartTime, interval).first()

            // THEN
            val expected = when (interval) {
                FundingInterval.HOURS_1 -> "00:30:00"
                FundingInterval.HOURS_2 -> "00:30:00"
                FundingInterval.HOURS_3 -> "01:30:00"
                FundingInterval.HOURS_4 -> "02:30:00"
                FundingInterval.HOURS_6 -> "04:30:00"
                FundingInterval.HOURS_8 -> "06:30:00"
                FundingInterval.HOURS_12 -> "10:30:00"
                FundingInterval.HOURS_24 -> "22:30:00"
            }

            assertThat(result).isEqualTo(expected)
        }

    @Test
    fun `GIVEN invalid time format WHEN invoke THEN handle exception gracefully`() = runTest {
        // GIVEN
        val fundingStartTime = "invalid-time"
        val fundingInterval = FundingInterval.HOURS_8
        val currentTime = Instant.parse("2023-01-01T12:00:00Z")
        every { currentTimeProvider.getTime() } returns currentTime

        // WHEN
        val result = useCase(fundingStartTime, fundingInterval).first()

        // THEN
        assertThat(result).isEmpty()
    }
}
