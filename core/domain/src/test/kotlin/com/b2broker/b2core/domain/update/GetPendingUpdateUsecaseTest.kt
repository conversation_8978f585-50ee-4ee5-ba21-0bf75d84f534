package com.b2broker.b2core.domain.update

import arrow.core.right
import com.b2broker.b2core.data.update.UpdateRepository
import com.b2broker.b2core.model.buildfields.BuildProperties
import com.b2broker.b2core.model.update.Release
import com.b2broker.b2core.model.update.Update
import com.b2broker.b2core.test.utils.capturedOrNull
import com.b2broker.b2core.test.utils.setOrClear
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Named
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

class GetPendingUpdateUsecaseTest {

    private val getLatestUpdateUsecase: GetLatestValidUpdateOrNullUsecase = mockk()
    private val setCurrentUpdateUsecase: SetCurrentUpdateUsecase = mockk()
    private val updateRepository: UpdateRepository = mockk()
    private val buildProperties: BuildProperties = BuildProperties(
        applicationName = "test_app_name",
        applicationId = "test_app_id",
        versionName = "test_app_version_name",
        versionCode = 0,
    )

    private val usecase: GetPendingUpdateUsecase = GetPendingUpdateUsecase(
        getLatestValidUpdateOrNullUsecase = getLatestUpdateUsecase,
        setCurrentUpdateUsecase = setCurrentUpdateUsecase,
        updateRepository = updateRepository,
        buildProperties = buildProperties,
    )

    @ParameterizedTest(name = "GIVEN latestUpdate = {0}, currentUpdate = {1}, countdown = {2} THEN returns expected = {3}, expectedCurrentUpdate = {4}, expectedCountdown = {5}")
    @MethodSource("provideTestParams")
    fun `WHEN invoke`(
        latestUpdate: Update?,
        currentUpdate: Update?,
        countdown: Int?,
        expected: Update?,
        expectedCurrentUpdate: Update?,
        expectedCountdown: Int?
    ) = runTest {
        // GIVEN
        val currentUpdateSlot = slot<Update?>()
        currentUpdateSlot.captured = currentUpdate
        val countdownSlot = slot<Int>()
        countdownSlot.setOrClear(countdown)

        coEvery { getLatestUpdateUsecase() } returns latestUpdate
        coEvery { updateRepository.getCurrentUpdate() } answers {
            currentUpdateSlot.capturedOrNull().right()
        }
        coEvery { setCurrentUpdateUsecase(captureNullable(currentUpdateSlot)) } returns Unit.right()
        coEvery { updateRepository.getUpdateAlertCountdown() } answers {
            countdownSlot.capturedOrNull().right()
        }
        coEvery { updateRepository.setUpdateAlertCountdown(capture(countdownSlot)) } returns Unit.right()

        // WHEN
        val actual = usecase()

        // THEN
        assertThat(actual).isEqualTo(expected.right())
        assertThat(currentUpdateSlot.captured).isEqualTo(expectedCurrentUpdate)
        assertThat(countdownSlot.capturedOrNull()).isEqualTo(expectedCountdown)
    }

    companion object {
        private val update1000 = Named.named("Update1000", Update.fixture(release = Release.fixture(newVersionCode = 1000)))
        private val update1000Modified = Named.named("Update1000Modified", Update.fixture(alertLoginCounter = 15, release = Release.fixture(newVersionCode = 1000)))
        private val update1001 = Named.named("Update1001", Update.fixture(release = Release.fixture(newVersionCode = 1001)))

        private fun testParam(
            latestUpdate: Named<Update>?,
            currentUpdate: Named<Update>?,
            countdown: Int?,
            expected: Named<Update>?,
            expectedCurrentUpdate: Named<Update>?,
            expectedCountdown: Int?,
        ) = arguments(
            latestUpdate,
            currentUpdate,
            countdown,
            expected,
            expectedCurrentUpdate,
            expectedCountdown,
        )

        @JvmStatic
        private fun provideTestParams(): Stream<Any> = Stream.of(
            testParam(
                latestUpdate = null,
                currentUpdate = null,
                countdown = null,
                expected = null,
                expectedCurrentUpdate = null,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = update1000,
                currentUpdate = update1000,
                countdown = null,
                expected = update1000,
                expectedCurrentUpdate = update1000,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = update1000Modified,
                currentUpdate = update1000,
                countdown = null,
                expected = update1000Modified,
                expectedCurrentUpdate = update1000Modified,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = update1000,
                currentUpdate = null,
                countdown = null,
                expected = update1000,
                expectedCurrentUpdate = update1000,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = null,
                currentUpdate = update1000,
                countdown = null,
                expected = update1000,
                expectedCurrentUpdate = update1000,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = null,
                currentUpdate = update1000,
                countdown = 2,
                expected = null,
                expectedCurrentUpdate = update1000,
                expectedCountdown = 1,
            ),
            testParam(
                latestUpdate = null,
                currentUpdate = update1000,
                countdown = 0,
                expected = update1000,
                expectedCurrentUpdate = update1000,
                expectedCountdown = 0,
            ),
            testParam(
                latestUpdate = update1000,
                currentUpdate = update1001,
                countdown = null,
                expected = update1001,
                expectedCurrentUpdate = update1001,
                expectedCountdown = null,
            ),
            testParam(
                latestUpdate = update1001,
                currentUpdate = update1000,
                countdown = null,
                expected = update1001,
                expectedCurrentUpdate = update1001,
                expectedCountdown = null,
            ),
        )
    }
}
