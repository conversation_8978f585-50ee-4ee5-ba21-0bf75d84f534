package com.b2broker.b2core.domain.verification

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.verification.VerificationRepository
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.verification.VerificationWizardStep
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest

class StartVerificationWizardUseCaseTest {

    private val repository = mockk<VerificationRepository>()
    private val useCase = StartVerificationWizardUseCase(repository)

    @org.junit.jupiter.api.Test
    fun `GIVEN repository error WHEN invoke THEN return error`() = runTest {
        // GIVEN
        val expected = DefaultError("oh no")
        coEvery { repository.startWizard() } returns expected.left()

        // WHEN
        val actual = useCase.invoke().leftOrNull()

        // THEN
        assertThat(actual).isEqualTo(expected)
    }

    @org.junit.jupiter.api.Test
    fun `GIVEN start time, repository data WHEN invoke THEN return data`() = runTest {
        // GIVEN
        val expected = VerificationWizardStep.Unknown
        coEvery { repository.startWizard() } returns expected.right()

        // WHEN
        val actual = useCase.invoke().getOrNull()

        // THEN
        assertThat(actual).isEqualTo(expected)
    }
}
