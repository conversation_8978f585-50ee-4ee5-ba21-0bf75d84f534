package com.b2broker.b2core.domain.update

import com.b2broker.b2core.data.update.UpdateRepository
import com.b2broker.b2core.model.update.DownloadId
import com.b2broker.b2core.model.update.DownloadStatus
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

class GetDownloadStatusUseCaseTest {
    private val updateRepository: UpdateRepository = mockk()

    private val useCase = GetDownloadStatusUseCase(updateRepository)

    @Test
    fun `WHEN invoke THEN correct download status returned`() = runTest {
        // GIVEN
        val downloadId = DownloadId.fixture()
        val downloadStatus = DownloadStatus.Successful
        coEvery { updateRepository.getStatus(downloadId) } returns downloadStatus

        // WHEN
        val actual = useCase(downloadId)

        // THEN
        assertThat(actual).isEqualTo(downloadStatus)
    }
}
