package com.b2broker.b2core.domain.b2trader.markets

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.domain.b2trader.markets.mapper.ExtendedB2TraderMarketDetailsMapper
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.markets.MarketId
import com.b2broker.b2core.model.b2trader.markets.details.B2TraderMarketDetails
import com.b2broker.b2core.model.b2trader.markets.details.ExtendedB2TraderAccountMarketDetails
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ObserveMarketDetailsByIdUseCaseTest {

    private val getB2TraderMarketDetailsUseCase: GetB2TraderMarketDetailsUseCase = mockk()
    private val checkTradingAvailabilityUseCase: CheckTradingAvailabilityUseCase = mockk()
    private val extendedB2TraderMarketDetailsMapper: ExtendedB2TraderMarketDetailsMapper = mockk()
    private lateinit var useCase: ObserveMarketDetailsByIdUseCase

    @BeforeEach
    fun setUp() {
        useCase = ObserveMarketDetailsByIdUseCase(
            getB2TraderMarketDetailsUseCase = getB2TraderMarketDetailsUseCase,
            checkTradingAvailabilityUseCase = checkTradingAvailabilityUseCase,
            extendedB2TraderMarketDetailsMapper = extendedB2TraderMarketDetailsMapper,
            dispatcher = UnconfinedTestDispatcher(),
        )
    }

    @Test
    fun `GIVEN successful market details and trading availability WHEN invoke THEN returns mapped details`() = runTest {
        // GIVEN
        val marketDetails = B2TraderMarketDetails.fixture()
        val extendedDetails = ExtendedB2TraderAccountMarketDetails.fixture(accountId = ACCOUNT_ID)
        coEvery { getB2TraderMarketDetailsUseCase(MarketId(MARKET_ID), ACCOUNT_ID) } returns marketDetails.right()
        every { checkTradingAvailabilityUseCase(marketDetails.calendar) } returns flowOf(true)
        every { extendedB2TraderMarketDetailsMapper.map(marketDetails, ACCOUNT_ID, true) } returns extendedDetails

        // WHEN
        val flow = useCase(ACCOUNT_ID, MARKET_ID)

        // THEN
        assertThat(flow.toList()).containsExactly(extendedDetails.right())
    }

    @Test
    fun `GIVEN error from market details use case WHEN invoke THEN returns error`() = runTest {
        // GIVEN
        val error = DefaultError("Market details error")
        coEvery { getB2TraderMarketDetailsUseCase(MarketId(MARKET_ID), ACCOUNT_ID) } returns error.left()

        // WHEN
        val flow = useCase(ACCOUNT_ID, MARKET_ID)

        // THEN
        assertThat(flow.toList()).containsExactly(error.left())
    }

    companion object {
        const val ACCOUNT_ID = "test-account"
        const val MARKET_ID = "test-market"
    }
}
