package com.b2broker.b2core.domain.b2trader.orders

import com.b2broker.b2core.model.b2trader.orders.OrderInputValidationResult
import com.b2broker.b2core.model.b2trader.orders.error.PlaceOrderValidationError.ParseError
import com.google.common.truth.Truth.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.Locale

class ValidateOrderInputUseCaseTest {

    private val useCase = ValidateOrderInputUseCase()

    @Test
    fun `GIVEN null value WHEN invoke THEN return Empty result`() {
        // WHEN
        val result = useCase(null, 2)

        // THEN
        assertThat(result).isInstanceOf(OrderInputValidationResult.Empty::class.java)
    }

    @Test
    fun `GIVEN blank value WHEN invoke THEN return Empty result`() {
        // WHEN
        val result = useCase("", 2)

        // THEN
        assertThat(result).isInstanceOf(OrderInputValidationResult.Empty::class.java)
    }

    @Test
    fun `GIVEN decimal value with scale 0 WHEN invoke THEN return Error result`() {
        // WHEN
        val result = useCase("10.5", 0)

        // THEN
        assertThat(result).isInstanceOf(OrderInputValidationResult.Error::class.java)
        assertThat((result as OrderInputValidationResult.Error).error).isEqualTo(ParseError)
    }

    @Test
    fun `GIVEN valid numeric value WHEN invoke THEN return Valid result`() {
        // WHEN
        val result = useCase("10.5", 2)

        // THEN
        assertThat(result).isInstanceOf(OrderInputValidationResult.Valid::class.java)
        assertThat((result as OrderInputValidationResult.Valid).value).isEqualTo(BigDecimal("10.5"))
    }

    @Test
    fun `GIVEN invalid numeric value WHEN invoke THEN return Error result`() {
        // WHEN
        val result = useCase("abc", 2)

        // THEN
        assertThat(result).isInstanceOf(OrderInputValidationResult.Error::class.java)
        assertThat((result as OrderInputValidationResult.Error).error).isEqualTo(ParseError)
    }

    @Test
    fun `GIVEN ru locale with valid decimal separator WHEN invoke THEN return Valid result`() {
        // GIVEN
        val defaultLocale = Locale.getDefault()
        Locale.setDefault(Locale("ru"))

        // WHEN
        val result = useCase("10,5", 2)

        // THEN
        Locale.setDefault(defaultLocale)
        assertThat(result).isInstanceOf(OrderInputValidationResult.Valid::class.java)
        assertThat((result as OrderInputValidationResult.Valid).value).isEqualTo(BigDecimal("10.5"))
    }
}
