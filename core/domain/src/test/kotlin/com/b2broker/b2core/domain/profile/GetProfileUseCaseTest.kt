package com.b2broker.b2core.domain.profile

import arrow.core.right
import com.b2broker.b2core.data.profile.ProfileRepository
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.profile.Profile
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

internal class GetProfileUseCaseTest {

    private val profileAvatarRepository = mockk<ProfileRepository>()
    private val getProfileUseCase = GetProfileUseCase(profileAvatarRepository)

    @Test
    fun `GIVEN repository returns Profile WHEN invoke THEN return Profile`() = runTest {
        // GIVEN
        val profileAvatar = Profile.fixture()
        coEvery { profileAvatarRepository.getProfile(any()) } returns profileAvatar.right()

        // WHEN
        val actual = getProfileUseCase(CachePolicy.CACHE_FIRST).getOrNull()

        // THEN
        assertThat(actual).isEqualTo(profileAvatar)
    }
}
