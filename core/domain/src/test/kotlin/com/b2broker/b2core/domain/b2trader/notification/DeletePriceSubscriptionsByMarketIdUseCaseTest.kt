package com.b2broker.b2core.domain.b2trader.notification

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.b2trader.notification.NotificationRepository
import com.b2broker.b2core.model.DefaultError
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DeletePriceSubscriptionsByMarketIdUseCaseTest {

    private lateinit var repository: NotificationRepository
    private lateinit var useCase: DeletePriceSubscriptionsByMarketIdUseCase

    private val accountId = "test-account-id"
    private val marketId = "BTC_USD"

    @BeforeEach
    fun setUp() {
        repository = mockk()
        useCase = DeletePriceSubscriptionsByMarketIdUseCase(repository)
    }

    @Test
    fun `GIVEN repository returns success WHEN invoke THEN returns success`() = runTest {
        // GIVEN
        coEvery {
            repository.deleteSubscriptionMarketById(
                accountId = accountId,
                marketId = marketId,
            )
        } returns Unit.right()

        // WHEN
        val result = useCase(
            accountId = accountId,
            marketId = marketId,
        )

        // THEN
        assertThat(result.isRight()).isTrue()
        assertThat(result.getOrNull()).isEqualTo(Unit)

        coVerify {
            repository.deleteSubscriptionMarketById(
                accountId = accountId,
                marketId = marketId,
            )
        }
    }

    @Test
    fun `GIVEN repository returns error WHEN invoke THEN returns error`() = runTest {
        // GIVEN
        val error = DefaultError("Failed to delete subscription market")
        coEvery {
            repository.deleteSubscriptionMarketById(
                accountId = accountId,
                marketId = marketId,
            )
        } returns error.left()

        // WHEN
        val result = useCase(
            accountId = accountId,
            marketId = marketId,
        )

        // THEN
        assertThat(result.isLeft()).isTrue()
        assertThat(result.leftOrNull()).isEqualTo(error)
    }

    @Test
    fun `GIVEN different parameters WHEN invoke THEN passes parameters to repository`() = runTest {
        // GIVEN
        val differentAccountId = "different-account"
        val differentMarketId = "ETH_USD"

        coEvery {
            repository.deleteSubscriptionMarketById(
                accountId = differentAccountId,
                marketId = differentMarketId,
            )
        } returns Unit.right()

        // WHEN
        val result = useCase(
            accountId = differentAccountId,
            marketId = differentMarketId,
        )

        // THEN
        assertThat(result.isRight()).isTrue()
        assertThat(result.getOrNull()).isEqualTo(Unit)

        coVerify {
            repository.deleteSubscriptionMarketById(
                accountId = differentAccountId,
                marketId = differentMarketId,
            )
        }
    }
}
