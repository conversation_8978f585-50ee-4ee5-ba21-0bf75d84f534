package com.b2broker.b2core.domain.b2trader.orders

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.b2trader.accounts.B2TraderAccountRepository
import com.b2broker.b2core.data.b2trader.orders.cfd.CfdOrdersRepository
import com.b2broker.b2core.model.b2trader.B2TraderWebSocketError
import com.b2broker.b2core.model.b2trader.orders.B2TraderOrderV4
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

class ObserveB2TraderCompletedOrdersUseCaseTest {

    private lateinit var useCase: ObserveB2TraderCompletedOrdersUseCase
    private lateinit var accountRepository: B2TraderAccountRepository
    private lateinit var cfdOrdersRepository: CfdOrdersRepository
    private val accountId = UUID.randomUUID().toString()

    @BeforeEach
    fun setUp() {
        accountRepository = mockk()
        cfdOrdersRepository = mockk()
        useCase = ObserveB2TraderCompletedOrdersUseCase(
            accountRepository = accountRepository,
            cfdOrdersRepository = cfdOrdersRepository,
        )
    }

    @Test
    fun `GIVEN accountId WHEN invoke THEN returns completed orders`() = runTest {
        // GIVEN
        val order = B2TraderOrderV4.fixture()

        every { accountRepository.observeCurrentAccountId() } returns flowOf(accountId)
        coEvery { cfdOrdersRepository.observeCompletedOrders(accountId) } returns flowOf(order.right())

        // WHEN
        val result = useCase().first()

        // THEN
        assertThat(result).isEqualTo(order)
    }

    @Test
    fun `GIVEN null accountId WHEN invoke THEN returns empty flow`() = runTest {
        // GIVEN
        every { accountRepository.observeCurrentAccountId() } returns flowOf(null)

        // WHEN
        val result = useCase().toList()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `GIVEN repository error WHEN invoke THEN returns empty flow`() = runTest {
        // GIVEN
        every { accountRepository.observeCurrentAccountId() } returns flowOf(accountId)
        coEvery { cfdOrdersRepository.observeCompletedOrders(accountId) } returns flowOf(B2TraderWebSocketError.UnknownError().left())

        // WHEN
        val result = useCase().toList()

        // THEN
        assertThat(result).isEmpty()
    }
}
