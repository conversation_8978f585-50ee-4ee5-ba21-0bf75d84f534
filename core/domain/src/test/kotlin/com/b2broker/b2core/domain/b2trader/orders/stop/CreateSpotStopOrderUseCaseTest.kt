package com.b2broker.b2core.domain.b2trader.orders.stop

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.data.b2trader.orders.stop.spot.SpotStopOrdersRepository
import com.b2broker.b2core.domain.b2trader.orders.stop.mapper.CreateSpotStopOrderMapper
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.orders.CreateOrderParams
import com.b2broker.b2core.model.b2trader.orders.stop.spot.CreateSpotStopOrder
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CreateSpotStopOrderUseCaseTest {

    private val spotStopOrdersRepository = mockk<SpotStopOrdersRepository>()
    private val createSpotStopOrderMapper = mockk<CreateSpotStopOrderMapper>()
    private lateinit var useCase: CreateSpotStopOrderUseCase

    @BeforeEach
    fun setUp() {
        useCase = CreateSpotStopOrderUseCase(
            spotStopOrdersRepository = spotStopOrdersRepository,
            createSpotStopOrderMapper = createSpotStopOrderMapper,
        )
    }

    @Test
    fun `GIVEN valid params WHEN invoke THEN return StopOrder`() = runTest {
        // GIVEN
        val params = CreateOrderParams.CreateSpotStopOrderParams.fixture()
        val createSpotStopOrder = CreateSpotStopOrder.fixture()

        every { createSpotStopOrderMapper.map(params) } returns createSpotStopOrder
        coEvery { spotStopOrdersRepository.createSpotStopOrder(params.accountId, createSpotStopOrder) } returns Unit.right()

        // WHEN
        val result = useCase(params)

        // THEN
        assertThat(result.getOrNull()).isEqualTo(Unit)
    }

    @Test
    fun `GIVEN repository error WHEN invoke THEN return error`() = runTest {
        // GIVEN
        val params = CreateOrderParams.CreateSpotStopOrderParams.fixture()
        val createSpotStopOrder = CreateSpotStopOrder.fixture()
        val error = DefaultError("Test error")

        every { createSpotStopOrderMapper.map(params) } returns createSpotStopOrder
        coEvery { spotStopOrdersRepository.createSpotStopOrder(params.accountId, createSpotStopOrder) } returns error.left()

        // WHEN
        val result = useCase(params)

        // THEN
        assertThat(result.leftOrNull()).isEqualTo(error)
    }
}
