package com.b2broker.b2core.domain.b2trader.permission

import com.b2broker.b2core.domain.platforms.accounts.GetAccountsByPlatformTypeUseCase
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.b2trader.TopUpBalanceParams
import com.b2broker.b2core.model.permissions.StatePermission
import com.b2broker.b2core.model.platforms.PlatformType
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.Locale
import javax.inject.Inject

class GetTopUpBalanceParamsUseCase @Inject constructor(
    private val getB2TraderDepositPermissionUseCase: GetB2TraderDepositPermissionUseCase,
    private val getB2TraderTransferPermissionUseCase: GetB2TraderTransferPermissionUseCase,
    private val getAccountsByPlatformTypeUseCase: GetAccountsByPlatformTypeUseCase,
) {

    suspend operator fun invoke(accountId: String?, quoteAsset: String): TopUpBalanceParams = coroutineScope {
        val hasDepositPermissionDeferred = async { getB2TraderDepositPermissionUseCase(CachePolicy.CACHE_FIRST).getOrNull() }
        val hasTransferPermissionDeferred = async { getB2TraderTransferPermissionUseCase(CachePolicy.CACHE_FIRST).getOrNull() }

        val selectedMarketQuoteAssetAccountIdDeferred = async {
            getAccountsByPlatformTypeUseCase(PlatformType.B2TraderBrokeragePlatform, CachePolicy.CACHE_FIRST)
                .getOrNull()
                ?.firstOrNull { account ->
                    val accountAssetId = account.currency.alphabeticCode
                    account.number == accountId && accountAssetId.lowercase(Locale.US) == quoteAsset.lowercase(Locale.US)
                }
                ?.id
                .orEmpty()
        }

        TopUpBalanceParams(
            hasDepositPermission = hasDepositPermissionDeferred.await() == StatePermission.Enabled,
            hasTransferPermission = hasTransferPermissionDeferred.await() == StatePermission.Enabled,
            selectedMarketQuoteAssetAccountId = selectedMarketQuoteAssetAccountIdDeferred.await(),
        )
    }
}
