package com.b2broker.b2core.domain.update

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.data.update.UpdateRepository
import com.b2broker.b2core.model.DefaultError
import javax.inject.Inject

class CancelCurrentDownloadUseCase
@Inject constructor(
    private val updateRepository: UpdateRepository
) {

    suspend operator fun invoke(): Either<DefaultError, Unit> = either {
        val downloadId = updateRepository.getCurrentDownloadId().bind()
        if (downloadId != null) {
            updateRepository.cancelDownload(downloadId).bind()
            updateRepository.setCurrentDownloadId(null).bind()
        }
    }
}
