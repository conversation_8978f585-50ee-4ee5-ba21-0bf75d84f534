package com.b2broker.b2core.domain.platforms

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.data.platforms.PlatformsRepository
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.platforms.PlatformType
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetTradeUrlUseCase @Inject internal constructor(
    private val repository: PlatformsRepository,
    @DefaultDispatcher private val dispatcher: CoroutineDispatcher,
) {
    suspend operator fun invoke(
        platformType: PlatformType,
        isDemo: Boolean,
        cachePolicy: CachePolicy = CachePolicy.CACHE_FIRST
    ): Either<DefaultError, String> = either {
        withContext(dispatcher) {
            val platforms = repository.getPlatforms(cachePolicy).bind()
            platforms.firstOrNull { platform ->
                platform.type == platformType && platform.isDemo == isDemo && !platform.tradeUrl.isNullOrBlank()
            }?.tradeUrl
                ?: raise(
                    DefaultError(
                        TextResource(
                            CoreStrings.platforms_match_error_format,
                            listOf(platformType)
                        )
                    )
                )
        }
    }
}
