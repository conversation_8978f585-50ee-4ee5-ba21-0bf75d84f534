package com.b2broker.b2core.domain.ibprogram

import arrow.core.Either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.data.ibprogram.IbProgramRepository
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.ibprogram.IbPayment
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetIbPaymentsUseCase @Inject constructor(
    private val repository: IbProgramRepository,
    @DefaultDispatcher private val dispatcher: CoroutineDispatcher,
) {
    suspend operator fun invoke(
        programId: String,
        limit: Int,
        offset: Int
    ): Either<DefaultError, List<IbPayment>> = withContext(dispatcher) {
        repository.getPayments(
            programId = programId,
            limit = limit,
            offset = offset,
        )
    }
}
