package com.b2broker.b2core.domain.dynamicforms

import com.b2broker.b2core.model.dynamicforms.RuleValidationResult
import com.b2broker.b2core.model.dynamicforms.state.FieldError
import com.b2broker.b2core.model.dynamicforms.state.FieldState
import com.b2broker.b2core.model.dynamicforms.state.OptionState
import com.b2broker.b2core.model.dynamicforms.state.SelectedOption
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.PersistentMap
import kotlinx.collections.immutable.toImmutableList
import javax.inject.Inject

class ValidateFieldUseCase @Inject internal constructor(
    private val validateRulesUseCase: ValidateRulesUseCase,
    private val updateCacheUseCase: UpdateCacheUseCase,
    private val getFieldValueUseCase: GetFieldValueUseCase,
) {
    suspend operator fun invoke(
        field: FieldState,
        cache: PersistentMap<String, ImmutableList<String>>
    ): FieldState {
        val updatedField = updateOptionsState(field, cache)
        val updatedCache = updateCacheUseCase(cache, updatedField)
        val value = getFieldValueUseCase(updatedField)
        val isVisible = validateRulesUseCase(field.displayRules, value, updatedCache).toBoolean()
        val isMandatory = validateRulesUseCase(field.mandatoryRules, value, updatedCache).toBoolean()
        val error = fieldError(
            field = updatedField,
            isMandatory = isMandatory,
            value = value,
            valuesCache = updatedCache,
        )

        return when (updatedField) {
            is FieldState.ReadonlyFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                )

            is FieldState.InputFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )

            is FieldState.SecretFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )

            is FieldState.SelectFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )

            is FieldState.MultiFieldState -> {
                val preparedError = error ?: multiSelectError(updatedField)
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = preparedError,
                )
            }

            is FieldState.PhoneFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )

            is FieldState.CheckboxFieldState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )

            is FieldState.DateTimeState ->
                updatedField.copy(
                    isVisible = isVisible,
                    isMandatory = isMandatory,
                    error = error,
                )
        }
    }

    private suspend fun updateOptionsState(field: FieldState, valuesCache: PersistentMap<String, ImmutableList<String>>): FieldState {
        @Suppress("ElseCaseInsteadOfExhaustiveWhen")
        return when (field) {
            is FieldState.SelectFieldState ->
                updateSelectState(
                    field = field,
                    valuesCache = valuesCache,
                )
            is FieldState.MultiFieldState ->
                updateMultiSelectState(
                    field = field,
                    valuesCache = valuesCache,
                )
            else -> field
        }
    }

    private fun multiSelectError(field: FieldState.MultiFieldState): FieldError? {
        val selectedCount = field.options.count { it.isSelected && it.isVisible }
        val min = field.minOptions
        val max = field.maxOptions
        return when {
            min != null && selectedCount < min -> FieldError.MultiSelectMin(min)
            max != null && selectedCount > max -> FieldError.MultiSelectMax(max)
            else -> null
        }
    }

    private suspend fun isFieldEmpty(field: FieldState): Boolean {
        return if (field is FieldState.PhoneFieldState) {
            field.phoneWithoutCode.isBlank()
        } else {
            getFieldValueUseCase(field).let { value ->
                value.isEmpty() || value.first().isEmpty()
            }
        }
    }

    private suspend fun fieldError(
        field: FieldState,
        isMandatory: Boolean,
        value: List<String>,
        valuesCache: Map<String, List<String>>,
    ): FieldError? {
        return if (isFieldEmpty(field)) {
            if (isMandatory) {
                FieldError.Required
            } else {
                null
            }
        } else {
            val errorResult = validateRulesUseCase(field.validationRules, value, valuesCache)
            when (errorResult) {
                is RuleValidationResult.Error -> FieldError.ValidationError(errorResult.error)
                is RuleValidationResult.Valid -> null
            }
        }
    }

    private suspend fun updateSelectState(
        field: FieldState.SelectFieldState,
        valuesCache: Map<String, List<String>>,
    ): FieldState.SelectFieldState {
        val options = field.options.map { option ->
            option.copy(
                isVisible = validateRulesUseCase(option.displayRules, getFieldValueUseCase(field), valuesCache).toBoolean(),
            )
        }

        val selectedOption = field.selectedOption?.let { selectedOption ->
            options.find { it.value == selectedOption.value }
                ?.takeIf { it.isVisible }
                ?.let(::toSelectedOption)
        }

        return field.copy(
            options = options.toImmutableList(),
            selectedOption = selectedOption,
        )
    }

    private suspend fun updateMultiSelectState(
        field: FieldState.MultiFieldState,
        valuesCache: PersistentMap<String, ImmutableList<String>>,
    ): FieldState.MultiFieldState {
        val options = field.options.map { option ->
            val isVisible = validateRulesUseCase(option.displayRules, getFieldValueUseCase(field), valuesCache).toBoolean()
            option.copy(
                isVisible = isVisible,
                isSelected = isVisible && option.isSelected,
            )
        }.toImmutableList()

        val selectedOptions = options.filter { it.isSelected }
        val selectedText = selectedOptions.joinToString(", ") { it.label }
        val selectedCount = selectedOptions.size

        return field.copy(
            options = options,
            selectedText = selectedText,
            selectedCount = selectedCount,
        )
    }

    private fun toSelectedOption(optionState: OptionState): SelectedOption {
        return SelectedOption(
            name = optionState.name,
            label = optionState.label,
            value = optionState.value,
        )
    }
}
