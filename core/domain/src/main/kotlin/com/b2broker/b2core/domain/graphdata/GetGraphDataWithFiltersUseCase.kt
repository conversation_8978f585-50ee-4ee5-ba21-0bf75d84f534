package com.b2broker.b2core.domain.graphdata

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.formatter.CurrentTimeProvider
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.graphdata.GraphDataFilter
import com.b2broker.b2core.model.graphdata.TimePeriodGraphData
import kotlinx.collections.immutable.toImmutableMap
import javax.inject.Inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class GetGraphDataWithFiltersUseCase @Inject constructor(
    private val getGraphDataUseCase: GetGraphDataUseCase,
    private val currentTimeProvider: CurrentTimeProvider,
) {

    suspend operator fun invoke(
        accountId: String,
        filter: GraphDataFilter
    ): Either<DefaultError, TimePeriodGraphData> = either {
        val currentTime = currentTimeProvider.getTime()
        val startDate = when (filter) {
            GraphDataFilter.Day -> currentTime.minus(DAY_DURATION)
            GraphDataFilter.Week -> currentTime.minus(WEEK_DURATION)
            GraphDataFilter.Month -> currentTime.minus(MONTH_DURATION)
            GraphDataFilter.ThreeMonth -> currentTime.minus(THREE_MONTH_DURATION)
            is GraphDataFilter.Custom -> filter.startDateUtc
        }
        val endDate = if (filter is GraphDataFilter.Custom) filter.endDateUtc else currentTime
        val graphDataPage = getGraphDataUseCase(
            startDateTime = startDate,
            accountId = accountId,
        ).bind().firstOrNull()

        val data = graphDataPage?.let { page ->
            if (filter is GraphDataFilter.Custom) {
                page.data
                    .filter { it.key <= filter.endDateUtc.epochSeconds }
                    .toImmutableMap()
            } else {
                page.data
            }
        }.orEmpty()

        TimePeriodGraphData(
            startDateUtc = startDate,
            endDateUtc = endDate,
            data = data.toImmutableMap(),
        )
    }

    companion object {
        private val DAY_DURATION = 1.toDuration(DurationUnit.DAYS)
        private val WEEK_DURATION = 7.toDuration(DurationUnit.DAYS)
        private val MONTH_DURATION = 30.toDuration(DurationUnit.DAYS)
        private val THREE_MONTH_DURATION = 90.toDuration(DurationUnit.DAYS)
    }
}
