package com.b2broker.b2core.domain.accountsecurity.whitelist

import arrow.core.Either
import com.b2broker.b2core.data.accountsecurity.AccountSecurityRepository
import com.b2broker.b2core.model.accounts.WizardWorkflow
import com.b2broker.b2core.model.funds.FieldValidationError
import javax.inject.Inject

class SubmitAddWhitelistInputUseCase @Inject constructor(
    private val accountSecurityRepository: AccountSecurityRepository,
) {

    suspend operator fun invoke(
        uuid: String,
        currencyCode: Int?,
        walletAddress: String,
        destinationTag: String? = null,
        noDestinationTag: Boolean? = null,
    ): Either<FieldValidationError, WizardWorkflow> {
        return accountSecurityRepository.submitAddWhitelistInput(
            uuid = uuid,
            currencyCode = currencyCode,
            walletAddress = walletAddress,
            destinationTag = destinationTag,
            noDestinationTag = noDestinationTag,
        )
    }
}
