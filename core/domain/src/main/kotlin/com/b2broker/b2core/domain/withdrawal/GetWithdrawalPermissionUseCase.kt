package com.b2broker.b2core.domain.withdrawal

import arrow.core.Either
import com.b2broker.b2core.domain.permission.CheckPermissionUseCase
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.permissions.MenuPermission
import com.b2broker.b2core.model.permissions.StatePermission
import com.b2broker.b2core.model.permissions.UserPermission
import javax.inject.Inject

class GetWithdrawalPermissionUseCase @Inject constructor(
    private val checkFeature: CheckPermissionUseCase,
) {
    suspend operator fun invoke(cachePolicy: CachePolicy): Either<DefaultError, StatePermission> =
        checkFeature(menu = MenuPermission.Withdraw, user = UserPermission.Withdrawals, cachePolicy = cachePolicy)
}
