package com.b2broker.b2core.domain.onboarding

import arrow.core.Either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.data.oboarding.OnboardingRepository
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.onboarding.OnboardingPage
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetOnboardingPagesUseCase @Inject constructor(
    private val onboardingRepository: OnboardingRepository,
    @DefaultDispatcher private val dispatcher: CoroutineDispatcher,
) {

    suspend operator fun invoke(): Either<DefaultError, List<OnboardingPage>> =
        withContext(dispatcher) { onboardingRepository.getOnboardingPages() }
}
