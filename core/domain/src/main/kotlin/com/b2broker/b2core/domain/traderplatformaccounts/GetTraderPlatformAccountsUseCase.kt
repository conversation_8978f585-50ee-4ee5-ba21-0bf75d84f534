package com.b2broker.b2core.domain.traderplatformaccounts

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.domain.graphdata.GetGraphDataUseCase
import com.b2broker.b2core.domain.permission.CheckPermissionUseCase
import com.b2broker.b2core.domain.permission.GetMenuItemPermissionUseCase
import com.b2broker.b2core.domain.platforms.accounts.GetAccountsByPlatformTypeUseCase
import com.b2broker.b2core.domain.traderplatformaccounts.mapper.AccountsToTraderPlatformAccountsMapper
import com.b2broker.b2core.formatter.CurrentTimeProvider
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.permissions.MenuPermission
import com.b2broker.b2core.model.permissions.UserPermission
import com.b2broker.b2core.model.platforms.PlatformType
import com.b2broker.b2core.model.traderplatformaccounts.AccountId
import com.b2broker.b2core.model.traderplatformaccounts.TraderPlatformAccount
import javax.inject.Inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class GetTraderPlatformAccountsUseCase @Inject internal constructor(
    private val getAccountsByPlatformTypeUseCase: GetAccountsByPlatformTypeUseCase,
    private val getGraphDataUseCase: GetGraphDataUseCase,
    private val checkPermissionUseCase: CheckPermissionUseCase,
    private val menuPermission: GetMenuItemPermissionUseCase,
    private val accountsToTraderPlatformAccountsMapper: AccountsToTraderPlatformAccountsMapper,
    private val currentTimeProvider: CurrentTimeProvider,
) {

    suspend operator fun invoke(
        platformType: PlatformType,
        cachePolicy: CachePolicy = CachePolicy.CACHE_FIRST,
    ): Either<DefaultError, List<TraderPlatformAccount>> = either {
        val depositPermissionState = checkPermissionUseCase(
            menu = MenuPermission.Deposit,
            user = UserPermission.Deposits,
            cachePolicy = CachePolicy.CACHE_FIRST,
        ).bind()

        val withdrawPermissionState = checkPermissionUseCase(
            menu = MenuPermission.Withdraw,
            user = UserPermission.Withdrawals,
            cachePolicy = CachePolicy.CACHE_FIRST,
        ).bind()

        val isTransferMenuPermissionGranted = menuPermission(
            permission = MenuPermission.Transfer,
            cachePolicy = CachePolicy.CACHE_FIRST,
        ).bind()

        val monthAgo = currentTimeProvider.getTime() - 30.toDuration(DurationUnit.DAYS)
        val graphData = getGraphDataUseCase(monthAgo).bind()
            .associateBy { AccountId(it.id) }

        val accounts = getAccountsByPlatformTypeUseCase(platformType, cachePolicy).bind()

        accountsToTraderPlatformAccountsMapper.map(
            accounts = accounts,
            depositPermissionState = depositPermissionState,
            withdrawPermissionState = withdrawPermissionState,
            isTransferMenuPermissionGranted = isTransferMenuPermissionGranted,
            graphData = graphData,
        )
    }
}
