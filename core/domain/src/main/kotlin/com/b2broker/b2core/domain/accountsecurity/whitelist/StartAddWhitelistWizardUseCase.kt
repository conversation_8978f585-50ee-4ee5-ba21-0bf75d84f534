package com.b2broker.b2core.domain.accountsecurity.whitelist

import arrow.core.Either
import arrow.core.flatMap
import com.b2broker.b2core.data.accountsecurity.AccountSecurityRepository
import com.b2broker.b2core.domain.systeminfo.GetSystemInfoUseCase
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.accounts.WizardWorkflow
import javax.inject.Inject

class StartAddWhitelistWizardUseCase @Inject constructor(
    private val accountSecurityRepository: AccountSecurityRepository,
    private val getSystemInfoUseCase: GetSystemInfoUseCase,
) {

    suspend operator fun invoke(): Either<DefaultError, WizardWorkflow> {
        return getSystemInfoUseCase(CachePolicy.CACHE_FIRST).flatMap { systemInfo ->
            accountSecurityRepository.startAddWhitelistWizard(
                frontendUrl = systemInfo.frontendUrl,
            )
        }
    }
}
