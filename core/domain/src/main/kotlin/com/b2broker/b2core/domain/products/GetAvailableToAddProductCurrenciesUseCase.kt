package com.b2broker.b2core.domain.products

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.data.products.ProductsRepository
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.platforms.PlatformType
import com.b2broker.b2core.model.products.ProductCurrency
import com.b2broker.b2core.model.products.ProductGroupType
import com.b2broker.b2core.model.products.ProductType
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import javax.inject.Inject

@Suppress("MaxChainedCallsOnSameLine")
class GetAvailableToAddProductCurrenciesUseCase @Inject constructor(
    private val productsRepository: ProductsRepository,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
) {
    suspend operator fun invoke(
        platformTypes: Set<PlatformType> = PlatformType.entries.toSet(),
        productTypes: Set<ProductType> = ProductType.entries.toSet(),
        productGroupTypes: Set<ProductGroupType> = ProductGroupType.entries.toSet(),
    ): Either<DefaultError, List<ProductCurrency>> = withContext(defaultDispatcher) {
        either {
            val productCurrenciesAsync = async { productsRepository.getProductsCurrency().bind() }
            val productCurrencies = productCurrenciesAsync.await()
            productCurrencies
                .asSequence()
                .filter { it.product.platform.type in platformTypes }
                .filter { it.product.type in productTypes }
                .filter { it.product.group.type in productGroupTypes }
                .groupBy { it.product.id }
                .map { products ->
                    products.value
                        .groupBy { it.currency.alphabeticCode }
                        .map { currencies ->
                            currencies.value.reduce { _, productCurrency -> productCurrency }
                        }
                }
                .flatten()
                .sortedBy { it.currency.numericCode }
        }
    }
}
