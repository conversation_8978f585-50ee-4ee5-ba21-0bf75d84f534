package com.b2broker.b2core.domain.qrscan

import arrow.core.Either
import com.b2broker.b2core.data.qrlogin.QrLoginRepository
import com.b2broker.b2core.model.DefaultError
import javax.inject.Inject

class QrLoginUseCase @Inject constructor(
    private val qrLoginRepository: QrLoginRepository,
) {

    suspend operator fun invoke(
        channelId: String,
    ): Either<DefaultError, Unit> {
        return qrLoginRepository.loginOnWebWithQr(channelId)
    }
}
