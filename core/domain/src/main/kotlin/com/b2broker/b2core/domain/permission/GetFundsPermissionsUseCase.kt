package com.b2broker.b2core.domain.permission

import arrow.core.Either
import arrow.core.raise.either
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.data.menu.MenuRepository
import com.b2broker.b2core.model.CachePolicy
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.menu.MenuItemType
import com.b2broker.b2core.model.menu.findByType
import com.b2broker.b2core.model.permissions.MenuPermission
import com.b2broker.b2core.model.permissions.toPermission
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetFundsPermissionsUseCase @Inject constructor(
    private val menuRepository: MenuRepository,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
) {

    suspend operator fun invoke(cachePolicy: CachePolicy): Either<DefaultError, List<MenuPermission>> = either {
        withContext(defaultDispatcher) {
            val menu = menuRepository.getMenu(cachePolicy = cachePolicy).bind()
            val generalMenu = menu.findByType(MenuItemType.GENERAL)?.children
            val fundsMenu = generalMenu?.findByType(MenuItemType.FUNDS)
            val exchangeMenu = generalMenu?.findByType(MenuItemType.EXCHANGE)

            val fundsPermissions = fundsMenu?.children.orEmpty()
                .map { it.toPermission() }

            val exchangePermission = exchangeMenu?.toPermission() ?: MenuPermission.Unknown

            val historyPermission = generalMenu
                ?.findByType(MenuItemType.HISTORY)
                ?.children
                ?.map { it.toPermission() }
                .orEmpty()

            val walletsPermission = generalMenu
                ?.findByType(MenuItemType.WALLETS)
                ?.toPermission()
                ?: MenuPermission.Unknown

            val platformPermission = generalMenu
                ?.findByType(MenuItemType.PLATFORMS)
                ?.toPermission()
                ?: MenuPermission.Unknown

            (fundsPermissions + exchangePermission + historyPermission + walletsPermission + platformPermission)
                .filter { it != MenuPermission.Unknown }
        }
    }
}
