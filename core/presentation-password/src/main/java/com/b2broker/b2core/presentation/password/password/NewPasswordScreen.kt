package com.b2broker.b2core.presentation.password.password

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.appspecific.B2PasswordRule
import com.b2broker.b2core.designsystem.appspecific.B2PasswordTextField
import com.b2broker.b2core.designsystem.appspecific.basescreen.action.B2ActionBaseScreen
import com.b2broker.b2core.designsystem.appspecific.basescreen.action.B2ActionButtonUiState
import com.b2broker.b2core.designsystem.component.Spacer
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.model.password.PasswordRule
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource

@Composable
public fun NewPasswordScreen(
    uiState: NewPasswordUiState,
    onBackClick: () -> Unit,
    onSubmitClick: () -> Unit,
    onPasswordUpdate: (String) -> Unit,
    onTogglePasswordVisibilityClick: () -> Unit,
    onPasswordConfirmationUpdate: (String) -> Unit,
    onTogglePasswordConfirmationVisibilityClick: () -> Unit,
    modifier: Modifier = Modifier,
    title: String = stringResource(id = CoreStrings.password_new_screen_title),
    bodyTitle: String = stringResource(id = CoreStrings.password_new_title),
    bodyDescription: String = stringResource(id = CoreStrings.password_new_subtitle),
) {
    B2ActionBaseScreen(
        title = title,
        bodyTitle = bodyTitle,
        bodyDescription = bodyDescription,
        bodyContent = {
            B2PasswordTextField(
                password = uiState.password,
                isPasswordVisible = uiState.isPasswordVisible,
                onPasswordChange = onPasswordUpdate,
                onTogglePasswordVisibilityClick = onTogglePasswordVisibilityClick,
                supportingText = {
                    Column {
                        PasswordRule.entries.forEach { rule ->
                            B2PasswordRule(
                                rule = rule,
                                errors = uiState.passwordErrors,
                            )
                        }
                    }
                },
                isError = false,
                imeAction = ImeAction.Next,
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(NewPasswordScreenTestTags.PASSWORD_INPUT),
            )

            Spacer(height = 16.dp)

            B2PasswordTextField(
                password = uiState.passwordConfirmation,
                isPasswordVisible = uiState.isPasswordConfirmationVisible,
                onPasswordChange = onPasswordConfirmationUpdate,
                onTogglePasswordVisibilityClick = onTogglePasswordConfirmationVisibilityClick,
                label = TextResource(CoreStrings.password_new_confirm_label),
                supportingText = uiState.passwordConfirmationError,
                imeAction = ImeAction.Done,
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(NewPasswordScreenTestTags.PASSWORD_CONFIRMATION_INPUT),
            )
        },
        onBackClick = onBackClick,
        positiveButton = B2ActionButtonUiState(
            onClick = onSubmitClick,
            text = stringResource(id = CoreStrings.password_new_btn_continue),
            isEnabled = uiState.isContinueEnabled,
            isLoading = uiState.isInProgress,
        ),
        modifier = modifier,
    )
}

object NewPasswordScreenTestTags {
    const val PASSWORD_INPUT = "PasswordInput"
    const val PASSWORD_CONFIRMATION_INPUT = "PasswordConfirmationInput"
}

@PreviewLightDark
@Composable
private fun InputEmailScreenPreview() = PreviewContainer {
    NewPasswordScreen(
        uiState = NewPasswordUiState.fixture(),
        onBackClick = {},
        onSubmitClick = {},
        onPasswordUpdate = {},
        onTogglePasswordVisibilityClick = {},
        onPasswordConfirmationUpdate = {},
        onTogglePasswordConfirmationVisibilityClick = {},
    )
}
