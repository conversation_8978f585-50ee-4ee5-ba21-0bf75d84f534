package com.b2broker.b2core.toggle

import android.content.SharedPreferences
import com.b2broker.b2core.model.featuretoggle.Feature
import com.google.common.truth.Truth.assertThat
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PreferencesFeatureToggleTest {

    private val feature1: Feature = mockk()
    private val feature2: Feature = mockk()
    private val sharedPreferences: SharedPreferences = mockk()
    private val featureToggle = PreferencesFeatureToggle(sharedPreferences = sharedPreferences)

    @BeforeEach
    fun setUp() {
        every { feature1.toggleName } returns "feature1"
        every { feature2.toggleName } returns "feature2"
    }

    @Test
    fun `WHEN isEnabled THEN check sharedPreferences`() {
        // GIVEN
        every { sharedPreferences.getBoolean("feature1", false) } returns true
        every { sharedPreferences.getBoolean("feature2", false) } returns false

        // WHEN
        val actual1 = featureToggle.isEnabled(feature1)
        val actual2 = featureToggle.isEnabled(feature2)

        // THEN
        assertThat(actual1).isTrue()
        assertThat(actual2).isFalse()
    }

    @Test
    fun `WHEN observeEnabled THEN flow checks sharedPreferences`() = runTest {
        // GIVEN
        every { sharedPreferences.getBoolean("feature1", false) } returns true
        every { sharedPreferences.getBoolean("feature2", false) } returns false

        // WHEN
        val actual1 = featureToggle.observeEnabled(feature1).first()
        val actual2 = featureToggle.observeEnabled(feature2).first()

        // THEN
        assertThat(actual1).isTrue()
        assertThat(actual2).isFalse()
    }

    @Test
    fun `WHEN hasEnabledState THEN check sharedPreferences`() = runTest {
        // GIVEN
        every { sharedPreferences.contains("feature1") } returns true
        every { sharedPreferences.contains("feature2") } returns false

        // WHEN
        val actual1 = featureToggle.hasEnabledState(feature1)
        val actual2 = featureToggle.hasEnabledState(feature2)

        // THEN
        assertThat(actual1).isTrue()
        assertThat(actual2).isFalse()
    }

    @Test
    fun `WHEN getValue THEN check sharedPreferences`() = runTest {
        // GIVEN
        every { sharedPreferences.getString("feature1_value", "") } returns "value1"
        every { sharedPreferences.getString("feature2_value", "") } returns null

        // WHEN
        val actual1 = featureToggle.getValue(feature1)
        val actual2 = featureToggle.getValue(feature2)

        // THEN
        assertThat(actual1).isEqualTo("value1")
        assertThat(actual2).isEqualTo("")
    }
}
