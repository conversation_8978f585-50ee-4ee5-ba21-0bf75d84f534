plugins {
    alias(libs.plugins.b2broker.library)
    alias(libs.plugins.b2broker.dagger.hilt)
}

android {
    namespace = "com.b2broker.b2core.logger"

    buildFeatures {
        buildConfig = true
    }

    detekt {
        ignoredBuildTypes = buildTypes.map { it.name }
    }
}

dependencies {
    implementation(projects.core.model)

    // App StartUp
    implementation(libs.androidx.app.startup)

    // Sentry
    qaImplementation(platform(libs.sentry.bom))
    qaImplementation(libs.sentry.android)
    qaImplementation(libs.sentry.android.compose)
    qaImplementation(libs.sentry.android.timber)

    releaseImplementation(platform(libs.sentry.bom))
    releaseImplementation(libs.sentry.android)
    releaseImplementation(libs.sentry.android.compose)
    releaseImplementation(libs.sentry.android.timber)

    // Timber
    implementation(libs.timber)

    // Voyager
    implementation(libs.voyager.navigator)
}
