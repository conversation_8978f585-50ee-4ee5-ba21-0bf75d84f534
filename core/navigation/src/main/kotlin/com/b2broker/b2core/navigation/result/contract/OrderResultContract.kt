package com.b2broker.b2core.navigation.result.contract

import cafe.adriel.voyager.core.screen.ScreenKey
import com.b2broker.b2core.model.b2trader.orders.spot.Order
import com.b2broker.b2core.navigation.result.NavigationContract
import kotlinx.parcelize.Parcelize

public interface OrderResultContract {
    @Parcelize
    data class Request(
        override val screenKey: ScreenKey,
        val order: Order,
        val errorMessage: String? = null,
    ) : NavigationContract.Request

    @Parcelize
    data object Result : NavigationContract.Result
}
