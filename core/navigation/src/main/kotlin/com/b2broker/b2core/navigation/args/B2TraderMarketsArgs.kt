package com.b2broker.b2core.navigation.args

import android.os.Parcelable
import com.b2broker.b2core.model.b2trader.markets.MarketSelectionType
import com.b2broker.b2core.model.b2trader.markets.MarketSelectionType.COMMON_MARKET_SELECTION
import kotlinx.parcelize.Parcelize

@Parcelize
public data class B2TraderMarketsArgs(
    val accountId: String,
    val marketId: String,
    val rootAsset: String,
    val excludedMarkets: Set<String> = emptySet(),
    val marketSelectionType: MarketSelectionType = COMMON_MARKET_SELECTION,
) : Parcelable {

    companion object {
        fun fixture(
            accountId: String = "8f604a64-bf16-4705-8f26-70c248bfe77c",
            marketId: String = "ed4d26ef-9e75-4f46-b3e9-3b96f0866158",
            rootAsset: String = "rootAsset_8418",
            excludedMarkets: Set<String> = emptySet(),
            marketSelectionType: MarketSelectionType = COMMON_MARKET_SELECTION,
        ) = B2TraderMarketsArgs(
            accountId = accountId,
            marketId = marketId,
            rootAsset = rootAsset,
            excludedMarkets = excludedMarkets,
            marketSelectionType = marketSelectionType,
        )
    }
}
