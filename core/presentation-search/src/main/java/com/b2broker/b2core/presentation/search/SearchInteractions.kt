package com.b2broker.b2core.presentation.search

import com.b2broker.b2core.model.search.B2SearchItem
import com.b2broker.b2core.presentation.search.model.CategoryChip

interface SearchInteractions<T : B2SearchItem> {

    fun onBackClick()
    fun onConfirmClick()
    fun onQueryChange(query: String)
    fun onCategoryChipClick(categoryChip: CategoryChip)
    fun onItemSelected(item: T)
    fun onClearClick()

    companion object {
        fun <T : B2SearchItem> mock() = object : SearchInteractions<T> {
            override fun onBackClick() = Unit
            override fun onConfirmClick() = Unit
            override fun onQueryChange(query: String) = Unit
            override fun onCategoryChipClick(categoryChip: CategoryChip) = Unit
            override fun onItemSelected(item: T) = Unit
            override fun onClearClick() = Unit
        }
    }
}
