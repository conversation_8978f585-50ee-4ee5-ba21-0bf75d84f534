package com.b2broker.b2core.presentation.search.ui

import com.b2broker.b2core.model.search.B2SearchItem
import com.b2broker.b2core.presentation.search.model.CategoryChip
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

public data class B2SearchUiState<T : B2SearchItem>(
    val isLoading: Boolean = true,
    val query: String = "",
    val emptyMessage: TextResource = TextResource(CoreStrings.common_no_results),
    val emptyDescription: TextResource = TextResource(CoreStrings.common_try_again),
    val filteredItems: ImmutableList<T> = persistentListOf(),
    val selectedItems: ImmutableList<T> = persistentListOf(),
    val categoryChips: List<CategoryChip> = emptyList(),
    val selectedCategory: CategoryChip? = null,
    val showCategoryHeaders: Boolean = false,
    val showDividers: Boolean = false,
    val showClearButton: Boolean = false,
    val allowEmptySelect: Boolean = false,
    val onTypeChipClick: () -> Unit = {},
    val onItemClick: () -> Unit = {},
    val onBackClick: () -> Unit = {},
) {

    val isConfirmButtonEnabled = allowEmptySelect || selectedItems.isNotEmpty()

    companion object {

        fun <T : B2SearchItem> fixture(
            isLoading: Boolean = false,
            query: String = "query_8962",
            emptyMessage: TextResource = TextResource(CoreStrings.common_no_results),
            emptyDescription: TextResource = TextResource(CoreStrings.common_try_again),
            filteredItems: ImmutableList<T> = persistentListOf(),
            selectedItems: ImmutableList<T> = persistentListOf(),
            categoryChips: List<CategoryChip> = emptyList(),
            selectedCategory: CategoryChip? = CategoryChip.fixture(),
            showCategoryHeaders: Boolean = true,
            showDividers: Boolean = true,
            showClearButton: Boolean = false,
            onTypeChipClick: () -> Unit = {},
            onItemClick: () -> Unit = {},
            onBackClick: () -> Unit = {},
        ) = B2SearchUiState(
            isLoading = isLoading,
            query = query,
            emptyMessage = emptyMessage,
            emptyDescription = emptyDescription,
            filteredItems = filteredItems,
            selectedItems = selectedItems,
            categoryChips = categoryChips,
            selectedCategory = selectedCategory,
            showCategoryHeaders = showCategoryHeaders,
            showDividers = showDividers,
            showClearButton = showClearButton,
            onTypeChipClick = onTypeChipClick,
            onItemClick = onItemClick,
            onBackClick = onBackClick,
        )
    }
}
