package com.b2broker.b2core.chart.components

import android.util.Size
import android.webkit.WebView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.b2broker.b2core.chart.api.B2ChartApi
import com.b2broker.b2core.chart.model.ChartApiLifecycleEvent
import com.b2broker.b2core.chart.model.ChartTheme
import com.b2broker.b2core.presentation.cache.CacheableView
import com.b2broker.b2core.presentation.cache.CacheableWebView
import com.b2broker.b2core.presentation.cache.ViewCache
import com.b2broker.b2core.presentation.composition.LocalViewCache

@Composable
internal fun AdvancedChart(
    theme: ChartTheme,
    modifier: Modifier = Modifier,
    cacheKey: ViewCache.Key<CacheableView>? = null,
    isVisible: Boolean = false,
    hint: (@Composable (containerSize: Size) -> Unit)? = null,
    onChartEvent: (event: ChartApiLifecycleEvent) -> Unit = {},
) {
    val onEvent by rememberUpdatedState(onChartEvent)

    val ctx = LocalContext.current
    val cache = LocalViewCache.current
    val webView = remember {
        cacheKey?.let {
            cache.getOrCreate(cacheKey) {
                CacheableWebView(ctx).also { view ->
                    onChartEvent(ChartApiLifecycleEvent.OnCoreApiCreate(B2ChartApi(view, theme)))
                }
            } as WebView
        } ?: WebView(ctx)
    }

    if (isVisible) {
        Box(modifier.then(Modifier.clipToBounds())) {
            AndroidView(
                modifier = Modifier.fillMaxSize(),
                factory = { webView },
            )
        }
    }

    hint?.invoke(Size(webView.width, webView.height))

    DisposableEffect(onEvent) {
        onDispose {
            if (cacheKey == null) {
                onEvent(ChartApiLifecycleEvent.OnCoreApiRelease)
            }
        }
    }
}
