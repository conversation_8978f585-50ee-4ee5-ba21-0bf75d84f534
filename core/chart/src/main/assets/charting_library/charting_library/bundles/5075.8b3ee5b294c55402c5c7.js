(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5075],{59142:function(e,t){var o,n,r;n=[t],o=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,o=Array(e.length);t<e.length;t++)o[t]=e[t];return o}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var o=!1;if("undefined"!=typeof window){var n={get passive(){o=!0}};window.addEventListener("testPassive",null,n),window.removeEventListener("testPassive",null,n)}var r="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),i=[],s=!1,a=-1,l=void 0,c=void 0,d=function(e){return i.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},u=function(e){var t=e||window.event;return!!d(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},p=function(){setTimeout((function(){void 0!==c&&(document.body.style.paddingRight=c,c=void 0),void 0!==l&&(document.body.style.overflow=l,l=void 0)}))};e.disableBodyScroll=function(e,n){if(r){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!i.some((function(t){return t.targetElement===e}))){var p={targetElement:e,options:n||{}};i=[].concat(t(i),[p]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var o,n,r,i;1===t.targetTouches.length&&(n=e,i=(o=t).targetTouches[0].clientY-a,!d(o.target)&&(n&&0===n.scrollTop&&0<i||(r=n)&&r.scrollHeight-r.scrollTop<=r.clientHeight&&i<0?u(o):o.stopPropagation()))},s||(document.addEventListener("touchmove",u,o?{passive:!1}:void 0),s=!0)}}else{m=n,setTimeout((function(){if(void 0===c){var e=!!m&&!0===m.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(c=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===l&&(l=document.body.style.overflow,document.body.style.overflow="hidden")}));var h={targetElement:e,options:n||{}};i=[].concat(t(i),[h])}var m},e.clearAllBodyScrollLocks=function(){r?(i.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),s&&(document.removeEventListener("touchmove",u,o?{passive:!1}:void 0),s=!1),i=[],a=-1):(p(),i=[])},e.enableBodyScroll=function(e){if(r){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,i=i.filter((function(t){return t.targetElement!==e})),s&&0===i.length&&(document.removeEventListener("touchmove",u,o?{passive:!1}:void 0),s=!1)}else 1===i.length&&i[0].targetElement===e?(p(),i=[]):i=i.filter((function(t){return t.targetElement!==e}))}},void 0===(r="function"==typeof o?o.apply(t,n):o)||(e.exports=r)},3196:e=>{e.exports={"tv-circle-logo":"tv-circle-logo-PsAlMQQF","tv-circle-logo--xxxsmall":"tv-circle-logo--xxxsmall-PsAlMQQF","tv-circle-logo--xxsmall":"tv-circle-logo--xxsmall-PsAlMQQF",
"tv-circle-logo--xsmall":"tv-circle-logo--xsmall-PsAlMQQF","tv-circle-logo--small":"tv-circle-logo--small-PsAlMQQF","tv-circle-logo--medium":"tv-circle-logo--medium-PsAlMQQF","tv-circle-logo--large":"tv-circle-logo--large-PsAlMQQF","tv-circle-logo--xlarge":"tv-circle-logo--xlarge-PsAlMQQF","tv-circle-logo--xxlarge":"tv-circle-logo--xxlarge-PsAlMQQF","tv-circle-logo--xxxlarge":"tv-circle-logo--xxxlarge-PsAlMQQF","tv-circle-logo--visually-hidden":"tv-circle-logo--visually-hidden-PsAlMQQF"}},49844:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},69658:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},2908:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",item:"item-jFqVJoPk",hovered:"hovered-jFqVJoPk",isDisabled:"isDisabled-jFqVJoPk",isActive:"isActive-jFqVJoPk",shortcut:"shortcut-jFqVJoPk",toolbox:"toolbox-jFqVJoPk",withIcon:"withIcon-jFqVJoPk","round-icon":"round-icon-jFqVJoPk",icon:"icon-jFqVJoPk",labelRow:"labelRow-jFqVJoPk",label:"label-jFqVJoPk",showOnHover:"showOnHover-jFqVJoPk","disclosure-item-circle-logo":"disclosure-item-circle-logo-jFqVJoPk",showOnFocus:"showOnFocus-jFqVJoPk"}},71150:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},62794:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},53885:(e,t,o)=>{"use strict";o.d(t,{getStyleClasses:()=>s,isCircleLogoWithUrlProps:()=>a});var n=o(97754),r=o(3196),i=o.n(r);function s(e,t){return n(i()["tv-circle-logo"],i()[`tv-circle-logo--${e}`],t)}function a(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},39416:(e,t,o)=>{"use strict";o.d(t,{useFunctionalRefObject:()=>i});var n=o(50959),r=o(43010);function i(e){const t=(0,n.useMemo)((()=>function(e){const t=o=>{e(o),t.current=o};return t.current=null,t}((e=>{a.current(e)}))),[]),o=(0,n.useRef)(null),i=t=>{if(null===t)return s(o.current,t),void(o.current=null);o.current!==e&&(o.current=e,s(o.current,t))},a=(0,n.useRef)(i);return a.current=i,(0,r.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return a.current(t.current),()=>a.current(null)}),[e]),t}function s(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},43010:(e,t,o)=>{"use strict";o.d(t,{useIsomorphicLayoutEffect:()=>r});var n=o(50959);function r(e,t){("undefined"==typeof window?n.useEffect:n.useLayoutEffect)(e,t)}},27267:(e,t,o)=>{"use strict";function n(e,t,o,n,r){function i(r){if(e>r.timeStamp)return;const i=r.target;void 0!==o&&null!==t&&null!==i&&i.ownerDocument===n&&(t.contains(i)||o(r))}return r.click&&n.addEventListener("click",i,!1),r.mouseDown&&n.addEventListener("mousedown",i,!1),
r.touchEnd&&n.addEventListener("touchend",i,!1),r.touchStart&&n.addEventListener("touchstart",i,!1),()=>{n.removeEventListener("click",i,!1),n.removeEventListener("mousedown",i,!1),n.removeEventListener("touchend",i,!1),n.removeEventListener("touchstart",i,!1)}}o.d(t,{addOutsideEventListener:()=>n})},36383:(e,t,o)=>{"use strict";o.d(t,{useOutsideEvent:()=>s});var n=o(50959),r=o(43010),i=o(27267);function s(e){const{click:t,mouseDown:o,touchEnd:s,touchStart:a,handler:l,reference:c}=e,d=(0,n.useRef)(null),u=(0,n.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,r.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:o,touchEnd:s,touchStart:a},n=c?c.current:d.current;return(0,i.addOutsideEventListener)(u.current,n,l,document,e)}),[t,o,s,a,l]),c||d}},9745:(e,t,o)=>{"use strict";o.d(t,{Icon:()=>r});var n=o(50959);const r=n.forwardRef(((e,t)=>{const{icon:o="",title:r,ariaLabel:i,ariaLabelledby:s,ariaHidden:a,...l}=e,c=!!(r||i||s);return n.createElement("span",{...l,ref:t,role:"img","aria-label":i,"aria-labelledby":s,"aria-hidden":a||!c,title:r,dangerouslySetInnerHTML:{__html:o}})}))},26996:(e,t,o)=>{"use strict";o.d(t,{Loader:()=>l});var n,r=o(50959),i=o(97754),s=o(49844),a=o.n(s);function l(e){const{className:t,size:o="medium",staticPosition:n,color:s="black"}=e,l=i(a().item,a()[s],a()[o]);return r.createElement("span",{className:i(a().loader,n&&a().static,t)},r.createElement("span",{className:l}),r.createElement("span",{className:l}),r.createElement("span",{className:l}))}!function(e){e.Medium="medium",e.Small="small"}(n||(n={}))},99663:(e,t,o)=>{"use strict";o.d(t,{Slot:()=>r,SlotContext:()=>i});var n=o(50959);class r extends n.Component{shouldComponentUpdate(){return!1}render(){return n.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const i=n.createContext(null)},90186:(e,t,o)=>{"use strict";function n(e){return i(e,s)}function r(e){return i(e,a)}function i(e,t){const o=Object.entries(e).filter(t),n={};for(const[e,t]of o)n[e]=t;return n}function s(e){const[t,o]=e;return 0===t.indexOf("data-")&&"string"==typeof o}function a(e){return 0===e[0].indexOf("aria-")}o.d(t,{filterAriaProps:()=>r,filterDataProps:()=>n,filterProps:()=>i,isAriaAttribute:()=>a,isDataAttribute:()=>s})},67961:(e,t,o)=>{"use strict";o.d(t,{OverlapManager:()=>i,getRootOverlapManager:()=>a});var n=o(50151);class r{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class i{constructor(e=document){this._storage=new r,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,o=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,o),this._container=o}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={
position:"fixed",direction:"normal"}){const o=this._windows.get(e);if(void 0!==o)return o;this.registerWindow(e);const n=this._document.createElement("div");if(n.style.position=t.position,n.style.zIndex=this._index.toString(),n.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(n);else if(t.index<=0)this._container.insertBefore(n,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(n,e)}}else"reverse"===t.direction?this._container.insertBefore(n,this._container.firstChild):this._container.appendChild(n);return this._windows.set(e,n),++this._index,n}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,o)=>{e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap",e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const s=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,n.ensureDefined)(s.get(t));{const t=new i(e),o=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return s.set(o,t),t.setContainer(o),e.body.appendChild(o),t}}var l;!function(e){e[e.BaseZindex=150]="BaseZindex"}(l||(l={}))},47201:(e,t,o)=>{"use strict";function n(...e){return t=>{for(const o of e)void 0!==o&&o(t)}}o.d(t,{createSafeMulticastEventHandler:()=>n})},99054:(e,t,o)=>{"use strict";o.d(t,{setFixedBodyState:()=>c});const n=(()=>{let e;return()=>{var t;if(void 0===e){const o=document.createElement("div"),n=o.style;n.visibility="hidden",n.width="100px",n.msOverflowStyle="scrollbar",document.body.appendChild(o);const r=o.offsetWidth;o.style.overflow="scroll";const i=document.createElement("div");i.style.width="100%",o.appendChild(i);const s=i.offsetWidth;null===(t=o.parentNode)||void 0===t||t.removeChild(o),e=r-s}return e}})();function r(e,t,o){null!==e&&e.style.setProperty(t,o)}function i(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function s(e,t){return parseInt(i(e,t))}let a=0,l=!1;function c(e){const{body:t}=document,o=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=i(t,"overflow"),a=s(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(r(o,"right",`${n()}px`),t.style.paddingRight=`${a+n()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){r(o,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=n()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},50238:(e,t,o)=>{"use strict";o.d(t,{useRovingTabindexElement:()=>i});var n=o(50959),r=o(39416);function i(e,t=[]){const[o,i]=(0,
n.useState)(!1),s=(0,r.useFunctionalRefObject)(e);return(0,n.useLayoutEffect)((()=>{const e=s.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":i(!0);break;case"roving-tabindex:secondary-element":i(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[s,o?0:-1]}},59695:(e,t,o)=>{"use strict";o.d(t,{CircleLogo:()=>a,hiddenCircleLogoClass:()=>s});var n=o(50959),r=o(53885),i=o(3196);const s=o.n(i)()["tv-circle-logo--visually-hidden"];function a(e){var t,o;const i=(0,r.getStyleClasses)(e.size,e.className),s=null!==(o=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==o?o:"";return(0,r.isCircleLogoWithUrlProps)(e)?n.createElement("img",{className:i,crossOrigin:"",src:e.logoUrl,alt:s,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):n.createElement("span",{className:i,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},37558:(e,t,o)=>{"use strict";o.d(t,{DrawerContext:()=>s,DrawerManager:()=>i});var n=o(50959),r=o(99054);class i extends n.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,r.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,r.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,r.setFixedBodyState)(!1)}render(){return n.createElement(s.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const s=n.createContext(null)},41590:(e,t,o)=>{"use strict";o.d(t,{Drawer:()=>h});var n=o(50959),r=o(50151),i=o(97754),s=o(92184),a=o(42842),l=o(37558),c=o(29197),d=o(86656),u=o(69658);var p;function h(e){const{position:t="Bottom",onClose:o,children:d,reference:p,className:h,theme:v=u}=e,f=(0,r.ensureNotNull)((0,n.useContext)(l.DrawerContext)),[g]=(0,n.useState)((()=>(0,s.randomHash)())),b=(0,n.useRef)(null),w=(0,n.useContext)(c.CloseDelegateContext);return(0,n.useLayoutEffect)((()=>((0,r.ensureNotNull)(b.current).focus({preventScroll:!0}),w.subscribe(f,o),f.addDrawer(g),()=>{f.removeDrawer(g),w.unsubscribe(f,o)})),[]),n.createElement(a.Portal,null,n.createElement("div",{ref:p,className:i(u.wrap,u[`position${t}`])},g===f.currentDrawer&&n.createElement("div",{className:u.backdrop,onClick:o}),n.createElement(m,{className:i(v.drawer,u[`position${t}`],h),ref:b,"data-name":e["data-name"]},d)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(p||(p={}));const m=(0,n.forwardRef)(((e,t)=>{const{className:o,...r}=e
;return n.createElement(d.TouchScrollContainer,{className:i(u.drawer,o),tabIndex:-1,ref:t,...r})}))},16396:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_POPUP_MENU_ITEM_THEME:()=>d,PopupMenuItem:()=>p});var n=o(50959),r=o(97754),i=o(51768),s=o(59064),a=o(59695),l=o(76460),c=o(2908);const d=c;function u(e){e.stopPropagation()}function p(e){const{id:t,role:o,className:d,title:p,labelRowClassName:h,labelClassName:m,toolboxClassName:v,shortcut:f,forceShowShortcuts:g,icon:b,iconClassname:w,isActive:y,isDisabled:E,isHovered:x,appearAsDisabled:_,label:T,link:C,showToolboxOnHover:k,showToolboxOnFocus:D,target:B,rel:O,toolbox:M,reference:L,onMouseOut:N,onMouseOver:P,onKeyDown:S,suppressToolboxClick:F=!0,theme:R=c,tabIndex:A,tagName:I,renderComponent:U,roundedIcon:W,iconAriaProps:Q,circleLogo:H,dontClosePopup:j,onClick:z,onClickArg:V,trackEventObject:K,trackMouseWheelClick:q,trackRightClick:G,...J}=e,Z=(0,n.useRef)(null),$=(0,n.useMemo)((()=>function(e){function t(t){const{reference:o,...r}=t,i=null!=e?e:r.href?"a":"div",s="a"===i?r:function(e){const{download:t,href:o,hrefLang:n,media:r,ping:i,rel:s,target:a,type:l,referrerPolicy:c,...d}=e;return d}(r);return n.createElement(i,{...s,ref:o})}return t.displayName=`DefaultComponent(${e})`,t}(I)),[I]),Y=null!=U?U:$;return n.createElement(Y,{...J,id:t,role:o,className:r(d,R.item,b&&R.withIcon,{[R.isActive]:y,[R.isDisabled]:E||_,[R.hovered]:x}),title:p,href:C,target:B,rel:O,reference:function(e){Z.current=e,"function"==typeof L&&L(e);"object"==typeof L&&(L.current=e)},onClick:function(e){if(E)return;K&&(0,i.trackEvent)(K.category,K.event,K.label);z&&z(V,e);j||(e.currentTarget.dispatchEvent(new CustomEvent("popup-menu-close-event",{bubbles:!0,detail:{clickType:(0,l.isKeyboardClick)(e)?"keyboard":"mouse"}})),(0,s.globalCloseMenu)())},onContextMenu:function(e){K&&G&&(0,i.trackEvent)(K.category,K.event,`${K.label}_rightClick`)},onMouseUp:function(e){if(1===e.button&&C&&K){let e=K.label;q&&(e+="_mouseWheelClick"),(0,i.trackEvent)(K.category,K.event,e)}},onMouseOver:P,onMouseOut:N,onKeyDown:S,tabIndex:A},H&&n.createElement(a.CircleLogo,{...Q,className:c["disclosure-item-circle-logo"],size:"xxxsmall",logoUrl:H.logoUrl,placeholderLetter:"placeholderLetter"in H?H.placeholderLetter:void 0}),b&&n.createElement("span",{"aria-label":Q&&Q["aria-label"],"aria-hidden":Q&&Boolean(Q["aria-hidden"]),className:r(R.icon,W&&c["round-icon"],w),dangerouslySetInnerHTML:{__html:b}}),n.createElement("span",{className:r(R.labelRow,h)},n.createElement("span",{className:r(R.label,m)},T)),(void 0!==f||g)&&n.createElement("span",{className:R.shortcut},(X=f)&&X.split("+").join(" + ")),void 0!==M&&n.createElement("span",{onClick:F?u:void 0,className:r(v,R.toolbox,{[R.showOnHover]:k,[R.showOnFocus]:D})},M));var X}},11684:(e,t,o)=>{"use strict";o.d(t,{PopupMenuSeparator:()=>l});var n,r=o(50959),i=o(97754),s=o.n(i),a=o(71150);function l(e){const{size:t="normal",className:o,ariaHidden:n=!1}=e;return r.createElement("div",{className:s()(a.separator,"small"===t&&a.small,"normal"===t&&a.normal,"large"===t&&a.large,o),
role:"separator","aria-hidden":n})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(n||(n={}))},42842:(e,t,o)=>{"use strict";o.d(t,{Portal:()=>l,PortalContext:()=>c});var n=o(50959),r=o(32227),i=o(25931),s=o(67961),a=o(99663);class l extends n.PureComponent{constructor(){super(...arguments),this._uuid=(0,i.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap","true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),r.createPortal(n.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,s.getRootOverlapManager)():this.context}}l.contextType=a.SlotContext;const c=n.createContext(null)},10381:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetCaret:()=>l});var n=o(50959),r=o(97754),i=o(9745),s=o(62794),a=o(578);function l(e){const{dropped:t,className:o}=e;return n.createElement(i.Icon,{className:r(o,s.icon,{[s.dropped]:t}),icon:a})}},86656:(e,t,o)=>{"use strict";o.d(t,{TouchScrollContainer:()=>c});var n=o(50959),r=o(59142),i=o(50151),s=o(49483);const a=CSS.supports("overscroll-behavior","none");let l=0;const c=(0,n.forwardRef)(((e,t)=>{const{children:o,...i}=e,c=(0,n.useRef)(null);return(0,n.useImperativeHandle)(t,(()=>c.current)),(0,n.useLayoutEffect)((()=>{if(s.CheckMobile.iOS())return l++,null!==c.current&&(a?1===l&&(document.body.style.overscrollBehavior="none"):(0,r.disableBodyScroll)(c.current,{allowTouchMove:d(c)})),()=>{l--,null!==c.current&&(a?0===l&&(document.body.style.overscrollBehavior=""):(0,r.enableBodyScroll)(c.current))}}),[]),n.createElement("div",{ref:c,...i},o)}));function d(e){return t=>{const o=(0,i.ensureNotNull)(e.current),n=document.activeElement;return!o.contains(t)||null!==n&&o.contains(n)&&n.contains(t)}}},40173:(e,t,o)=>{"use strict";function n(e,t,o={}){return Object.assign({},e,function(e,t,o={}){const n=Object.assign({},t);for(const r of Object.keys(t)){const i=o[r]||r;i in e&&(n[r]=[e[i],t[r]].join(" "))}return n}(e,t,o))}o.d(t,{mergeThemes:()=>n})},6132:(e,t,o)=>{"use strict";var n=o(22134);function r(){}function i(){}i.resetWarningCache=r,e.exports=function(){function e(e,t,o,r,i,s){if(s!==n){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var o={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,
oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return o.PropTypes=o,o}},19036:(e,t,o)=>{e.exports=o(6132)()},22134:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},19347:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},38177:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},52495:e=>{e.exports={button:"button-xNqEcuN2"}},33532:e=>{e.exports={title:"title-u3QJgF_p"}},94878:e=>{e.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},20243:(e,t,o)=>{"use strict";o.d(t,{focusFirstMenuItem:()=>c,handleAccessibleMenuFocus:()=>a,handleAccessibleMenuKeyDown:()=>l,queryMenuElements:()=>p});var n=o(19291),r=o(57177),i=o(68335);const s=[37,39,38,40];function a(e,t){var o;if(!e.target)return;const n=null===(o=e.relatedTarget)||void 0===o?void 0:o.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=n&&document.getElementById(n);if(!e||e!==t.current)return}c(e.target)}function l(e){var t;if(e.defaultPrevented)return;const o=(0,i.hashFromEvent)(e);if(!s.includes(o))return;const a=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const l=p(e.currentTarget).sort(n.navigationOrderComparator);if(0===l.length)return;const c=document.activeElement.closest('[data-role="menuitem"]')||(null===(t=document.activeElement.parentElement)||void 0===t?void 0:t.querySelector('[data-role="menuitem"]'));if(!(c instanceof HTMLElement))return;const m=l.indexOf(c);if(-1===m)return;const v=h(c),f=v.indexOf(document.activeElement),g=-1!==f,b=e=>{a&&(0,r.becomeSecondaryElement)(a),(0,r.becomeMainElement)(e),e.focus()};switch((0,n.mapKeyCodeToDirection)(o)){case"inlinePrev":if(!v.length)return;e.preventDefault(),b(0===f?l[m]:g?d(v,f,-1):v[v.length-1]);break;case"inlineNext":if(!v.length)return;e.preventDefault(),f===v.length-1?b(l[m]):b(g?d(v,f,1):v[0]);break;case"blockPrev":{e.preventDefault();const t=d(l,m,-1);if(g){const e=u(t,f);b(e||t);break}b(t);break}case"blockNext":{e.preventDefault();const t=d(l,m,1);if(g){const e=u(t,f);b(e||t);break}b(t)}}}function c(e){const[t]=p(e);t&&((0,r.becomeMainElement)(t),t.focus())}function d(e,t,o){return e[(t+e.length+o)%e.length]}function u(e,t){const o=h(e);return o.length?o[(t+o.length)%o.length]:null}function p(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,n.createScopedVisibleElementFilter)(e))}function h(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,n.createScopedVisibleElementFilter)(e))}},57177:(e,t,o)=>{
"use strict";var n;function r(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}o.d(t,{becomeMainElement:()=>r,becomeSecondaryElement:()=>i}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(n||(n={}))},10838:(e,t,o)=>{"use strict";o.d(t,{AccessibleMenuItem:()=>d});var n=o(50959),r=o(97754),i=o.n(r),s=o(3343),a=o(50238),l=o(16396),c=o(19347);function d(e){const{className:t,...o}=e,[r,d]=(0,a.useRovingTabindexElement)(null);return n.createElement(l.PopupMenuItem,{...o,className:i()(c.accessible,e.isActive&&c.active,t),reference:r,tabIndex:d,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,s.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),r.current instanceof HTMLElement&&r.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0})}},78135:(e,t,o)=>{"use strict";o.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>n,VerticalDropDirection:()=>i,getPopupPositioner:()=>c});var n,r,i,s,a=o(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(n||(n={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(i||(i={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const l={verticalAttachEdge:n.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:i.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return o=>{var c,d;const{contentWidth:u,contentHeight:p,availableHeight:h}=o,m=(0,a.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:v=l.horizontalAttachEdge,horizontalDropDirection:f=l.horizontalDropDirection,horizontalMargin:g=l.horizontalMargin,verticalMargin:b=l.verticalMargin,matchButtonAndListboxWidths:w=l.matchButtonAndListboxWidths}=t;let y=null!==(c=t.verticalAttachEdge)&&void 0!==c?c:l.verticalAttachEdge,E=null!==(d=t.verticalDropDirection)&&void 0!==d?d:l.verticalDropDirection;y===n.AutoStrict&&(h<m.y+m.height+b+p?(y=n.Top,E=i.FromBottomToTop):(y=n.Bottom,E=i.FromTopToBottom));const x=y===n.Top?-1*b:b,_=v===r.Right?m.right:m.left,T=y===n.Top?m.top:m.bottom,C={x:_-(f===s.FromRightToLeft?u:0)+g,y:T-(E===i.FromBottomToTop?p:0)+x};return w&&(C.overrideWidth=m.width),C}}},81348:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>l});var n=o(50959),r=o(97754),i=o(9745),s=o(38177);const a=s,l=n.forwardRef(((e,t)=>{const{tag:o="div",icon:a,endIcon:l,isActive:c,isOpened:d,isDisabled:u,isGrouped:p,isHovered:h,isClicked:m,onClick:v,text:f,textBeforeIcon:g,title:b,theme:w=s,className:y,forceInteractive:E,inactive:x,"data-name":_,"data-tooltip":T,...C}=e,k=r(y,w.button,(b||T)&&"apply-common-tooltip",{[w.isActive]:c,[w.isOpened]:d,
[w.isInteractive]:(E||Boolean(v))&&!u&&!x,[w.isDisabled]:Boolean(u||x),[w.isGrouped]:p,[w.hover]:h,[w.clicked]:m}),D=a&&("string"==typeof a?n.createElement(i.Icon,{className:w.icon,icon:a}):n.cloneElement(a,{className:r(w.icon,a.props.className)}));return"button"===o?n.createElement("button",{...C,ref:t,type:"button",className:r(k,w.accessible),disabled:u&&!x,onClick:v,title:b,"data-name":_,"data-tooltip":T},g&&f&&n.createElement("div",{className:r("js-button-text",w.text)},f),D,!g&&f&&n.createElement("div",{className:r("js-button-text",w.text)},f)):n.createElement("div",{...C,ref:t,"data-role":"button",className:k,onClick:u?void 0:v,title:b,"data-name":_,"data-tooltip":T},g&&f&&n.createElement("div",{className:r("js-button-text",w.text)},f),D,!g&&f&&n.createElement("div",{className:r("js-button-text",w.text)},f),l&&n.createElement(i.Icon,{icon:l,className:s.endIcon}))}))},56388:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetIconButton:()=>a});var n=o(50959),r=o(97754),i=o(81348),s=o(52495);const a=n.forwardRef((function(e,t){const{className:o,id:a,...l}=e;return n.createElement(i.ToolWidgetButton,{id:a,"data-name":a,...l,ref:t,className:r(o,s.button)})}))},16829:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetMenuSummary:()=>s});var n=o(50959),r=o(97754),i=o(33532);function s(e){return n.createElement("div",{className:r(e.className,i.title)},e.children)}},20626:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetMenu:()=>g});var n=o(50959),r=o(97754),i=o(3343),s=o(20520),a=o(10381),l=o(90186),c=o(37558),d=o(41590),u=o(78135),p=o(90692),h=o(56570),m=o(76460),v=o(94878);var f;!function(e){e[e.Vertical=2]="Vertical",e[e.Horizontal=0]="Horizontal"}(f||(f={}));class g extends n.PureComponent{constructor(e){super(e),this._wrapperRef=null,this._controller=n.createRef(),this._onPopupCloseOnClick=e=>{"keyboard"===e.detail.clickType&&this.focus()},this._handleMenuFocus=e=>{var t,o;e.relatedTarget===this._wrapperRef&&this.setState((e=>({...e,isOpenedByButton:!0}))),null===(o=(t=this.props).onMenuFocus)||void 0===o||o.call(t,e)},this._handleWrapperRef=e=>{this._wrapperRef=e,this.props.reference&&this.props.reference(e)},this._handleOpen=()=>{var e,t,o;"div"!==this.props.tag&&(this.setState((e=>({...e,isOpenedByButton:!1}))),null===(t=null===(e=this.props.menuReference)||void 0===e?void 0:e.current)||void 0===t||t.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),null===(o=this._controller.current)||void 0===o||o.focus())},this._handleClick=e=>{(h.enabled("skip_event_target_check")||e.target instanceof Node)&&e.currentTarget.contains(e.target)&&(this._handleToggleDropdown(void 0,(0,m.isKeyboardClick)(e)),this.props.onClick&&this.props.onClick(e,!this.state.isOpened))},this._handleToggleDropdown=(e,t=!1)=>{const{onClose:o,onOpen:n}=this.props,{isOpened:r}=this.state,i="boolean"==typeof e?e:!r;this.setState({isOpened:i,shouldReturnFocus:!!i&&t}),i&&n&&n(),!i&&o&&o()},this._handleClose=()=>{this.close()},this._handleKeyDown=e=>{var t;const{orientation:o="horizontal"}=this.props;if(e.defaultPrevented)return
;if(!(e.target instanceof Node))return;const n=(0,i.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(n){case 40:if("div"===this.props.tag||"horizontal"!==o)return;if(this.state.isOpened)return;e.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;e.preventDefault(),e.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(n){case 27:{e.preventDefault();const{shouldReturnFocus:o,isOpenedByButton:n}=this.state;this._handleToggleDropdown(!1),o&&n&&(null===(t=this._wrapperRef)||void 0===t||t.focus());break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:e="div",id:t,arrow:o,content:i,isDisabled:s,isDrawer:c,isShowTooltip:d,title:u,className:h,hotKey:m,theme:v,drawerBreakpoint:f,tabIndex:g,isClicked:w}=this.props,{isOpened:y}=this.state,E=r(h,v.button,{"apply-common-tooltip":d||!s,[v.isDisabled]:s,[v.isOpened]:y,[v.clicked]:w}),x=b(i)?i({isOpened:y}):i;return"button"===e?n.createElement("button",{type:"button",id:t,className:r(E,v.accessible),disabled:s,onClick:this._handleClick,title:u,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:g,...(0,l.filterDataProps)(this.props),...(0,l.filterAriaProps)(this.props)},x,o&&n.createElement("div",{className:v.arrow},n.createElement("div",{className:v.arrowWrap},n.createElement(a.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(f?n.createElement(p.MatchMedia,{rule:f},(e=>this._renderContent(e))):this._renderContent(c))):n.createElement("div",{id:t,className:E,onClick:s?void 0:this._handleClick,title:u,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,"data-role":"button",tabIndex:g,onKeyDown:this._handleKeyDown,...(0,l.filterDataProps)(this.props)},x,o&&n.createElement("div",{className:v.arrow},n.createElement("div",{className:v.arrowWrap},n.createElement(a.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&this._renderContent(c))}close(){var e,t;null===(t=null===(e=this.props.menuReference)||void 0===e?void 0:e.current)||void 0===t||t.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){var e;null===(e=this._wrapperRef)||void 0===e||e.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(e){const{menuDataName:t,minWidth:o,menuClassName:r,maxHeight:i,drawerPosition:a="Bottom",children:l}=this.props,{isOpened:p}=this.state,h={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},m=Boolean(p&&e&&a),v=b(l)?l({isDrawer:m}):l;return m?n.createElement(c.DrawerManager,null,n.createElement(d.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,
position:a,"data-name":t},v)):n.createElement(s.PopupMenu,{reference:this.props.menuReference,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:p,minWidth:o,onClose:this._handleClose,position:(0,u.getPopupPositioner)(this._wrapperRef,h),className:r,maxHeight:i,"data-name":t,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus},v)}}function b(e){return"function"==typeof e}g.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:v}},20792:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_TOOLBAR_BUTTON_THEME:()=>s,ToolbarButton:()=>a});var n=o(50959),r=o(81348),i=o(50238);const s=r.DEFAULT_TOOL_WIDGET_BUTTON_THEME,a=(0,n.forwardRef)((function(e,t){const{tooltip:o,...s}=e,[a,l]=(0,i.useRovingTabindexElement)(t);return n.createElement(r.ToolWidgetButton,{"aria-label":o,...s,tag:"button",ref:a,tabIndex:l,"data-tooltip":o})}))},98945:(e,t,o)=>{"use strict";o.d(t,{ToolbarIconButton:()=>s});var n=o(50959),r=o(50238),i=o(56388);const s=(0,n.forwardRef)((function(e,t){const{tooltip:o,...s}=e,[a,l]=(0,r.useRovingTabindexElement)(t);return n.createElement(i.ToolWidgetIconButton,{"aria-label":o,...s,tag:"button",ref:a,tabIndex:l,"data-tooltip":o})}))},88811:(e,t,o)=>{"use strict";o.d(t,{ToolbarMenuButton:()=>d});var n=o(50959),r=o(39416),i=o(50238),s=o(7047),a=o(20626),l=o(20243);const c=(0,n.forwardRef)((function(e,t){const{tooltip:o,tag:i,buttonRef:s,reference:c,...d}=e,u=(0,r.useFunctionalRefObject)(null!=c?c:null);return n.createElement(a.ToolWidgetMenu,{"aria-label":o,...d,ref:t,tag:null!=i?i:"button",reference:null!=s?s:u,"data-tooltip":o,onMenuKeyDown:l.handleAccessibleMenuKeyDown,onMenuFocus:e=>(0,l.handleAccessibleMenuFocus)(e,null!=s?s:u)})})),d=(0,n.forwardRef)((function(e,t){const{tooltip:o,menuReference:a=null,...l}=e,[d,u]=(0,i.useRovingTabindexElement)(null),p=(0,r.useFunctionalRefObject)(a);return n.createElement(c,{"aria-label":o,...s.MouseClickAutoBlurHandler.attributes(),...l,ref:t,tag:"button",buttonRef:d,tabIndex:u,menuReference:p,tooltip:o})}))},77151:(e,t,o)=>{"use strict";o.d(t,{RegistryProvider:()=>l,registryContextType:()=>c,validateRegistry:()=>a});var n=o(50959),r=o(19036),i=o.n(r);const s=n.createContext({});function a(e,t){i().checkPropTypes(t,e,"context","RegistryContext")}function l(e){const{validation:t,value:o}=e;return a(o,t),n.createElement(s.Provider,{value:o},e.children)}function c(){return s}},60925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},578:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},25931:(e,t,o)=>{"use strict";o.d(t,{nanoid:()=>n});let n=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);