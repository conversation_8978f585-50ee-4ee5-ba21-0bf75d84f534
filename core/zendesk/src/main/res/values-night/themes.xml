<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Zendesk" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Customize  theme Zendesk -->
        <item name="colorPrimary">@color/onSurfaceVariant</item>
        <item name="colorAccent">@color/onSurfaceVariant</item>
        <item name="alertDialogTheme">@style/AlertDialogStyle</item>
        <!--        status bar dark mode -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:statusBarColor">@color/schemesBackground</item>

    </style>

    <style name="ThemeOverlay.App.Toolbar" parent="ThemeOverlay.AppCompat.ActionBar">
        <!-- color used by navigation icon and overflow icon -->
        <item name="colorOnPrimary">@color/schemesOnSurface</item>
        <item name="navigationIcon">@drawable/ic_arrow_left_android_24</item>
        <item name="navigationIconTint">@color/schemesOnSurface</item>
        <item name="android:textColorSecondary">@color/schemesOnSurface</item>
        <item name="android:textColorPrimary">@color/schemesOnSurface</item>
        <item name="tint">@color/schemesOnSurface</item>
    </style>

    <style name="AlertDialogStyle" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:colorAccent">@color/schemesSurface</item>
        <item name="android:textColor">@color/schemesOnSurface</item>
        <item name="android:textColorPrimary">@color/schemesOnSurface</item>
        <item name="buttonBarNegativeButtonStyle">@style/NegativeButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/PositiveButtonStyle</item>
    </style>

    <style name="NegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/schemesOnSurface</item>
    </style>

    <style name="PositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/schemesOnSurface</item>
    </style>

    <style name="zs_contact_us_fab">
        <item name="android:gravity">bottom|end</item>
        <item name="android:layout_margin">@dimen/zs_fab_margin</item>
        <item name="android:contentDescription">@string/zg_general_contact_us_button_label_accessibility</item>
        <item name="android:visibility">gone</item>
        <item name="colorSecondary">@color/schemesBackground</item>
        <item name="colorOnSecondary">@color/schemesBackground</item>
        <item name="srcCompat">@drawable/zs_create_ticket_fab</item>
        <item name="backgroundTint">@color/schemesBackground</item>
        <item name="rippleColor">@color/schemesBackground</item>
        <item name="tint">@color/schemesOnSurface</item>
    </style>
</resources>
