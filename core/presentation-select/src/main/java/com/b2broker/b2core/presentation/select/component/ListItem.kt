package com.b2broker.b2core.presentation.select.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.component.B2IconArrowDown
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.imageloader.B2CurrencyImage

@Composable
public fun FoundListItem(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    leadingContent: @Composable (RowScope.() -> Unit)? = null,
    middleContent: @Composable (RowScope.() -> Unit)? = null,
    trailingContent: @Composable (RowScope.() -> Unit)? = null,
) {
    val alphaItem = if (enabled) 1f else 0.5.toFloat()
    Row(
        modifier = modifier
            .alpha(alphaItem)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (leadingContent != null) {
            Row(content = leadingContent)
        }
        if (middleContent != null) {
            Row(
                modifier = Modifier.weight(1f),
                content = middleContent,
            )
        }
        if (trailingContent != null) {
            Row(content = trailingContent)
        }
    }
}

@Composable
public fun MiddleListItemContent(
    modifier: Modifier = Modifier,
    subTitle: String = "",
    title: String = "",
    holderText: String = "",
    enabled: Boolean = true,
) {
    Column(modifier = modifier) {
        if (title.isNotEmpty()) {
            Text(
                text = title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = if (enabled) {
                    B2Theme.colors.onSurface
                } else {
                    B2Theme.colors.onSurfaceVariant
                },
                style = B2Theme.typography.bodyLarge,
                modifier = Modifier.testTag(ListItemTestTags.CURRENCY_ALPHABETIC_CODE),
            )
            if (subTitle.isNotEmpty()) {
                Text(
                    text = subTitle,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = B2Theme.colors.onSurfaceVariant,
                    style = B2Theme.typography.bodyLarge,
                    modifier = Modifier.testTag(ListItemTestTags.CURRENCY_NAME),
                )
            }
        } else {
            Text(
                text = holderText,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = B2Theme.colors.onSurfaceVariant,
                style = B2Theme.typography.titleMedium,
                modifier = Modifier.testTag(ListItemTestTags.EMPTY_STATE_ITEM),
            )
        }
    }
}

object ListItemTestTags {
    const val CURRENCY_ALPHABETIC_CODE = "CurrencyAlphabeticCode"
    const val CURRENCY_NAME = "CurrencyName"
    const val EMPTY_STATE_ITEM = "EmptyStateItem"
}

@PreviewLightDark
@Composable
private fun DepositListItemPreview() = PreviewContainer(
    itemSpacing = 16.dp,
) {
    FoundListItem(
        leadingContent = {
            B2CurrencyImage(alphabeticCode = "BTC")
            Spacer(modifier = Modifier.padding(start = 8.dp))
        },
        modifier = Modifier.padding(all = 12.dp),
        middleContent = {
            MiddleListItemContent(
                title = "title",
                subTitle = "subtitle",
                enabled = false,
            )
        },
        enabled = false,
        trailingContent = { B2IconArrowDown() },
    )

    FoundListItem(
        leadingContent = {
            B2CurrencyImage(alphabeticCode = "BTC")
            Spacer(modifier = Modifier.padding(start = 8.dp))
        },
        middleContent = { MiddleListItemContent(title = "title") },
        modifier = Modifier.padding(all = 20.dp),
        trailingContent = { B2IconArrowDown() },
    )

    FoundListItem(
        leadingContent = {
            B2CurrencyImage(alphabeticCode = "BTC")
            Spacer(modifier = Modifier.padding(start = 8.dp))
        },
        middleContent = { MiddleListItemContent(title = "title") },
        modifier = Modifier.padding(all = 8.dp),
    )

    FoundListItem(
        middleContent = { MiddleListItemContent(title = "title") },
        modifier = Modifier
            .defaultMinSize(minHeight = 64.dp),
    )

    FoundListItem(
        middleContent = { MiddleListItemContent(holderText = "holder") },
        modifier = Modifier
            .defaultMinSize(minHeight = 64.dp),
        trailingContent = { B2IconArrowDown() },
    )
}
