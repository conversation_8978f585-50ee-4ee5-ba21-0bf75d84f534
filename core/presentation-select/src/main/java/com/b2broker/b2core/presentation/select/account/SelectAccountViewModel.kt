package com.b2broker.b2core.presentation.select.account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.b2broker.b2core.domain.selectaccount.ProcessAccountsUseCase
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.accounts.AccountGroup
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.state.UiStateHolder
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = SelectAccountViewModel.Factory::class)
internal class SelectAccountViewModel @AssistedInject constructor(
    private val uiStateHolder: UiStateHolder<SelectAccountUiState>,
    private val processAccountsUseCase: ProcessAccountsUseCase,
    @Assisted private val accountsInitial: List<Account>,
    @Assisted private val queryMinLen: Int,
    eventDispatcher: EventDispatcher,
) : ViewModel(), UiStateHolder<SelectAccountUiState> by uiStateHolder, EventDispatcher by eventDispatcher {

    @AssistedFactory
    interface Factory {
        fun create(
            accounts: List<Account>,
            queryMinLen: Int,
        ): SelectAccountViewModel
    }

    init {
        viewModelScope.launch {
            val resultGroup = processAccountsUseCase(accountsInitial)
            updateState {
                copy(
                    accounts = resultGroup.toImmutableMap(),
                    accountsGroup = resultGroup.toImmutableMap(),
                )
            }
        }
    }

    fun onAccountClick(account: Account) {
        dispatch(SelectAccountEvent.SelectAccount(account.id))
    }

    fun onQueryChanged(newQuery: String) {
        if (newQuery.length < queryMinLen) {
            updateState {
                copy(
                    query = newQuery,
                    accounts = getAccountGroup(),
                )
            }
            return
        }
        val filteredMap = mutableMapOf<AccountGroup, List<Account>>()
        uiState.accountsGroup.forEach { (key, values) ->
            filteredMap[key] = values.filter { account ->
                val alphabeticCode = account.currency.alphabeticCode
                val name = account.number
                val balance = account.statement
                    ?.availableBalance
                    ?.toPlainString()
                    .orEmpty()
                listOf(alphabeticCode, name, balance).any { it.contains(newQuery, ignoreCase = true) }
            }
        }
        if (uiState.groupCurrent != null) {
            filteredMap.keys.removeIf { it != uiState.groupCurrent }
        }
        updateState {
            copy(
                query = newQuery,
                accounts = filteredMap.toImmutableMap(),
            )
        }
    }

    private fun onClear() {
        updateState {
            copy(
                query = "",
                accounts = getAccountGroup(),
            )
        }
    }

    fun onSelectGroup(group: AccountGroup) {
        var groupCurrent: AccountGroup? = group
        if (groupCurrent == uiState.groupCurrent) {
            groupCurrent = null
        }
        updateState {
            copy(
                groupCurrent = groupCurrent,
            )
        }
        onQueryChanged(uiState.query)
    }

    private fun getAccountGroup(): ImmutableMap<AccountGroup, List<Account>> = if (uiState.groupCurrent == null) {
        uiState.accountsGroup
    } else {
        uiState.accountsGroup
            .filterKeys {
                it == uiState.groupCurrent
            }
            .toImmutableMap()
    }

    fun onSearchClick() {
        val active = uiState.isSearchActive
        updateState {
            copy(
                isSearchActive = !active,
            )
        }
    }

    fun onCloseSearchClick() {
        val active = uiState.isSearchActive
        updateState {
            copy(
                isSearchActive = !active,
            )
        }
        onClear()
    }
}
