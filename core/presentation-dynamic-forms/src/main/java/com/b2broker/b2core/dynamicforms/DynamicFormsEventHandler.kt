package com.b2broker.b2core.dynamicforms

import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.dynamicforms.select.OptionSelectRoute
import com.b2broker.b2core.model.dynamicforms.state.MultiselectOptionState
import com.b2broker.b2core.model.search.SelectItem
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.navigation.push
import com.b2broker.b2core.navigation.result.contract.DatePickerContract
import com.b2broker.b2core.navigation.result.contract.DynamicMultiSelectContract
import com.b2broker.b2core.navigation.result.contract.SearchPhoneCountryContract
import com.b2broker.b2core.navigation.result.contract.SelectOptionContract
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.handler.EventHandler
import javax.inject.Inject

public class DynamicFormsEventHandler @Inject constructor(
    private val navigator: Navigator,
) : EventHandler() {

    override fun handle(event: Event) {
        when (event) {
            DynamicFormsDelegateEvent.SelectCountryPhoneCode -> {
                val route = SharedRoute.SearchPhoneCountry(
                    SearchPhoneCountryContract.Request(screenKey = navigator.lastItem.key),
                )
                navigator.push(route)
            }

            is DynamicFormsDelegateEvent.SelectOption -> {
                val route = OptionSelectRoute(
                    request = SelectOptionContract.Request(
                        screenKey = navigator.lastItem.key,
                    ),
                    options = event.options,
                    statePosition = event.statePosition,
                )
                navigator.push(route)
            }

            is DynamicFormsDelegateEvent.SelectDate -> {
                val request = DatePickerContract.Request(
                    screenKey = navigator.lastItem.key,
                    initialDateMillis = event.initialDate,
                    minDateMillis = event.minDate,
                    maxDateMillis = event.maxDate,
                )
                navigator.push(SharedRoute.DatePicker(request))
            }

            is DynamicFormsDelegateEvent.MultiSelectOption -> {
                val items = event.options.map(::toSelectItem)
                val selectedItems = event.options
                    .filter { it.isSelected }
                    .map(::toSelectItem)
                val request = DynamicMultiSelectContract.Request(
                    items = items,
                    selectedItems = selectedItems.toSet(),
                    allowEmptySelect = true,
                    screenKey = navigator.lastItem.key,
                )
                navigator.push(SharedRoute.MultiSelectOptions(request))
            }

            else -> super.handle(event)
        }
    }

    private fun toSelectItem(state: MultiselectOptionState): SelectItem {
        return SelectItem(
            name = state.name,
            value = state.value,
        )
    }
}
