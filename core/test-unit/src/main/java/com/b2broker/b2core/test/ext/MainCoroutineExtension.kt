package com.b2broker.b2core.test.ext

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.extension.AfterEachCallback
import org.junit.jupiter.api.extension.BeforeEachCallback
import org.junit.jupiter.api.extension.ExtensionContext

@Deprecated(
    message = "Use MainDispatcherTest(type = Unconfined/Standard) for consistent coroutine testing with Dispatchers.Main.",
    replaceWith = ReplaceWith("MainDispatcherTest(type = Unconfined/Standard)")
)
public class MainCoroutineUnconfinedExtension : BeforeEachCallback, AfterEachCallback {

    public lateinit var scheduler: TestCoroutineScheduler
        private set
    public lateinit var dispatcher: TestDispatcher
        private set

    override fun beforeEach(context: ExtensionContext?) {
        scheduler = TestCoroutineScheduler()
        dispatcher = UnconfinedTestDispatcher(scheduler)
        Dispatchers.setMain(dispatcher)
    }

    override fun afterEach(context: ExtensionContext?) {
        Dispatchers.resetMain()
    }
}

@Deprecated(
    message = "Use MainDispatcherTest(type = Unconfined/Standard) for consistent coroutine testing with Dispatchers.Main.",
    replaceWith = ReplaceWith("MainDispatcherTest(type = Unconfined/Standard)")
)
public class MainCoroutineStandardExtension : BeforeEachCallback, AfterEachCallback {

    public lateinit var scheduler: TestCoroutineScheduler
        private set
    public lateinit var dispatcher: TestDispatcher
        private set

    override fun beforeEach(context: ExtensionContext?) {
        scheduler = TestCoroutineScheduler()
        dispatcher = StandardTestDispatcher(scheduler)
        Dispatchers.setMain(dispatcher)
    }

    override fun afterEach(context: ExtensionContext?) {
        Dispatchers.resetMain()
    }
}
