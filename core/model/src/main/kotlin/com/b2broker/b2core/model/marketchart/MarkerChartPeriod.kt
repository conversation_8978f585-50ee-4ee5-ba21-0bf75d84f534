package com.b2broker.b2core.model.marketchart

import java.util.concurrent.TimeUnit

enum class MarkerChartPeriod {
    OneMinute {
        override val inSeconds = TimeUnit.MINUTES.toSeconds(1)
        override fun toString() = "1m"
    },
    FiveMinutes {
        override val inSeconds = TimeUnit.MINUTES.toSeconds(5)
        override fun toString() = "5m"
    },
    FifteenMinutes {
        override val inSeconds = TimeUnit.MINUTES.toSeconds(15)
        override fun toString() = "15m"
    },
    ThirtyMinutes {
        override val inSeconds = TimeUnit.MINUTES.toSeconds(30)
        override fun toString() = "30m"
    },
    OneHour {
        override val inSeconds = TimeUnit.HOURS.toSeconds(1)
        override fun toString() = "1H"
    },
    FourHours {
        override val inSeconds = TimeUnit.HOURS.toSeconds(4)
        override fun toString() = "4H"
    },
    TwelveHours {
        override val inSeconds = TimeUnit.HOURS.toSeconds(12)
        override fun toString() = "12H"
    },
    OneDay {
        override val inSeconds = TimeUnit.DAYS.toSeconds(1)
        override fun toString() = "1D"
    },
    OneWeek {
        override val inSeconds = TimeUnit.DAYS.toSeconds(7)
        override fun toString() = "1W"
    },
    OneMonth {
        override val inSeconds = TimeUnit.DAYS.toSeconds(30)
        override fun toString() = "1M"
    };

    abstract val inSeconds: Long
}
