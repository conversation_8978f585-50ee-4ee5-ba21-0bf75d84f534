package com.b2broker.b2core.model.b2trader.orders

import com.b2broker.b2core.model.b2trader.trigger.TriggerRequest
import java.math.BigDecimal

sealed interface CalculateOrder {

    val marketId: String
    val orderType: OrderType
    val side: OrderSide

    data class CalculateCfdOrder(
        override val marketId: String,
        override val orderType: OrderType,
        override val side: OrderSide,
        val requestedPrice: BigDecimal?,
        val requestedLotAmount: BigDecimal,
        val leverage: Int,
        val takeProfit: TriggerRequest? = null,
        val stopLoss: TriggerRequest? = null,
        val isPerpetual: Boolean,
    ) : CalculateOrder {
        companion object {
            fun fixture(
                marketId: String = "cfd.btc_usd",
                orderType: OrderType = OrderType.LIMIT,
                side: OrderSide = OrderSide.BUY,
                requestedPrice: BigDecimal = BigDecimal("100.0"),
                requestedLotAmount: BigDecimal = BigDecimal("1.0"),
                leverage: Int = 10,
                takeProfit: TriggerRequest? = null,
                stopLoss: TriggerRequest? = null,
                isPerpetual: Boolean = false,
            ) = CalculateCfdOrder(
                marketId = marketId,
                orderType = orderType,
                side = side,
                requestedPrice = requestedPrice,
                requestedLotAmount = requestedLotAmount,
                leverage = leverage,
                takeProfit = takeProfit,
                stopLoss = stopLoss,
                isPerpetual = isPerpetual,
            )
        }
    }

    data class CalculateSpotOrder(
        override val marketId: String,
        override val orderType: OrderType,
        override val side: OrderSide,
        val requestedPrice: BigDecimal?,
        val requestedBaseAmount: BigDecimal,
    ) : CalculateOrder {

        companion object {
            fun fixture(
                marketId: String = "spot.btc_usd",
                orderType: OrderType = OrderType.MARKET,
                side: OrderSide = OrderSide.BUY,
                requestedPrice: BigDecimal = BigDecimal("100.0"),
                requestedBaseAmount: BigDecimal = BigDecimal("100.0"),
            ) = CalculateSpotOrder(
                marketId = marketId,
                orderType = orderType,
                side = side,
                requestedPrice = requestedPrice,
                requestedBaseAmount = requestedBaseAmount,
            )
        }
    }
}
