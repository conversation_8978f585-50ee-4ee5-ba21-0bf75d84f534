package com.b2broker.b2core.model.products

sealed class ProductsGettingResult {
    data class Products(val products: List<ProductCurrency>) : ProductsGettingResult() {
        val groups = products.map(ProductCurrency::group).distinct()
        val groupedProducts = products.groupBy(ProductCurrency::group)
    }

    data class Empty(val reason: ProductsGettingEmptyReason) : ProductsGettingResult()
}
