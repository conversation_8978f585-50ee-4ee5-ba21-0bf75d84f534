package com.b2broker.b2core.model.ibprogram

import kotlinx.datetime.Instant
import java.math.BigDecimal

data class IbPayment(
    val clientId: String,
    val paymentNumber: String? = null,
    val paymentAmount: BigDecimal,
    val paymentCurrencyCode: String,
    val paymentCurrencySign: String? = null,
    val paymentLevel: Int,
    val paymentState: String,
    val platformName: String,
    val symbolName: String,
    val tradeAccountIdentifier: String,
    val tradeIdentifier: String,
    val tradeSide: IbPaymentSide,
    val tradingVolume: BigDecimal,
    val tradeTime: Instant,
    val transactionIdentifier: String
) {
    companion object {
        fun fixture(
            clientId: String = "db0882de-d05a-4940-9a35-0fc5694951e6",
            paymentNumber: String? = "paymentNumber_a130",
            paymentAmount: BigDecimal = BigDecimal(401),
            paymentCurrencyCode: String = "12345",
            paymentCurrencySign: String? = null,
            paymentLevel: Int = 113,
            paymentState: String = "paymentState_0796",
            platformName: String = "platformName_4b32",
            symbolName: String = "symbolName_e9da",
            tradeAccountIdentifier: String = "tradeAccountIdentifier_0334",
            tradeIdentifier: String = "tradeIdentifier_b0a2",
            tradeSide: IbPaymentSide = IbPaymentSide.SELL,
            tradingVolume: BigDecimal = BigDecimal(12),
            tradeTime: Instant = Instant.fromEpochMilliseconds(1_731_314_298_070L),
            transactionIdentifier: String = "transactionIdentifier_20bc",
        ) = IbPayment(
            clientId = clientId,
            paymentNumber = paymentNumber,
            paymentAmount = paymentAmount,
            paymentCurrencyCode = paymentCurrencyCode,
            paymentCurrencySign = paymentCurrencySign,
            paymentLevel = paymentLevel,
            paymentState = paymentState,
            platformName = platformName,
            symbolName = symbolName,
            tradeAccountIdentifier = tradeAccountIdentifier,
            tradeIdentifier = tradeIdentifier,
            tradeSide = tradeSide,
            tradingVolume = tradingVolume,
            tradeTime = tradeTime,
            transactionIdentifier = transactionIdentifier,
        )
    }
}

enum class IbPaymentSide(val value: String) {
    BUY(value = "buy"),
    SELL(value = "sell"),
    UNKNOWN(value = "unknown");

    companion object {

        fun fromString(value: String): IbPaymentSide {
            return IbPaymentSide.entries.find { it.value.lowercase() == value.lowercase() } ?: UNKNOWN
        }
    }
}
