package com.b2broker.b2core.model.accountsecurity

data class AccountSecurityState(
    val twoFaState: TwoFactorAuthenticationState,
    val antiPhishingState: AntiPhishingState,
    val whitelistState: WhitelistState,
    val loginSecurityState: LoginSecurityState,
) {

    data class TwoFactorAuthenticationState(
        val google2Fa: TwoFactorMethodState,
        val sms2Fa: TwoFactorMethodState,
    )

    enum class TwoFactorMethodState(val isEnabled: Boolean?) {
        UNAVAILABLE(null),
        ENABLED(true),
        DISABLED(false);

        companion object {
            fun valueOf(isEnabled: Boolean?): TwoFactorMethodState = when {
                isEnabled == null -> UNAVAILABLE
                isEnabled -> ENABLED
                else -> DISABLED
            }
        }
    }

    sealed interface AntiPhishingState {
        data object TwoFactorRequired : AntiPhishingState
        data object Disabled : AntiPhishingState
        data class Enabled(val code: String) : AntiPhishingState
        data object Review : AntiPhishingState
    }

    sealed interface WhitelistState {
        data object TwoFactorRequired : WhitelistState
        data object Enabled : WhitelistState
        data object Disabled : WhitelistState
        data object Process : WhitelistState
        data object Invisible : WhitelistState
    }

    data class LoginSecurityState(
        val biometricState: LoginSecurityMethodState,
        val pinCodeState: LoginSecurityMethodState,
        val showDisableBiometricDialog: Boolean = false
    ) {
        val isVisible = biometricState != LoginSecurityMethodState.Hidden || pinCodeState != LoginSecurityMethodState.Hidden

        companion object {
            fun fixture(
                biometricState: LoginSecurityMethodState = LoginSecurityMethodState.Hidden,
                pinCodeState: LoginSecurityMethodState = LoginSecurityMethodState.Hidden,
            ) = LoginSecurityState(
                biometricState = biometricState,
                pinCodeState = pinCodeState,
            )
        }
    }

    sealed interface LoginSecurityMethodState {
        data object Enabled : LoginSecurityMethodState
        data object Disabled : LoginSecurityMethodState
        data object Process : LoginSecurityMethodState
        data object Hidden : LoginSecurityMethodState
    }

    companion object {
        fun fixture(
            twoFaState: TwoFactorAuthenticationState = TwoFactorAuthenticationState(
                google2Fa = TwoFactorMethodState.DISABLED,
                sms2Fa = TwoFactorMethodState.DISABLED,
            ),
            antiPhishingState: AntiPhishingState = AntiPhishingState.Disabled,
            whitelistState: WhitelistState = WhitelistState.Disabled,
            loginSecurityState: LoginSecurityState = LoginSecurityState.fixture()
        ) = AccountSecurityState(
            twoFaState = twoFaState,
            antiPhishingState = antiPhishingState,
            whitelistState = whitelistState,
            loginSecurityState = loginSecurityState,
        )
    }
}
