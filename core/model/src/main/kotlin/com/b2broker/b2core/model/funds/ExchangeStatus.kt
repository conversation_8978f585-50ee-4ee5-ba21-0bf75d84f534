package com.b2broker.b2core.model.funds

import android.os.Parcelable
import com.b2broker.b2core.text.TextResource
import kotlinx.parcelize.Parcelize

@Parcelize
sealed class ExchangeStatus : Parcelable {
    data class Success(val convert: Convert) : ExchangeStatus()
    data class Pending(val convert: Convert) : ExchangeStatus()
    data class Failed(val message: TextResource? = null) : ExchangeStatus()
}
