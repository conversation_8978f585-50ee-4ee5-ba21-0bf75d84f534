package com.b2broker.b2core.model.b2trader.account

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class B2TraderAccount(
    val id: String,
    val name: String,
    val type: B2TraderAccountType,
    val totalBalanceInRAT: BigDecimal
) : Parcelable {
    companion object {
        fun fixture(
            id: String = "66c87347c0412061faadd3c7",
            name: String = "trader-account",
            type: B2TraderAccountType = B2TraderAccountType.HEDGING,
            totalBalanceInRAT: BigDecimal = BigDecimal.ZERO,
        ) = B2TraderAccount(
            id = id,
            name = name,
            type = type,
            totalBalanceInRAT = totalBalanceInRAT,
        )
    }
}
