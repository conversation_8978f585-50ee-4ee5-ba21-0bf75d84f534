package com.b2broker.b2core.model.ibprogram

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class IbDashboardInfo(
    val programsAvailable: Boolean,
    val programs: List<IbDashboardProgram>
) : Parcelable {
    companion object {
        fun fixture(
            programsAvailable: Boolean = false,
            programs: List<IbDashboardProgram> = emptyList(),
        ) = IbDashboardInfo(
            programsAvailable = programsAvailable,
            programs = programs,
        )
    }
}

@Parcelize
data class IbDashboardProgram(
    val programId: String,
    val title: String,
    val referralLink: IbLink,
    val countOfUsers: Int = 0
) : Parcelable {
    companion object {
        fun fixture(
            userId: String = "3e135f3a-205a-4f8d-9ec7-5fc2b2412220",
            title: String = "title_fd1f",
            referralLink: IbLink = IbLink.fixture(),
            countOfUsers: Int = 402,
        ) = IbDashboardProgram(
            programId = userId,
            title = title,
            referralLink = referralLink,
            countOfUsers = countOfUsers,
        )
    }
}
