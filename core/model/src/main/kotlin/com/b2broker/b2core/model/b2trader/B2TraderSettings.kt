package com.b2broker.b2core.model.b2trader

import com.b2broker.b2core.model.marketchart.MarketsChartType

data class B2TraderSettings(
    val areZeroAssetsHidden: Boolean,
    val marketChartType: MarketsChartType,
    val marketChartVisible: Boolean,
    val marketChartPnlVisible: Boolean,
    val marketChartTriggersVisible: Boolean,
    val marketChartOpenOrdersVisible: Boolean,
    val marketChartExecutedOrdersVisible: Boolean = true,
) {

    companion object {
        fun fixture(
            areZeroAssetsHidden: Boolean = true,
            marketChartType: MarketsChartType = MarketsChartType.Candlestick,
            marketChartVisible: Boolean = true,
            marketChartPnlVisible: Boolean = true,
            marketChartTriggersVisible: Boolean = true,
            marketChartOpenOrdersVisible: Boolean = true,
            marketChartExecutedOrdersVisible: Boolean = false,
        ) = B2TraderSettings(
            areZeroAssetsHidden = areZeroAssetsHidden,
            marketChartType = marketChartType,
            marketChartVisible = marketChartVisible,
            marketChartPnlVisible = marketChartPnlVisible,
            marketChartTriggersVisible = marketChartTriggersVisible,
            marketChartOpenOrdersVisible = marketChartOpenOrdersVisible,
            marketChartExecutedOrdersVisible = marketChartExecutedOrdersVisible,
        )
    }
}
