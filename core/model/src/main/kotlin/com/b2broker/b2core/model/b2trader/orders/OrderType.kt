package com.b2broker.b2core.model.b2trader.orders

import com.b2broker.b2core.text.CoreStrings
import java.util.Locale

enum class OrderType {
    MARKET,
    LIMIT;

    companion object {
        val OrderType.stringRes: Int
            get() = when (this) {
                MARKET -> CoreStrings.b2trader_order_type_market
                LIMIT -> CoreStrings.b2trader_order_type_limit
            }

        val OrderType.stopOrderStringRes: Int
            get() = when (this) {
                MARKET -> CoreStrings.b2trader_order_type_stop_market
                LIMIT -> CoreStrings.b2trader_order_type_stop_limit
            }

        fun fromStringOrNull(type: String): OrderType? = when (type.uppercase(Locale.US)) {
            "LIMIT" -> LIMIT
            "MARKET" -> MARKET
            else -> null
        }

        fun OrderType.toApiParam(): String = when (this) {
            LIMIT -> "LIMIT"
            MARKET -> "MARKET"
        }
    }
}
