package com.b2broker.b2core.devsettings

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.devsettings.dynhamicforms.DynamicFormsPlaygroundRoute
import com.b2broker.b2core.devsettings.standswitch.StandSwitchRoute
import com.b2broker.b2core.devsettings.toggles.TogglesRoute
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.uniqueScreenKey
import kotlinx.parcelize.Parcelize

@Parcelize
internal class DevSettingsRoute(
    override val key: ScreenKey = uniqueScreenKey(),
) : Route {

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow

        DevSettingsScreen(
            onBackClick = navigator::pop,
            onTogglesClick = { navigator.push(TogglesRoute()) },
            onStandSwitchClick = { navigator.push(StandSwitchRoute()) },
            onDynamicFormsPlaygroundClick = { navigator.push(DynamicFormsPlaygroundRoute()) },
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
        )
    }
}
