package com.b2broker.b2core.devsettings.standswitch

import com.b2broker.b2core.domain.standswitch.GetAllStandsUseCase
import com.b2broker.b2core.domain.standswitch.GetCurrentStandUseCase
import com.b2broker.b2core.domain.standswitch.GetProdBaseUrlUseCase
import com.b2broker.b2core.domain.standswitch.SetCurrentStandUseCase
import com.b2broker.b2core.model.BaseUrl
import com.b2broker.b2core.model.stand.StandConfiguration
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.google.common.truth.Truth
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class StandSwitchViewModelTest {
    private val mockProdUrl = BaseUrl.fixture()
    private val mockCurrentStage = StandConfiguration(
        "mockCurrentStage name",
        "mockCurrentStage url",
    )
    private val mockStageStands = listOf(
        StandConfiguration("mockStageStands name", "mockStageStands url"),
    )

    private val getProdBaseUrlUseCase = mockk<GetProdBaseUrlUseCase>()
    private val getCurrentStandUseCase = mockk<GetCurrentStandUseCase>()
    private val getAllStandsUseCase = mockk<GetAllStandsUseCase>()
    private val setCurrentStandUseCase = mockk<SetCurrentStandUseCase>()
    private val defaultUiStateHolder = DefaultUiStateHolder(StandSwitchUiState())
    private val eventDispatcher = mockk<EventDispatcher>(relaxed = true)

    private lateinit var viewModel: StandSwitchViewModel

    @BeforeEach
    fun setUp() {
        coEvery { getProdBaseUrlUseCase() } returns mockProdUrl
        coEvery { setCurrentStandUseCase(any()) } returns Unit
        coEvery { getCurrentStandUseCase() } returns mockCurrentStage
        coEvery { getAllStandsUseCase() } returns mockStageStands
    }

    @Test
    fun `WHEN vm initialized THEN test stage urls value`() = runTest {
        // WHEN
        viewModel = createViewModel()
        advanceUntilIdle()
        val actualProdUrl = viewModel.uiState.prodUrl
        val actualStageStands = viewModel.uiState.stageStands
        val actualSelectedStageStand = viewModel.uiState.selectedStageStand

        // THEN
        Truth.assertThat(actualProdUrl).isEqualTo(mockProdUrl)
        Truth.assertThat(actualStageStands).isEqualTo(mockStageStands)
        Truth.assertThat(actualSelectedStageStand).isEqualTo(mockCurrentStage)
    }

    @Test
    fun `GIVEN closed dialog WHEN on change stage url click THEN dialog open`() = runTest {
        // GIVEN
        viewModel = createViewModel()
        Truth.assertThat(viewModel.uiState.showChooseStageStandDialog).isEqualTo(false)

        // WHEN
        viewModel.onChangeStageUrlClick()

        // THEN
        Truth.assertThat(viewModel.uiState.showChooseStageStandDialog).isEqualTo(true)
    }

    @Test
    fun `GIVEN view model WHEN on choose stage url click THEN stage url is set`() = runTest {
        // GIVEN
        viewModel = createViewModel()

        // WHEN
        val selectedStand = StandConfiguration("name", "url")
        viewModel.onChooseStageStandClick(selectedStand)

        // THEN
        Truth.assertThat(viewModel.uiState.selectedStageStand).isEqualTo(selectedStand)
    }

    @Test
    fun `GIVEN dialog is open WHEN dismiss dialog THEN dialog closes`() = runTest {
        // GIVEN
        viewModel = createViewModel(
            DefaultUiStateHolder(
                StandSwitchUiState(
                    prodUrl = null,
                    showChooseStageStandDialog = true,
                ),
            ),
        )

        // WHEN
        viewModel.onDialogDismissRequest()

        // THEN
        Truth.assertThat(viewModel.uiState.showChooseStageStandDialog).isEqualTo(false)
    }

    private fun TestScope.createViewModel(
        uiStateHolder: DefaultUiStateHolder<StandSwitchUiState> = defaultUiStateHolder,
    ): StandSwitchViewModel {
        val testDispatcher = UnconfinedTestDispatcher(testScheduler)
        Dispatchers.setMain(testDispatcher)

        return StandSwitchViewModel(
            getProdBaseUrlUseCase = getProdBaseUrlUseCase,
            getCurrentStandUseCase = getCurrentStandUseCase,
            getAllStandsUseCase = getAllStandsUseCase,
            setCurrentStandUseCase = setCurrentStandUseCase,
            uiStateHolder = uiStateHolder,
            eventDispatcher = eventDispatcher,
        )
    }
}
