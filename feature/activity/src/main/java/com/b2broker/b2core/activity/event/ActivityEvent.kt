package com.b2broker.b2core.activity.event

import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.model.transaction.Transaction
import com.b2broker.b2core.presentation.event.Event

internal sealed interface ActivityEvent : Event {
    data class OpenSelectCurrencies(
        val currencies: Set<Currency>,
        val selectedCurrency: Currency?,
    ) : ActivityEvent
    data class OpenTransactionDetails(val transaction: Transaction) : ActivityEvent
    data class OpenIbReports(val transaction: Transaction) : ActivityEvent
}
