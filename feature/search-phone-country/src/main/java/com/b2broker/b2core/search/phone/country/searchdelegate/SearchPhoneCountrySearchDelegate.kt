package com.b2broker.b2core.search.phone.country.searchdelegate

import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.search.delegate.BaseOfflineSearchDelegate
import com.b2broker.b2core.presentation.search.ui.B2SearchUiState
import com.b2broker.b2core.presentation.state.UiStateHolder
import com.b2broker.b2core.search.phone.country.event.SearchPhoneCountryEvent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject

internal class SearchPhoneCountrySearchDelegate @Inject constructor(
    eventDispatcher: EventDispatcher,
    @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
    uiStateHolder: UiStateHolder<B2SearchUiState<SearchPhoneCountrySearchItem>>,
) : BaseOfflineSearchDelegate<SearchPhoneCountrySearchItem>(
    uiStateHolder = uiStateHolder,
    eventDispatcher = eventDispatcher,
    defaultDispatcher = defaultDispatcher,
    showDividers = true,
    showCategoryHeaders = false,
) {

    override fun onConfirmClick() {
        val selectedItem = uiState.selectedItems.firstOrNull() ?: return
        dispatch(SearchPhoneCountryEvent.AcceptCountry(selectedItem.countryData))
    }
}
