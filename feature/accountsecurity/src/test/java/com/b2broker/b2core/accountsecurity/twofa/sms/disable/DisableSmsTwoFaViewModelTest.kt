package com.b2broker.b2core.accountsecurity.twofa.sms.disable

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.accountsecurity.twofa.sms.SmsTwoFaEvent
import com.b2broker.b2core.domain.accountsecurity.twofa.InitializeDisableSms2FaWizardUseCase
import com.b2broker.b2core.domain.accountsecurity.twofa.Submit2FaWithWizardUseCase
import com.b2broker.b2core.model.accountsecurity.twofa.InitializeDisable2FaWizardData
import com.b2broker.b2core.model.accountsecurity.twofa.TwoFaError
import com.b2broker.b2core.presentation.delegate.ResendCodeDelegate
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.default.BackEvent
import com.b2broker.b2core.presentation.event.default.SnackbarActionEvent
import com.b2broker.b2core.presentation.event.default.SnackbarMessageEvent
import com.b2broker.b2core.presentation.event.dispatcher.QueueEventDispatcher
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.b2broker.b2core.test.ext.MainCoroutineUnconfinedExtension
import com.b2broker.b2core.text.asTextResource
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MainCoroutineUnconfinedExtension::class)
class DisableSmsTwoFaViewModelTest {

    private val initializeDisableSms2FaWizardUseCase = mockk<InitializeDisableSms2FaWizardUseCase>()
    private val submit2FaWithWizardUseCase = mockk<Submit2FaWithWizardUseCase>()
    private val initializeDisable2FaWizardDataMock = InitializeDisable2FaWizardData.fixture()

    private lateinit var viewModel: DisableSmsTwoFaViewModel

    @BeforeEach
    fun setUp() {
        coEvery { initializeDisableSms2FaWizardUseCase() } returns initializeDisable2FaWizardDataMock.right()
        coEvery { submit2FaWithWizardUseCase(any(), any(), any()) } returns Unit.right()
    }

    @Test
    fun `GIVEN current vm WHEN vm initialized THEN initialized sms 2fa wizard and update state`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()

        // WHEN
        val actual = viewModel.uiState

        // THEN
        assertThat(actual.wizardUuid).isEqualTo(initializeDisable2FaWizardDataMock.uuid)
    }

    @Test
    fun `GIVEN current vm WHEN onBackClick THEN BackEvent`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()

        // WHEN
        viewModel.onBackClick()

        // THEN
        assertThat(viewModel.events.first()).isInstanceOf(BackEvent::class.java)
    }

    @Test
    fun `GIVEN current vm WHEN onContinueClick THEN send snackbar and feature disabled events`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()

        // WHEN
        val events = mutableListOf<Event>()
        val job = launch {
            viewModel.events.collectLatest {
                events.add(it)
            }
        }

        viewModel.onContinueClick()
        advanceUntilIdle()

        // THEN
        assertThat(events).hasSize(2)
        assertThat(events.first()).isInstanceOf(SnackbarActionEvent::class.java)
        assertThat(events[1]).isInstanceOf(SmsTwoFaEvent.Disabled::class.java)
        job.cancel()
    }

    @Test
    fun `GIVEN current vm WHEN onContinueClick THEN InvalidCode and update state`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()
        val error = TwoFaError.InvalidCode("Invalid code".asTextResource())

        coEvery { submit2FaWithWizardUseCase(any(), any(), any()) } returns error.left()

        // WHEN
        viewModel.onContinueClick()

        // THEN
        assertThat(viewModel.uiState.codeError).isEqualTo(error.message)
    }

    @Test
    fun `GIVEN current vm WHEN onContinueClick THEN SourceError and message event`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()
        val error = TwoFaError.SourceError("SourceError".asTextResource())

        coEvery { submit2FaWithWizardUseCase(any(), any(), any()) } returns error.left()

        // WHEN
        viewModel.onContinueClick()

        // THEN
        assertThat(viewModel.events.first()).isInstanceOf(SnackbarMessageEvent::class.java)
    }

    @Test
    fun `GIVEN current vm WHEN onCodeChanged THEN update state`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()
        val code = "123456"

        // WHEN
        viewModel.onCodeChanged(code)

        // THEN
        assertThat(viewModel.uiState.code).isEqualTo(code)
        assertThat(viewModel.uiState.codeError).isNull()
    }

    @Test
    fun `GIVEN current vm WHEN code length is equal REQUIRED_CODE_LENGTH and onCodeChanged THEN send snackbar and feature disabled events`() = runTest {
        // GIVEN
        viewModel = createDisableSmsTwoFaViewModel()
        val code = "12345"

        // WHEN
        val events = mutableListOf<Event>()
        val job = launch {
            viewModel.events.collectLatest {
                events.add(it)
            }
        }

        viewModel.onCodeChanged(code)
        advanceUntilIdle()

        // THEN
        assertThat(viewModel.uiState.code).isEqualTo(code)
        assertThat(viewModel.uiState.codeError).isNull()
        assertThat(events).hasSize(2)
        assertThat(events.first()).isInstanceOf(SnackbarActionEvent::class.java)
        assertThat(events[1]).isInstanceOf(SmsTwoFaEvent.Disabled::class.java)
        job.cancel()
    }

    private fun createDisableSmsTwoFaViewModel(
        uiState: DisableSmsTwoFaUiState = DisableSmsTwoFaUiState(),
    ): DisableSmsTwoFaViewModel {
        return DisableSmsTwoFaViewModel(
            uiStateHolder = createUiStateHolder(uiState),
            initializeDisableSms2FaWizardUseCase = initializeDisableSms2FaWizardUseCase,
            submit2FaWithWizardUseCase = submit2FaWithWizardUseCase,
            resendCodeDelegate = ResendCodeDelegate(),
            eventDispatcher = QueueEventDispatcher(),
        )
    }

    private fun createUiStateHolder(uiState: DisableSmsTwoFaUiState) = DefaultUiStateHolder(uiState)
}
