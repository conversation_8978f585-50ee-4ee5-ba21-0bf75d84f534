package com.b2broker.b2core.update.di

import com.b2broker.b2core.navigation.RouteApplier
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.update.UpdateRoute
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet

@Module
@InstallIn(SingletonComponent::class)
internal interface UpdateModule {

    companion object {
        @Provides
        @IntoSet
        fun provideRouteApplier(): RouteApplier {
            return { register<SharedRoute.AppUpdate> { UpdateRoute(it.update) } }
        }
    }
}
