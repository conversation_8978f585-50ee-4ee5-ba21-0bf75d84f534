package com.b2broker.b2core.trader.platform.accounts.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.theme.B2Theme

@Composable
internal fun RowScope.BalanceItem(
    title: String,
    value: String,
) {
    Column(Modifier.weight(1f)) {
        B2Text(
            modifier = Modifier.testTag(BalanceItemTestTags.getBalanceItemTitleTestTag(title)),
            text = title,
            style = B2Theme.typography.labelMedium,
            color = B2Theme.colors.onSurfaceVariant,
        )

        B2Text(
            modifier = Modifier.testTag(BalanceItemTestTags.getBalanceItemValueTestTag(title)),
            text = value,
            style = B2Theme.typography.labelLarge,
            color = B2Theme.colors.onSurface,
        )
    }
}

object BalanceItemTestTags {
    fun getBalanceItemTitleTestTag(name: String) = "Title_$name"
    fun getBalanceItemValueTestTag(name: String) = "Value_$name"
}
