package com.b2broker.b2core.trader.platform.details.settings.archive

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.navigation.result.OnResult
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.event.handler.rememberEventHandler
import com.b2broker.b2core.presentation.getEventfullViewModel
import com.b2broker.b2core.presentation.uniqueScreenKey
import com.b2broker.b2core.trader.platform.details.settings.archive.event.ArchiveAccountEventHandler
import com.b2broker.b2core.trader.platform.details.settings.archive.select.FallbackAccountContract
import kotlinx.parcelize.Parcelize

@Parcelize
internal class ArchiveAccountRoute(
    private val request: ArchiveAccountContract.Request,
    override val key: ScreenKey = uniqueScreenKey(),
) : Route {

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow
        val eventHandler = rememberEventHandler { ArchiveAccountEventHandler(navigator, request) }
        val viewModel = getEventfullViewModel<ArchiveAccountViewModel, ArchiveAccountViewModel.Factory>(
            eventHandler = eventHandler,
            viewModelFactory = { factory -> factory.create(request.account) },
        )
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()

        navigator.OnResult<FallbackAccountContract.Result>(
            action = { result -> viewModel.onSelectAccountResult(result) },
        )

        ArchiveAccountScreen(
            uiState = uiState,
            onBackClick = viewModel::onBackClick,
            onArchiveClick = viewModel::onArchiveClick,
            onCurrencyClick = viewModel::onCurrencyClick,
            onCurrencyReloadClick = viewModel::onCurrencyReloadClick,
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
        )
    }
}
