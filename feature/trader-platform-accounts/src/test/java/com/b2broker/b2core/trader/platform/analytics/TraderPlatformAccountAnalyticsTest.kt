package com.b2broker.b2core.trader.platform.analytics

import com.b2broker.b2core.analytics.event.AnalyticsEvent
import com.b2broker.b2core.analytics.helper.AnalyticsHelper
import com.b2broker.b2core.trader.platform.analitycs.TraderPlatformAccountAnalytics
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class TraderPlatformAccountAnalyticsTest {
    private val helper = mockk<AnalyticsHelper>(relaxed = true)
    private val analytics = TraderPlatformAccountAnalytics(helper)

    @Test
    fun `GIVEN currency and platform WHEN accountCreated is called THEN log event with correct parameters`() {
        // GIVEN
        val currency = "USD"
        val platform = "B2Trader"

        // WHEN
        analytics.accountCreated(currency, platform)

        // THEN
        val expectedEvent = AnalyticsEvent(
            type = TRADING_ACCOUNT_CREATED,
            extras = listOf(
                AnalyticsEvent.Param("Currency", currency),
                AnalyticsEvent.Param("Platform", platform),
            ),
        )
        verify { helper.logEvent(event = expectedEvent) }
    }

    @Test
    fun `GIVEN platform WHEN clickTradeButton is called THEN log event with correct parameters`() {
        // GIVEN
        val platform = "B2Trader"

        // WHEN
        analytics.clickTradeButton(platform)

        // THEN
        val expectedEvent = AnalyticsEvent(
            type = TRADE_BUTTON_CLICKED_EVENT,
            extras = listOf(
                AnalyticsEvent.Param("Platform", platform),
            ),
        )
        verify { helper.logEvent(event = expectedEvent) }
    }

    companion object {
        private const val TRADING_ACCOUNT_CREATED = "trading_account_created"
        private const val TRADE_BUTTON_CLICKED_EVENT = "Trade button clicked"
    }
}
