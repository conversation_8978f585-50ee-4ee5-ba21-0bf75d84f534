package com.b2broker.b2core.funds.deposit.account

import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.navigation.result.pop
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.handler.EventHandler
import com.b2broker.b2core.presentation.select.account.SelectAccountEvent

internal class DepositAccountEventHandler(
    private val request: DepositAccountContract.Request,
    private val navigator: Navigator,
) : EventHandler() {

    override fun handle(event: Event) {
        when (event) {
            is SelectAccountEvent -> when (event) {
                is SelectAccountEvent.SelectAccount -> navigator.pop(
                    request = request,
                    result = DepositAccountContract.Result.AccountId(
                        id = event.accountId,
                    ),
                )
            }

            else -> super.handle(event)
        }
    }
}
