package com.b2broker.b2core.funds.exchange.account

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.event.handler.rememberEventHandler
import com.b2broker.b2core.presentation.select.account.SetupSelectAccountScreen
import com.b2broker.b2core.presentation.uniqueScreenKey
import kotlinx.collections.immutable.toImmutableList
import kotlinx.parcelize.Parcelize

@Parcelize
internal class ExchangeAccountRoute(
    private val request: ExchangeAccountContract.Request,
    private val accounts: List<Account>,
    private val queryMinLen: Int = 2,
    override val key: ScreenKey = uniqueScreenKey(),
) : Route {

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow

        val eventHandler = rememberEventHandler {
            ExchangeAccountEventHandler(
                request = request,
                navigator = navigator,
            )
        }
        SetupSelectAccountScreen(
            accounts = accounts.toImmutableList(),
            queryMinLen = queryMinLen,
            navigator = navigator,
            eventHandler = eventHandler,
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
        )
    }
}
