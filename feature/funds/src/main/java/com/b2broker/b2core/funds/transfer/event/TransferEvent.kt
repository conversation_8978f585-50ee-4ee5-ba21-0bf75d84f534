package com.b2broker.b2core.funds.transfer.event

import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.funds.TransferData
import com.b2broker.b2core.presentation.event.Event

internal sealed interface TransferEvent : Event {
    data class SelectWithdrawAccount(val accounts: List<Account>) : TransferEvent
    data class SelectDepositAccount(val accounts: List<Account>) : TransferEvent
    data class ContinueTransfer(val transferData: TransferData) : TransferEvent
    data object OpenUpgradeLevel : TransferEvent
}
