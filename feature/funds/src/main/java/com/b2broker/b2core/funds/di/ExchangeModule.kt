package com.b2broker.b2core.funds.di

import com.b2broker.b2core.analytics.helper.AnalyticsHelper
import com.b2broker.b2core.funds.exchange.analytics.ExchangeAnalytics
import com.b2broker.b2core.funds.exchange.confirm.ExchangeConfirmUiState
import com.b2broker.b2core.funds.exchange.result.ExchangeResultUiState
import com.b2broker.b2core.funds.exchange.select.ExchangeRoute
import com.b2broker.b2core.funds.exchange.select.ExchangeUiState
import com.b2broker.b2core.navigation.RouteApplier
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.b2broker.b2core.presentation.state.UiStateHolder
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet

@Module
@InstallIn(SingletonComponent::class)
internal object ExchangeModule {

    @Provides
    fun provideExchangeHolder(): UiStateHolder<ExchangeUiState> {
        return DefaultUiStateHolder(ExchangeUiState())
    }

    @Provides
    fun provideExchangeConfirmHolder(): UiStateHolder<ExchangeConfirmUiState> {
        return DefaultUiStateHolder(ExchangeConfirmUiState())
    }

    @Provides
    fun provideExchangeResultHolder(): UiStateHolder<ExchangeResultUiState> {
        return DefaultUiStateHolder(ExchangeResultUiState())
    }

    @Provides
    fun provideExchangeAnalytics(helper: AnalyticsHelper): ExchangeAnalytics {
        return ExchangeAnalytics(helper = helper)
    }

    @Provides
    @IntoSet
    fun provideRouteApplier(): RouteApplier {
        return {
            register<SharedRoute.Exchange> { sharedRoute ->
                ExchangeRoute(
                    fromAccountNumber = sharedRoute.fromAccountId,
                    toAccountNumber = sharedRoute.toAccountId,
                )
            }
        }
    }
}
