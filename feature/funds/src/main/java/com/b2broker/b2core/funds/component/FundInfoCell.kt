package com.b2broker.b2core.funds.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.component.Spacer
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.imageloader.B2CurrencyImage

@Composable
internal fun FundInfoCell(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    valueDescription: String? = null,
    valueLeadingContent: (@Composable () -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        B2Text(
            modifier = Modifier.weight(1.0f),
            text = title,
            style = B2Theme.typography.bodyLarge,
            color = B2Theme.colors.onSurfaceVariant,
        )

        Column(
            horizontalAlignment = Alignment.End,
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                valueLeadingContent?.invoke()
                Spacer(width = 8.dp)
                B2Text(
                    modifier = Modifier.testTag(getFundCellInfoTestTagByTitle(title)),
                    text = value,
                    textAlign = TextAlign.End,
                    style = B2Theme.typography.bodyLarge,
                    color = B2Theme.colors.onSurface,
                )
            }

            valueDescription?.let { valueDescription ->
                B2Text(
                    text = valueDescription,
                    textAlign = TextAlign.End,
                    style = B2Theme.typography.bodyMedium,
                    color = B2Theme.colors.onSurfaceVariant,
                )
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun TextValueFieldPreview() = PreviewContainer {
    FundInfoCell(
        title = "Title",
        value = "Value",
        valueDescription = "Description",
    )
    Spacer(height = 8.dp)
    FundInfoCell(
        title = "Title",
        value = "Value",
        valueLeadingContent = {
            B2CurrencyImage(
                modifier = Modifier.size(24.dp),
                alphabeticCode = "USD",
            )
        },
    )
    Spacer(height = 8.dp)
    FundInfoCell(
        title = "Long long long long long long long title",
        value = "Long long long long long long long long long long long value",
    )
}

fun getFundCellInfoTestTagByTitle(title: String) = "FundInfoCell_$title"
