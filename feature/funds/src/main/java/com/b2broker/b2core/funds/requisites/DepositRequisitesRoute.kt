package com.b2broker.b2core.funds.requisites

import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import arrow.core.Either
import cafe.adriel.voyager.core.annotation.ExperimentalVoyagerApi
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.core.dynamicfields.DynamicFieldsEventHandler
import com.b2broker.b2core.dynamicforms.DynamicFormsEventHandler
import com.b2broker.b2core.dynamicforms.ObserveDynamicFormsResults
import com.b2broker.b2core.funds.requisites.event.DepositRequisitesEventHandler
import com.b2broker.b2core.funds.utils.EitherFormParceler
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.model.deposit.DepositPaymentMethod
import com.b2broker.b2core.model.deposit.DynamicForm
import com.b2broker.b2core.model.deposit.LegacyFields
import com.b2broker.b2core.model.funds.FormType
import com.b2broker.b2core.navigation.result.OnResult
import com.b2broker.b2core.navigation.result.contract.DatePickerContract
import com.b2broker.b2core.navigation.result.contract.SearchPhoneCountryContract
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.event.handler.Handle
import com.b2broker.b2core.presentation.event.handler.rememberEventHandler
import com.b2broker.b2core.presentation.getViewModel
import com.b2broker.b2core.presentation.uniqueScreenKey
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.TypeParceler

@Parcelize
@TypeParceler<Either<LegacyFields, DynamicForm>, EitherFormParceler>()
internal class DepositRequisitesRoute(
    override val key: ScreenKey = uniqueScreenKey(),
    private val account: Account,
    private val depositCurrency: Currency,
    private val depositMethod: DepositPaymentMethod,
    private val isReadyToDone: Boolean,
    private val form: Either<LegacyFields, DynamicForm>,
    private val useNewDepositFlow: Boolean,
) : Route {

    @OptIn(ExperimentalVoyagerApi::class)
    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow

        val viewModel = getViewModel<DepositRequisitesViewModel, DepositRequisitesViewModel.Factory>(
            viewModelFactory = { factory ->
                factory.create(
                    account = account,
                    depositCurrency = depositCurrency,
                    depositMethod = depositMethod,
                    isReadyToDone = isReadyToDone,
                    form = form,
                    useNewDepositFlow = useNewDepositFlow,
                )
            },
        )

        val multipleLauncher = rememberChooseMultipleImageLauncher { uri -> viewModel.onFilesAttached(uri) }
        val singleLauncher = rememberChooseImageLauncher { uri -> viewModel.onFilesAttached(listOf(uri)) }
        val urlLauncher = rememberUrlResultLauncher { viewModel.onUrlClosed() }
        val eventHandler = rememberEventHandler {
            DepositRequisitesEventHandler(
                navigator = navigator,
                urlResultLauncher = urlLauncher,
            ).setNext(
                DynamicFieldsEventHandler(
                    navigator = navigator,
                    multipleLauncher = multipleLauncher,
                    singleLauncher = singleLauncher,
                ),
            ).setNext(
                DynamicFormsEventHandler(
                    navigator = navigator,
                ),
            )
        }
        viewModel.events.Handle(eventHandler = eventHandler)

        val state by viewModel.uiStateFlow.collectAsStateWithLifecycle()

        when (state.formType) {
            FormType.FIELDS -> {
                navigator.OnResult<SearchPhoneCountryContract.Result>(
                    action = { result ->
                        viewModel.onCountryPhoneCodeSelected(result.phoneCountryData)
                    },
                )

                navigator.OnResult<DatePickerContract.Result> { result ->
                    viewModel.onDateSelected(result.dateMillis)
                }
            }

            FormType.FORM ->
                ObserveDynamicFormsResults(
                    navigator = navigator,
                    listener = viewModel,
                )
        }

        DepositRequisitesScreen(
            uiState = state,
            onBackClick = navigator::pop,
            onConfirmClick = viewModel::onConfirmClick,
            onRefreshRate = viewModel::refreshRate,
            onFromValueChange = viewModel::onFromValueChanged,
            onToValueChange = viewModel::onToValueChanged,
            onAmountChange = viewModel::onAmountChanged,
            onCommissionClick = viewModel::onCommissionClick,
            onCommissionDismiss = viewModel::onCommissionDismiss,
            dynamicFieldInteractions = viewModel,
            dynamicFormsInteractions = viewModel,
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
        )
    }

    @Composable
    private fun rememberChooseMultipleImageLauncher(onFilesSelect: (List<Uri>) -> Unit): ActivityResultLauncher<String> {
        return rememberLauncherForActivityResult(ActivityResultContracts.GetMultipleContents()) { listUri: List<Uri>? ->
            listUri ?: return@rememberLauncherForActivityResult
            onFilesSelect(listUri)
        }
    }

    @Composable
    private fun rememberChooseImageLauncher(onFileSelect: (Uri) -> Unit): ActivityResultLauncher<String> {
        return rememberLauncherForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            uri ?: return@rememberLauncherForActivityResult
            onFileSelect(uri)
        }
    }

    @Composable
    private fun rememberUrlResultLauncher(onClose: () -> Unit): ManagedActivityResultLauncher<Intent, ActivityResult> {
        return rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_CANCELED) {
                onClose()
            }
        }
    }
}
