package com.b2broker.b2core.funds.exchange.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.componentV2.divider.B2HorizontalDivider
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.formatter.formattedWithScaleDecimals
import com.b2broker.b2core.funds.component.FundInfoCell
import com.b2broker.b2core.imageloader.B2CurrencyImage
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.text.CoreStrings
import java.math.BigDecimal

@Composable
internal fun ExchangeInfo(
    from: Account?,
    to: Account?,
    amountFrom: BigDecimal,
    amountTo: BigDecimal,
    modifier: Modifier = Modifier,
) {
    val alphabeticCodeFrom = from?.currency
        ?.alphabeticCode
        .orEmpty()
    val alphabeticCodeTo = to?.currency
        ?.alphabeticCode
        .orEmpty()
    Column(modifier = modifier) {
        FundInfoCell(
            title = stringResource(id = CoreStrings.exchange_confirm_from_account),
            value = alphabeticCodeFrom,
            valueDescription = stringResource(id = CoreStrings.common_account_id, from?.number.orEmpty()),
            valueLeadingContent = {
                CurrencyIcon(alphabeticCodeFrom)
            },
        )
        B2HorizontalDivider()
        FundInfoCell(
            title = stringResource(id = CoreStrings.exchange_confirm_to_account),
            value = alphabeticCodeTo,
            valueDescription = stringResource(id = CoreStrings.common_account_id, to?.number.orEmpty()),
            valueLeadingContent = {
                CurrencyIcon(
                    alphabeticCodeTo,
                )
            },
        )
        B2HorizontalDivider()
        val sentAmount = remember(from) {
            StringBuilder().apply {
                append(amountFrom.formattedWithScaleDecimals())
                append(" ")
                append(
                    alphabeticCodeFrom,
                )
            }.toString()
        }
        FundInfoCell(
            title = stringResource(id = CoreStrings.exchange_confirm_from_amount),
            value = sentAmount,
        )
        B2HorizontalDivider()
        val receivedAmount = remember(to) {
            StringBuilder().apply {
                append(amountTo.formattedWithScaleDecimals())
                append(" ")
                append(
                    alphabeticCodeTo,
                )
            }.toString()
        }
        FundInfoCell(
            title = stringResource(id = CoreStrings.exchange_confirm_to_amount),
            value = receivedAmount,
        )
        B2HorizontalDivider()
    }
}

@Composable
private fun CurrencyIcon(alphabeticCode: String) {
    B2CurrencyImage(
        alphabeticCode = alphabeticCode,
        modifier = Modifier.size(16.dp),
    )
}

@PreviewLightDark
@Composable
private fun ExchangeInfoPreview() = PreviewContainer {
    ExchangeInfo(
        from = Account.fixture(
            currency = Currency.fixture(alphabeticCode = "EUR"),
        ),
        to = Account.fixture(
            currency = Currency.fixture(alphabeticCode = "USD"),
        ),
        amountFrom = BigDecimal("100"),
        amountTo = BigDecimal("1000"),
    )
}
