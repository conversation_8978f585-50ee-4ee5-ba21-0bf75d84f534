package com.b2broker.b2core.funds.internaltransfer

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.domain.funds.CalculatePercentUseCase
import com.b2broker.b2core.domain.internaltransfer.GetInternalTransferAccountsUseCase
import com.b2broker.b2core.domain.internaltransfer.GetInternalTransferPermissionUseCase
import com.b2broker.b2core.domain.internaltransfer.InternalTransferConfirmUseCase
import com.b2broker.b2core.domain.internaltransfer.ObserveInternalTransferAccountsUseCase
import com.b2broker.b2core.domain.internaltransfer.ValidateInternalTransferAmountValueUseCase
import com.b2broker.b2core.funds.internaltransfer.analytics.InternalTransferAnalytics
import com.b2broker.b2core.funds.internaltransfer.event.InternalTransferEvent
import com.b2broker.b2core.funds.internaltransfer.result.InternalTransferResultContract
import com.b2broker.b2core.funds.internaltransfer.selectaccount.InternalTransferSelectAccountContract
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.accounts.AccountStatement
import com.b2broker.b2core.model.accounts.AccountType
import com.b2broker.b2core.model.amount.AmountError
import com.b2broker.b2core.model.funds.internaltransfer.InternalTransferStatus
import com.b2broker.b2core.model.permissions.AccountPermission
import com.b2broker.b2core.model.permissions.StatePermission
import com.b2broker.b2core.presentation.event.default.SnackbarMessageEvent
import com.b2broker.b2core.presentation.event.dispatcher.QueueEventDispatcher
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.b2broker.b2core.test.ext.MainCoroutineUnconfinedExtension
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal

/**
 * Tests for [InternalTransferViewModel]
 */
@ExtendWith(MainCoroutineUnconfinedExtension::class)
internal class InternalTransferViewModelTest {

    private val getInternalTransferAccountsUseCase: GetInternalTransferAccountsUseCase = mockk()
    private val validateInternalTransferAmountValueUseCase: ValidateInternalTransferAmountValueUseCase = mockk {
        every {
            invoke(
                account = any(),
                value = any(),
                locale = any(),
            )
        } returns null
    }
    private val internalTransferConfirmUseCase: InternalTransferConfirmUseCase = mockk()
    private val observeInternalTransferAccountsUseCase: ObserveInternalTransferAccountsUseCase = mockk()
    private val internalTransferPermissionUseCase: GetInternalTransferPermissionUseCase = mockk()
    private val analytics: InternalTransferAnalytics = mockk()
    private lateinit var viewModel: InternalTransferViewModel
    private val accountChanges = MutableSharedFlow<List<Account>>()
    private val mockAccounts = listOf(
        Account.fixture(
            type = AccountType.PERSONAL,
            statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
            permissions = setOf(AccountPermission.TransferWithdrawal),
        ),
        Account.fixture(
            type = AccountType.TRADE,
            statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
            permissions = setOf(AccountPermission.TransferWithdrawal),
        ),
        Account.fixture(
            type = AccountType.PARTNER,
            statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
            permissions = setOf(AccountPermission.TransferWithdrawal),
        ),
        Account.fixture(
            id = "123",
            type = AccountType.PERSONAL,
            statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
            permissions = setOf(AccountPermission.TransferWithdrawal),
        ),
        Account.fixture(
            id = "*********",
            type = AccountType.PERSONAL,
            statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
            permissions = setOf(AccountPermission.TransferWithdrawal),
        ),
    )

    private fun createViewModel(
        initialFromId: String? = null,
        statePermission: StatePermission = StatePermission.Enabled,
    ) {
        coEvery { getInternalTransferAccountsUseCase(any()) } returns mockAccounts.right()
        coEvery { observeInternalTransferAccountsUseCase() } returns accountChanges
        coEvery { internalTransferPermissionUseCase(any()) } returns statePermission.right()
        viewModel = InternalTransferViewModel(
            uiStateHolder = DefaultUiStateHolder(InternalTransferUiState()),
            eventDispatcher = QueueEventDispatcher(),
            getInternalTransferAccountsUseCase = getInternalTransferAccountsUseCase,
            observeInternalTransferAccountsUseCase = observeInternalTransferAccountsUseCase,
            calculatePercentUseCase = CalculatePercentUseCase(),
            validateInternalTransferAmountValueUseCase = validateInternalTransferAmountValueUseCase,
            internalTransferConfirmUseCase = internalTransferConfirmUseCase,
            analytics = analytics,
            initialFromAccountId = initialFromId,
            internalTransferPermissionUseCase = internalTransferPermissionUseCase,
        )
    }

    private fun confirmMocks() {
        confirmVerified(
            getInternalTransferAccountsUseCase,
            validateInternalTransferAmountValueUseCase,
            internalTransferConfirmUseCase,
            observeInternalTransferAccountsUseCase,
            internalTransferPermissionUseCase,
            analytics,
        )
    }

    @Test
    fun `GIVEN viewmodel and user not allowed to make internal transfer WHEN init THEN should correct state`() = runTest {
        // GIVEN
        createViewModel(
            statePermission = StatePermission.Disabled,
        )

        // WHEN
        val actual = viewModel.uiState

        // THEN
        val expected = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = null,
            allWithdrawAccounts = persistentListOf(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = true,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Disabled,
        )

        assertThat(actual).isEqualTo(expected)
        coVerify { internalTransferPermissionUseCase(any()) }
        confirmMocks()
    }

    @Test
    fun `GIVEN viewmodel WHEN init THEN check loadAccounts`() = runTest {
        // WHEN
        createViewModel()

        // THEN
        coVerify { getInternalTransferAccountsUseCase(any()) }
    }

    @Test
    fun `GIVEN viewmodel with initialFromId WHEN init THEN check loadAccounts and state fromId is initialFromId`() = runTest {
        // GIVEN
        val initialId = "*********"

        // WHEN
        createViewModel(initialId)

        // THEN
        coVerify { getInternalTransferAccountsUseCase(any()) }
        assertThat(viewModel.uiState.currentWithdrawAccount).isEqualTo(mockAccounts[4])
    }

    @Test
    fun `GIVEN initial state WHEN onReloadClick THEN state with error and loadAccounts`() = runTest {
        // GIVEN
        createViewModel()
        coEvery { getInternalTransferAccountsUseCase(any()) } returns DefaultError("oh no").left()

        // WHEN
        viewModel.onReloadClick()
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = true,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
        coVerify { getInternalTransferAccountsUseCase(any()) }
    }

    @Test
    fun `GIVEN initial state WHEN onFromAccountClick THEN SelectWithdrawAccount event dispatched`() = runTest {
        // GIVEN
        createViewModel()

        // WHEN
        viewModel.onFromAccountClick()
        val actual = viewModel.events.first()
        // THEN
        val expect = InternalTransferEvent.SelectWithdrawAccount(mockAccounts)
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN clientId WHEN onClientIdChange THEN ui state with new clientId`() = runTest {
        // GIVEN
        val clientId = "123"
        createViewModel()

        // WHEN
        viewModel.onClientIdChange(clientId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = clientId,
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN viewModel and clientId with 30 symbols WHEN onClientIdChange with 31 symbol THEN ui state with old clientId`() = runTest {
        // GIVEN
        val clientId = buildString { repeat(30) { append("7") } }
        createViewModel()
        viewModel.onClientIdChange(clientId)

        // WHEN
        val newClientId = clientId + "7"
        viewModel.onClientIdChange(newClientId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = clientId,
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN empty clientId WHEN onClientIdChange THEN ui state with empty clientId and error`() = runTest {
        // GIVEN
        val clientId = ""
        createViewModel()

        // WHEN
        viewModel.onClientIdChange(clientId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = "",
            clientIdError = TextResource(CoreStrings.internal_transfer_field_is_required),
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN clientId WHEN onClientIdChange with non decimal symbol THEN ui state with old clientId`() = runTest {
        // GIVEN
        val clientId = "123"
        createViewModel()
        viewModel.onClientIdChange(clientId)

        // WHEN
        val clientIDWithNonDecimalSymbol = clientId + 'P'
        viewModel.onClientIdChange(clientIDWithNonDecimalSymbol)

        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = clientId,
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN recipientAccountId WHEN onRecipientAccountIdChange THEN ui state with new recipientAccountId`() = runTest {
        // GIVEN
        val recipientAccountId = "123"
        createViewModel()

        // WHEN
        viewModel.onRecipientAccountIdChange(recipientAccountId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountId = recipientAccountId,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN viewModel and recipientAccountId with 30 symbols WHEN recipientAccountId with 31 symbol THEN ui state with old recipientAccountId`() = runTest {
        // GIVEN
        val recipientAccountId = buildString { repeat(30) { append("7") } }
        createViewModel()
        viewModel.onRecipientAccountIdChange(recipientAccountId)

        // WHEN
        val newRecipientAccountId = recipientAccountId + "7"
        viewModel.onClientIdChange(newRecipientAccountId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountId = recipientAccountId,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN empty recipientAccountId WHEN onRecipientAccountIdChange THEN ui state with empty recipientAccountId and error`() = runTest {
        // GIVEN
        val recipientAccountId = ""
        createViewModel()

        // WHEN
        viewModel.onRecipientAccountIdChange(recipientAccountId)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = "",
            clientIdError = null,
            recipientAccountIdError = TextResource(CoreStrings.internal_transfer_field_is_required),
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN recipientAccountId WHEN onRecipientAccountIdChange with non decimal symbol THEN ui state with old recipientAccountId`() = runTest {
        // GIVEN
        val recipientAccountId = "123"
        createViewModel()
        viewModel.onRecipientAccountIdChange(recipientAccountId)

        // WHEN
        val recipientAccountIdWithNonDecimalSymbol = recipientAccountId + 'P'
        viewModel.onRecipientAccountIdChange(recipientAccountIdWithNonDecimalSymbol)

        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountId = recipientAccountId,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN amount WHEN onAmountChange THEN ui state with new amount`() = runTest {
        // GIVEN
        val amount = "123"
        every { validateInternalTransferAmountValueUseCase(any(), any()) } returns null
        createViewModel()

        // WHEN
        viewModel.onAmountChange(amount)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN empty amount WHEN onAmountChange THEN ui state with empty amount and error`() = runTest {
        // GIVEN
        val amount = ""
        every { validateInternalTransferAmountValueUseCase(any(), any()) } returns AmountError.RequiredError
        createViewModel()

        // WHEN
        viewModel.onAmountChange(amount)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            amountError = AmountError.RequiredError,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN amount with 1000 symbols WHEN onAmountChange THEN ui state not updates`() = runTest {
        // GIVEN
        val amount = buildString {
            repeat(1000) { append(7) }
        }
        every { validateInternalTransferAmountValueUseCase(any(), any()) } returns null
        createViewModel()
        viewModel.onAmountChange(amount)

        // WHEN
        val newAmount = amount + "7"
        viewModel.onAmountChange(newAmount)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN init viewModel WHEN onInternalTransferFromResultReturn THEN ui state with empty fields`() = runTest {
        // GIVEN
        createViewModel()

        // WHEN
        viewModel.onInternalTransferFromResultReturn(InternalTransferResultContract.Result.FromInternalTransferResultScreenOnDoneClick)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            amountError = null,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = "",
            clientIdError = null,
            recipientAccountId = "",
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN initial state WHEN onSelectPercent THEN check amount in state`() = runTest {
        // GIVEN
        every { validateInternalTransferAmountValueUseCase(any(), any()) } returns null
        createViewModel()

        // WHEN
        viewModel.onSelectPercent(percent = 25)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "0.25",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN result number of withdraw WHEN onSelectAccountResult THEN check the number in ui state`() {
        // GIVEN
        val id = "123"
        val result = InternalTransferSelectAccountContract.Result.WithdrawAccountId(id)
        createViewModel()

        // WHEN
        viewModel.onSelectWithdrawalAccountResult(result)
        val actual = viewModel.uiState
        // THEN
        val expect = InternalTransferUiState.fixture(
            amount = "",
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            currentWithdrawAccount = mockAccounts[3],
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        assertThat(actual).isEqualTo(expect)
    }

    @Test
    fun `GIVEN Status Failed Any500Failure WHEN onConfirmClick THEN get Result Screen event and analytics event sent`() = runTest {
        // GIVEN
        createViewModel()
        val errorMessage = "error message"
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns InternalTransferStatus.Failed.Any500Failure(errorMessage).right()
        coEvery { analytics.internalWithdrawFailed(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        assertThat(viewModel.events.first()).isInstanceOf(InternalTransferEvent.ConfirmTransfer::class.java)
        verify(exactly = 1) { analytics.internalWithdrawFailed(any(), any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
    }

    @Test
    fun `GIVEN Status SUCCESS WHEN onConfirmClick THEN get Result Screen event and analytics event sent`() = runTest {
        // GIVEN
        createViewModel()
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns InternalTransferStatus.Successful.right()
        coEvery { analytics.internalWithdrawDone(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        assertThat(viewModel.events.first()).isInstanceOf(InternalTransferEvent.ConfirmTransfer::class.java)
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawDone(any(), any(), any()) }
    }

    @Test
    fun `GIVEN Status PENDING WHEN onConfirmClick THEN get Result Screen event and analytics event sent`() = runTest {
        // GIVEN
        createViewModel()
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns InternalTransferStatus.Pending.right()
        coEvery { analytics.internalWithdrawDone(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        assertThat(viewModel.events.first()).isInstanceOf(InternalTransferEvent.ConfirmTransfer::class.java)
        verify(exactly = 1) { analytics.internalWithdrawDone(any(), any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
    }

    @Test
    fun `GIVEN Status Failed Common WHEN onConfirmClick THEN get failed message and analytics event not sent`() = runTest {
        // GIVEN
        val errorMessage = "error message"
        createViewModel()
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns InternalTransferStatus.Failed.Common(errorMessage).right()
        coEvery { analytics.internalWithdrawFailed(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        assertThat(viewModel.events.first()).isInstanceOf(SnackbarMessageEvent::class.java)
        verify(exactly = 0) { analytics.internalWithdrawFailed(any(), any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
    }

    @Test
    fun `GIVEN Status Failed ClientIdFailed WHEN onConfirmClick THEN get ClientIdFailure event and analytics event not sent`() = runTest {
        // GIVEN
        createViewModel()
        val errorMessage = "error message"
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns InternalTransferStatus.Failed.ClientIdFailed(errorMessage).right()
        coEvery { analytics.internalWithdrawFailed(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientId = "",
            clientIdError = TextResource(errorMessage),
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        verify(exactly = 0) { analytics.internalWithdrawFailed(any(), any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
    }

    @Test
    fun `GIVEN Status Failed RecipientAccountIdFailed WHEN onConfirmClick THEN get RecipientAccountIdFailure event and analytics event not sent`() =
        runTest {
            // GIVEN
            createViewModel()
            val errorMessage = "error message"
            coEvery {
                internalTransferConfirmUseCase.invoke(
                    sendAmount = any(),
                    fromAccountId = any(),
                    clientId = any(),
                    recipientAccountId = any(),
                )
            } returns InternalTransferStatus.Failed.RecipientAccountIdFailed(errorMessage).right()
            coEvery { analytics.internalWithdrawFailed(any(), any(), any()) } returns Unit
            coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
            val amount = "1"

            // WHEN
            viewModel.onAmountChange(amount)
            viewModel.onConfirmClick()
            val actual = viewModel.uiStateFlow.firstOrNull()
            val expected = InternalTransferUiState.fixture(
                amount = amount,
                currentWithdrawAccount = mockAccounts[0],
                allWithdrawAccounts = mockAccounts.toImmutableList(),
                clientIdError = null,
                recipientAccountId = "",
                recipientAccountIdError = TextResource(errorMessage),
                isAccountsLoadingInProgress = false,
                isPermissionInProgress = false,
                permissionState = StatePermission.Enabled,
            )

            // THEN
            assertThat(actual).isEqualTo(expected)
            verify(exactly = 0) { analytics.internalWithdrawFailed(any(), any(), any()) }
            verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
        }

    @Test
    fun `GIVEN error onLeft WHEN onConfirmClick THEN get failed message`() = runTest {
        // GIVEN
        createViewModel()
        val error = DefaultError(TextResource("error message"))
        coEvery {
            internalTransferConfirmUseCase.invoke(
                sendAmount = any(),
                fromAccountId = any(),
                clientId = any(),
                recipientAccountId = any(),
            )
        } returns error.left()
        coEvery { analytics.internalWithdrawFailed(any(), any(), any()) } returns Unit
        coEvery { analytics.internalWithdrawSubmitted(any(), any()) } returns Unit
        val amount = "1"

        // WHEN
        viewModel.onAmountChange(amount)
        viewModel.onConfirmClick()
        val actual = viewModel.uiStateFlow.firstOrNull()
        val expected = InternalTransferUiState.fixture(
            amount = amount,
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )

        // THEN
        assertThat(actual).isEqualTo(expected)
        assertThat(viewModel.events.first()).isInstanceOf(SnackbarMessageEvent::class.java)
        verify(exactly = 0) { analytics.internalWithdrawFailed(any(), any(), any()) }
        verify(exactly = 1) { analytics.internalWithdrawSubmitted(any(), any()) }
    }

    @Test
    fun `GIVEN initial viewModel WHEN new accounts added THEN update UiState with new accounts`() = runTest {
        // GIVEN
        createViewModel()

        // WHEN
        val newAccounts = buildList<Account> {
            addAll(mockAccounts)
            add(
                Account.fixture(
                    id = "********",
                    type = AccountType.PERSONAL,
                    statement = AccountStatement.fixture(availableBalance = BigDecimal("1")),
                    permissions = setOf(AccountPermission.TransferWithdrawal),
                ),
            )
        }.toImmutableList()
        accountChanges.emit(newAccounts)
        val expected = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = newAccounts,
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        val actual = viewModel.uiState

        // THEN
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `GIVEN initial viewModel WHEN emitted accounts with same accountsList THEN UiState with old accounts`() = runTest {
        // GIVEN
        createViewModel()

        // WHEN
        accountChanges.emit(mockAccounts)
        val expected = InternalTransferUiState.fixture(
            amount = "",
            currentWithdrawAccount = mockAccounts[0],
            allWithdrawAccounts = mockAccounts.toImmutableList(),
            clientIdError = null,
            recipientAccountIdError = null,
            isAccountsLoadingInProgress = false,
            loadingError = false,
            isPermissionInProgress = false,
            permissionState = StatePermission.Enabled,
        )
        val actual = viewModel.uiState

        // THEN
        assertThat(actual).isEqualTo(expected)
    }
}
