package com.b2broker.b2core.funds.transfer.confirm

import com.b2broker.b2core.test.SnapshotUnitTest
import com.b2broker.b2core.test.rule.MainDispatcherRule
import org.junit.Rule
import org.junit.Test

internal class TransferConfirmScreenKtTest : SnapshotUnitTest() {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @Test
    fun `WHEN TransferConfirmScreen THEN correct snapshot GIVEN params = `() {
        snapshot {
            TransferConfirmScreen(
                uiState = TransferConfirmUiState.fixture(),
                onBackClick = {},
                onConfirmClick = {},
            )
        }
    }
}
