package com.b2broker.b2core.funds.requisites.event

import android.content.Intent
import android.net.Uri
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.funds.deposit.done.DepositDoneRoute
import com.b2broker.b2core.model.deposit.DepositResult
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class DepositRequisitesEventHandlerTest {
    private val navigator: Navigator = mockk(relaxed = true)
    private val urlLauncher: ManagedActivityResultLauncher<Intent, ActivityResult> = mockk(relaxed = true)
    private lateinit var eventHandler: DepositRequisitesEventHandler

    @BeforeEach
    fun setUp() {
        eventHandler = DepositRequisitesEventHandler(
            navigator = navigator,
            urlResultLauncher = urlLauncher,
        )

        mockkStatic(Uri::class)
        every { Uri.parse(any()) } returns mockk()
    }

    @AfterEach
    fun after() {
        unmockkStatic(Uri::class)
    }

    @Test
    fun `GIVEN event OpenUrl WHEN handle THEN open chrome tabs via urlLauncher`() {
        // GIVEN
        val url = "https://b2broker.com"
        val event = DepositRequisitesEvent.OpenUrl(url)

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { Uri.parse(url) }
        verify(exactly = 1) { urlLauncher.launch(any()) }
    }

    @Test
    fun `GIVEN event ShowResult WHEN handle THEN Navigator push route`() {
        // GIVEN
        val event = DepositRequisitesEvent.ShowResult(DepositResult.UnknownResult)

        // WHEN
        eventHandler.handle(event)

        // THEN
        verify(exactly = 1) { navigator.replace(any() as DepositDoneRoute) }
    }
}
