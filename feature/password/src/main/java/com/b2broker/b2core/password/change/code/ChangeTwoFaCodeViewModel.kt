package com.b2broker.b2core.password.change.code

import androidx.lifecycle.ViewModel
import com.b2broker.b2core.model.twofa.TwoFaChallenge
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.mergeStateWith
import com.b2broker.b2core.presentation.password.code.TwoFaCodeUiState
import com.b2broker.b2core.presentation.password.code.delegate.ResendableTwoFaCodeDelegate
import com.b2broker.b2core.presentation.password.code.delegate.ResendableTwoFaCodeInteractions
import com.b2broker.b2core.presentation.state.UiStateHolder
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel(assistedFactory = ChangeTwoFaCodeViewModel.Factory::class)
internal class ChangeTwoFaCodeViewModel(
    uiStateHolder: UiStateHolder<TwoFaCodeUiState>,
    eventDispatcher: EventDispatcher,
    twoFaCodeDelegate: ResendableTwoFaCodeDelegate,
) : ViewModel(twoFaCodeDelegate),
    UiStateHolder<TwoFaCodeUiState> by uiStateHolder,
    EventDispatcher by eventDispatcher,
    ResendableTwoFaCodeInteractions by twoFaCodeDelegate {

    init {
        mergeStateWith(twoFaCodeDelegate) { copy(codeUiState = it) }
    }

    @AssistedInject
    constructor(
        uiStateHolder: UiStateHolder<TwoFaCodeUiState>,
        eventDispatcher: EventDispatcher,
        twoFaCodeDelegateFactory: ResendableTwoFaCodeDelegate.ChangeFactory,
        @Assisted challenge: TwoFaChallenge,
    ) : this(
        uiStateHolder = uiStateHolder,
        eventDispatcher = eventDispatcher,
        twoFaCodeDelegate = twoFaCodeDelegateFactory.create(challenge = challenge),
    )

    @AssistedFactory
    interface Factory {
        fun create(challenge: TwoFaChallenge): ChangeTwoFaCodeViewModel
    }
}
