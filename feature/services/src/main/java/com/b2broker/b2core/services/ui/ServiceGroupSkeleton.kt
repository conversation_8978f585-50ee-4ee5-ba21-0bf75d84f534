package com.b2broker.b2core.services.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.component.B2Shimmer
import com.b2broker.b2core.designsystem.preview.PreviewContainer

private const val SHIMMER_COUNT_1 = 6
private const val SHIMMER_COUNT_2 = 4

@Composable
internal fun ServicesLoadingBlock(
    modifier: Modifier = Modifier,
) {
    val shimmerLayout = listOf(SHIMMER_COUNT_1, SHIMMER_COUNT_2, SHIMMER_COUNT_1, SHIMMER_COUNT_2)
    LazyVerticalGrid(
        columns = GridCells.Adaptive(132.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(
            start = 16.dp,
            end = 16.dp,
            bottom = 16.dp,
        ),
        modifier = modifier,
    ) {
        shimmerLayout.forEachIndexed { index, groupCount ->
            item(key = index * 10 + groupCount) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp),
                ) {
                    B2Shimmer(
                        modifier = Modifier
                            .padding(top = 16.dp, bottom = 8.dp)
                            .height(16.dp)
                            .fillMaxWidth(),
                    )
                }
            }
            item(
                key = index * 10 + groupCount + 1,
                span = { GridItemSpan(maxCurrentLineSpan) },
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp),
                    horizontalArrangement = Arrangement.End,
                ) {
                    B2Shimmer(
                        modifier = Modifier
                            .padding(top = 12.dp, bottom = 4.dp)
                            .height(24.dp)
                            .width(24.dp),
                    )
                }
            }
            repeat(groupCount) { itemId ->
                item(key = index * 10 + itemId) {
                    B2Shimmer(
                        modifier = Modifier
                            .defaultMinSize(minWidth = 132.dp, minHeight = 88.dp),
                    )
                }
            }
            item(span = { GridItemSpan(maxCurrentLineSpan) }) {
                // header will start from a new line
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun ServicesLoadingBlockPreview() = PreviewContainer {
    ServicesLoadingBlock()
}
