package com.b2broker.b2core.b2trader.chart

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.domain.b2trader.orders.GetB2TraderOrdersV4HistoryUseCase
import com.b2broker.b2core.domain.b2trader.orders.ObserveB2TraderAccountOrdersUseCase
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.b2trader.orders.B2TraderOrderV4
import com.b2broker.b2core.model.b2trader.orders.OrderListData
import com.b2broker.b2core.model.b2trader.orders.OrderStatus
import com.b2broker.b2core.paging.model.PagerLoadingState
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class OrderHistoryPagerTest {

    private val getOrdersHistoryUseCase: GetB2TraderOrdersV4HistoryUseCase = mockk()
    private val observeOrdersUseCase: ObserveB2TraderAccountOrdersUseCase = mockk()

    private lateinit var pager: OrderHistoryPager

    @BeforeEach
    fun setUp() {
        pager = OrderHistoryPager(
            getB2TraderOrdersV4HistoryUseCase = getOrdersHistoryUseCase,
            observeB2TraderOrdersUseCase = observeOrdersUseCase,
        )
    }

    @Test
    fun `GIVEN orders WHEN load THEN emit filtered data`() = runTest {
        val ordersHistory =
            listOf(B2TraderOrderV4.fixture(orderId = "1", status = OrderStatus.STARTED))
        val ordersUpdate1 = OrderListData.fixture(
            history = listOf(
                B2TraderOrderV4.fixture(
                    orderId = "2",
                    status = OrderStatus.STARTED
                )
            )
        )
        // to be dropped off by the filter
        val ordersUpdate2 = OrderListData.fixture(
            history = listOf(
                B2TraderOrderV4.fixture(
                    orderId = "3",
                    status = OrderStatus.COMPLETED
                )
            ),
        )

        mockOrderHistory(ordersHistory.right())

        every { observeOrdersUseCase.invoke(any(), any()) } returns flowOf(
            ordersUpdate1.right(),
            ordersUpdate2.right()
        )

        val eventsDeferred = async {
            pager.load(
                accountId = "accountId",
                marketId = "marketId",
                startDate = Instant.DISTANT_PAST,
                endDate = Instant.DISTANT_FUTURE,
                statuses = emptySet(),
                filter = { events -> events.filter { event -> event.status == OrderStatus.STARTED } },
            )
                .take(3)
                .toList()
        }
        launch { pager.loadMoreHistory(0) }

        val events = eventsDeferred.await()
        assertThat(events.first().loadingState).isEqualTo(PagerLoadingState.Loading)
        assertThat(events.last().loadingState).isEqualTo(PagerLoadingState.NotLoading)
        assertThat(events.last().items).containsExactly(
            ordersHistory.first(),
            ordersUpdate1.history.first()
        )
    }

    @Test
    fun `GIVEN history error WHEN load THEN emit error`() = runTest {
        val ordersUpdate = OrderListData.fixture(history = listOf(B2TraderOrderV4.fixture(orderId = "2")))

        mockOrderHistory(DefaultError("").left())

        every { observeOrdersUseCase.invoke(any(), any()) } returns flowOf(ordersUpdate.right())

        val events = mutableListOf<OrderHistoryPagerState>()
        val job = launch {
            pager.load(
                accountId = "accountId",
                marketId = "marketId",
                startDate = Instant.DISTANT_PAST,
                endDate = Instant.DISTANT_FUTURE,
                statuses = emptySet(),
            )
                .take(2)
                .toList(events)
        }
        launch { pager.loadMoreHistory(0) }
        job.join()

        assertThat(events[0].loadingState).isEqualTo(PagerLoadingState.Loading)
        assertThat(events[1].loadingState).isInstanceOf(PagerLoadingState.Error::class.java)
    }

    private fun mockOrderHistory(response: Either<DefaultError, List<B2TraderOrderV4>>) {
        coEvery {
            getOrdersHistoryUseCase.invoke(
                accountId = any(),
                marketId = any(),
                lastOrderId = any(),
                createdAtFrom = any(),
                createdAtTo = any(),
                statuses = any(),
                limit = any(),
            )
        } returns response
    }
}
