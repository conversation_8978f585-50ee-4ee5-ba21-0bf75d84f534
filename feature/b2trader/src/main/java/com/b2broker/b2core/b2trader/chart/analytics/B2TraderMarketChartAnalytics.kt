package com.b2broker.b2core.b2trader.chart.analytics

import com.b2broker.b2core.analytics.event.AnalyticsEvent
import com.b2broker.b2core.analytics.helper.AnalyticsHelper
import com.b2broker.b2core.b2trader.chart.analytics.mapper.PeriodAnalyticEventMapper
import com.b2broker.b2core.b2trader.chart.analytics.model.AnalyticsChartState
import com.b2broker.b2core.model.marketchart.MarkerChartPeriod
import com.b2broker.b2core.presentation.b2trader.analytics.B2TraderAnalyticsEvent.createB2TraderEvent

internal class B2TraderMarketChartAnalytics(
    private val helper: AnalyticsHelper,
    private val periodAnalyticEventMapper: PeriodAnalyticEventMapper,
) {

    fun chartState(chartState: AnalyticsChartState) {
        val event = createB2TraderEvent(
            type = CHART_STATE,
            extras = listOf(
                AnalyticsEvent.Param("State", chartState.value),
            ),
        )
        helper.logEvent(event)
    }

    fun candleIntervalChange(interval: MarkerChartPeriod) {
        val event = createB2TraderEvent(
            type = CANDLE_INTERVAL_CHANGED,
            extras = listOf(
                AnalyticsEvent.Param("Interval", periodAnalyticEventMapper.map(interval)),
            ),
        )
        helper.logEvent(event)
    }

    companion object {
        private const val CHART_STATE = "Chart state"
        private const val CANDLE_INTERVAL_CHANGED = "Candle interval is changed"
    }
}
