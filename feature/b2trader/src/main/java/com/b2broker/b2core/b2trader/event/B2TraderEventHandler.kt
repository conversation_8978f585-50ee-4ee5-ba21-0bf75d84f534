package com.b2broker.b2core.b2trader.event

import cafe.adriel.voyager.navigator.Navigator
import cafe.adriel.voyager.navigator.bottomSheet.BottomSheetNavigator
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.navigation.args.B2TraderAccountListArgs
import com.b2broker.b2core.navigation.args.B2TraderMarginParamsArgs
import com.b2broker.b2core.navigation.args.B2TraderMarketsArgs
import com.b2broker.b2core.navigation.args.B2TraderOrderBookArgs
import com.b2broker.b2core.navigation.args.B2TraderPlaceOrdersArgs
import com.b2broker.b2core.navigation.push
import com.b2broker.b2core.navigation.result.contract.B2TraderAccountListContract
import com.b2broker.b2core.navigation.result.contract.B2TraderMarketsContract
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.default.BackEvent
import com.b2broker.b2core.presentation.event.handler.EventHandler
import com.b2broker.b2core.presentation.navigator.rootNavigator

internal class B2TraderEventHandler(
    private val localNavigator: Navigator,
    private val bottomSheetNavigator: BottomSheetNavigator,
) : EventHandler() {

    override fun handle(event: Event) {
        val rootNavigator = localNavigator.rootNavigator()
        if (event is BackEvent) {
            rootNavigator.pop()
            return
        }

        when (event) {
            is B2TraderEvent.OpenAccountList -> {
                val args = B2TraderAccountListArgs(event.currentAccountId)
                val request = B2TraderAccountListContract.Request(args, rootNavigator.lastItem.key)
                val route = SharedRoute.B2TraderAccountList(request)
                bottomSheetNavigator.push(route)
            }

            is B2TraderEvent.OpenMarginParams -> {
                val args = B2TraderMarginParamsArgs(event.currentAccountId)
                val route = SharedRoute.B2TraderMarginParams(args)
                bottomSheetNavigator.push(route)
            }

            is B2TraderEvent.OpenFunding -> bottomSheetNavigator.push(SharedRoute.B2TraderOpenFunding)

            is B2TraderEvent.OpenOrderBook -> {
                val args = B2TraderOrderBookArgs(event.currentAccountId, event.orderBook)
                rootNavigator.push(SharedRoute.B2TraderOrderBook(args))
            }

            is B2TraderEvent.OpenMarketInfo -> rootNavigator.push(
                SharedRoute.B2TraderMarketInfo(marketId = event.marketId, marketDisplayName = event.marketDisplayName, accountId = event.accountId),
            )

            is B2TraderEvent.OpenMarketPicker -> rootNavigator.push(
                SharedRoute.B2TraderMarkets(
                    B2TraderMarketsContract.Request(
                        args = B2TraderMarketsArgs(
                            accountId = event.accountId,
                            marketId = event.marketId,
                            rootAsset = event.rootAsset,
                        ),
                        screenKey = rootNavigator.lastItem.key,
                    ),
                ),
            )

            is B2TraderEvent.OpenPlaceOrderAdvanced -> {
                val args = B2TraderPlaceOrdersArgs(event.currentAccountId, event.marketId)
                rootNavigator.push(SharedRoute.B2TraderPlaceOrders(args))
            }

            else -> super.handle(event)
        }
    }
}
