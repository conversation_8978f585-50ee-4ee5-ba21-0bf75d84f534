package com.b2broker.b2core.b2trader.marketinfo

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.getViewModel
import com.b2broker.b2core.presentation.uniqueScreenKey
import kotlinx.parcelize.Parcelize

@Parcelize
internal class B2TraderMarketInfoRoute(
    override val key: ScreenKey = uniqueScreenKey(),
    private val marketId: String,
    private val marketDisplayName: String,
    private val accountId: String,
) : Route {

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow
        val viewModel = getViewModel<B2TraderMarketInfoViewModel, B2TraderMarketInfoViewModel.Factory> { factory ->
            factory.create(marketId, marketDisplayName, accountId)
        }
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()

        B2TraderMarketInfoScreen(
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
            state = uiState,
            onReloadClick = viewModel::onReloadClick,
            onBackClick = { navigator.pop() },
            onTabClick = viewModel::onTabClick,
            onFundingTabReloadPageClick = viewModel::onFundingTabReloadPageClick,
            onFundingTabTabScrolling = viewModel::onFundingTabTabScrolling,
        )
    }
}
