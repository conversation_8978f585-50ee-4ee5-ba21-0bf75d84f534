package com.b2broker.b2core.b2trader.components.tabs.orders.history

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.LoadingState
import com.b2broker.b2core.b2trader.Tab
import com.b2broker.b2core.b2trader.components.tabs.B2TraderInfoCardViewTabLoadingState
import com.b2broker.b2core.b2trader.components.tabs.OrderFilterSize
import com.b2broker.b2core.b2trader.components.tabs.SpaceBetweenCards
import com.b2broker.b2core.b2trader.components.tabs.SpaceTopCards
import com.b2broker.b2core.b2trader.components.tabs.TabSpacer
import com.b2broker.b2core.b2trader.components.tabs.getItemHeight
import com.b2broker.b2core.b2trader.tabs.TraderPlatformCfdTabUiState
import com.b2broker.b2core.b2trader.tabs.orderhistory.B2TraderOrderHistoryTabItem
import com.b2broker.b2core.designsystem.component.B2Shimmer
import com.b2broker.b2core.designsystem.component.listitem.listItemPosition
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.model.b2trader.orders.OrderType.Companion.stringRes
import com.b2broker.b2core.paging.model.PagerLoadingState
import com.b2broker.b2core.presentation.b2trader.common.B2TraderErrorState
import kotlinx.collections.immutable.ImmutableList

internal fun LazyListScope.historyOrderCardsTab(
    items: ImmutableList<B2TraderOrderHistoryTabItem>,
    loadingState: LoadingState,
    pagerLoadingState: PagerLoadingState = PagerLoadingState.NotLoading,
    showOrderFilter: Boolean,
    onItemClick: (tab: Tab, index: Int) -> Unit,
    onRetryPaginationClick: (tab: Tab) -> Unit
) {
    if (loadingState == LoadingState.LOADING) {
        item {
            Spacer(modifier = Modifier.size(SpaceTopCards))
            B2TraderInfoCardViewTabLoadingState(
                modifier = Modifier.padding(horizontal = 16.dp),
                tab = Tab.ORDER_HISTORY,
            )
        }
    } else {
        itemsIndexed(
            items = items,
            key = { _, item -> item.orderId },
        ) { index, item ->
            if (index == 0) {
                Spacer(modifier = Modifier.size(SpaceTopCards))
            }
            HistoryOrdersTabInfoCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .semantics { listItemPosition = index },
                onDetailsClick = { onItemClick(Tab.ORDER_HISTORY, index) },
                title = item.marketName,
                marketType = item.marketType,
                side = item.side,
                type = stringResource(item.type.stringRes),
                leverage = item.leverage,
                execPrice = item.executionPrice,
                fee = item.fee,
                feeCurrencyCode = item.feeCurrencyCode,
                amount = item.amount,
                createdAt = item.createdAt,
                filled = item.filled,
                remaining = item.remaining,
            )
            if (index != items.lastIndex) {
                Spacer(modifier = Modifier.size(SpaceBetweenCards))
            }
        }

        if (pagerLoadingState != PagerLoadingState.NotLoading) {
            item {
                Spacer(modifier = Modifier.size(SpaceBetweenCards))
            }
        }

        paginationItem(
            pagerLoadingState = pagerLoadingState,
            onRetryPaginationClick = { onRetryPaginationClick(Tab.ORDER_HISTORY) },
        ) {
            B2Shimmer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tab.ORDER_HISTORY.getItemHeight(isCardView = true))
                    .padding(horizontal = 16.dp),
            )
        }

        item {
            TabSpacer(
                tab = Tab.ORDER_HISTORY,
                itemsCount = items.size,
                coveredElementsSize = if (showOrderFilter) OrderFilterSize else 0.dp,
            )
        }
    }
}

private fun LazyListScope.paginationItem(
    pagerLoadingState: PagerLoadingState,
    onRetryPaginationClick: () -> Unit,
    loadingState: @Composable () -> Unit,
) {
    item {
        when (pagerLoadingState) {
            PagerLoadingState.Loading, PagerLoadingState.Refreshing -> loadingState()

            is PagerLoadingState.Error -> Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center,
            ) {
                B2TraderErrorState(onReloadClick = onRetryPaginationClick)
            }

            PagerLoadingState.NotLoading -> Unit
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview() = PreviewContainer {
    LazyColumn {
        historyOrderCardsTab(
            loadingState = LoadingState.COMPLETED,
            items = TraderPlatformCfdTabUiState.OrderHistory.fixture().items,
            pagerLoadingState = PagerLoadingState.Loading,
            showOrderFilter = false,
            onItemClick = { _, _ -> },
            onRetryPaginationClick = {},
        )
    }
}
