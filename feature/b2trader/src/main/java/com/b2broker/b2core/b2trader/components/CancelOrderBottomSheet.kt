package com.b2broker.b2core.b2trader.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.SheetState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.component.bottomsheet.B2BottomSheet
import com.b2broker.b2core.designsystem.component.buttons.B2Button
import com.b2broker.b2core.text.CoreStrings

@Composable
internal fun CancelOrderBottomSheet(
    orderType: String,
    onDismiss: () -> Unit,
    state: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    onConfirm: () -> Unit
) {
    B2BottomSheet(
        onDismissRequest = onDismiss,
        sheetState = state,
        header = {
            BottomSheetHeader(
                text = stringResource(CoreStrings.b2trader_market_chart_cancel_order_title, orderType),
            )
        },
    ) {
        Row(
            modifier = Modifier
                .padding(top = 24.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            B2Button(
                modifier = Modifier
                    .weight(1f)
                    .testTag(B2TraderMarketChartContentTestTags.CANCEL_ORDER_BOTTOM_SHEET_BACK_BUTTON),
                text = stringResource(CoreStrings.common_topbar_back),
                onClick = onDismiss,
                type = B2Button.Type.SECONDARY,
                size = B2Button.Size.L,
            )

            B2Button(
                modifier = Modifier
                    .weight(1f)
                    .testTag(B2TraderMarketChartContentTestTags.CANCEL_ORDER_BOTTOM_SHEET_CONFIRM_BUTTON),
                text = stringResource(CoreStrings.common_confirm),
                onClick = onConfirm,
                type = B2Button.Type.PRIMARY,
                size = B2Button.Size.L,
            )
        }
    }
}
