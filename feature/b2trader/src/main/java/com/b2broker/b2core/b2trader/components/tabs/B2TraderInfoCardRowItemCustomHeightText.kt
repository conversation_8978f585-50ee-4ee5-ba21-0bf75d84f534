package com.b2broker.b2core.b2trader.components.tabs

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.presentation.b2trader.common.getTextColor
import com.b2broker.b2core.presentation.b2trader.common.tabs.card.B2TraderInfoCardRow
import com.b2broker.b2core.presentation.b2trader.common.tabs.card.B2TraderInfoCardRowItem
import com.b2broker.b2core.presentation.b2trader.common.tabs.card.B2TraderInfoCardRowText
import com.b2broker.b2core.text.asString

@Composable
internal fun B2TraderInfoCardRowWithCustomHeightTextItem(
    firstItem: B2TraderInfoCardRowText,
    secondItem: B2TraderInfoCardRowText,
    lastItem: B2TraderInfoCardRowText,
    textStyle: TextStyle,
    lastItemTextStyle: TextStyle,
    modifier: Modifier = Modifier,
) {
    val lastItemValue = lastItem.value
    B2TraderInfoCardRow(
        modifier = modifier,
        firstItem = firstItem,
        secondItem = secondItem,
        textStyle = textStyle,
        lastItem = {
            B2TraderInfoCardRowItem(
                state = lastItem,
                horizontalAlignment = Alignment.End,
            ) {
                Box(
                    modifier = Modifier.height(textStyle.lineHeight.toDp()),
                    contentAlignment = Alignment.CenterEnd,
                ) {
                    B2Text(
                        modifier = Modifier.testTag(lastItem.testTag),
                        text = lastItemValue.value.asString(),
                        color = lastItemValue.polarity.getTextColor(),
                        style = lastItemTextStyle,
                        maxLines = 1,
                    )
                }
            }
        },
    )
}

@Composable
private fun TextUnit.toDp(): Dp {
    val density = LocalDensity.current
    return with(density) { toDp() }
}
