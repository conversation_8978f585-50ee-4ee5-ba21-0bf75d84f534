package com.b2broker.b2core.b2trader.positions.details

import android.content.ClipData
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.rememberTooltipState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.platform.toClipEntry
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.LoadingState
import com.b2broker.b2core.b2trader.components.B2TraderSellBuyIcon
import com.b2broker.b2core.b2trader.components.ReasonBottomSheet
import com.b2broker.b2core.b2trader.components.triggers.PositionTriggers
import com.b2broker.b2core.designsystem.CoreDrawable
import com.b2broker.b2core.designsystem.appspecific.B2LoadingError
import com.b2broker.b2core.designsystem.component.B2IconArrowDown
import com.b2broker.b2core.designsystem.component.B2NestedScaffold
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.component.buttons.B2Button
import com.b2broker.b2core.designsystem.component.clipboard.B2Clipboard
import com.b2broker.b2core.designsystem.component.clipboard.showWithDelay
import com.b2broker.b2core.designsystem.component.listitem.listItemPosition
import com.b2broker.b2core.designsystem.component.topbar.B2TopBar
import com.b2broker.b2core.designsystem.componentV2.B2Icon
import com.b2broker.b2core.designsystem.componentV2.B2SegmentedButton
import com.b2broker.b2core.designsystem.componentV2.B2SegmentedButtonItem
import com.b2broker.b2core.designsystem.componentV2.B2SegmentedButtonSize
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.model.b2trader.common.B2TraderReason.Companion.stringRes
import com.b2broker.b2core.model.b2trader.orders.OrderSide
import com.b2broker.b2core.model.b2trader.orders.OrderSide.Companion.stringRes
import com.b2broker.b2core.paging.model.PagerLoadingState
import com.b2broker.b2core.presentation.b2trader.common.B2TraderErrorState
import com.b2broker.b2core.presentation.b2trader.common.getTextColor
import com.b2broker.b2core.presentation.b2trader.triggers.components.TriggersInfoBottomSheet
import com.b2broker.b2core.presentation.b2trader.triggers.model.TriggerInfo
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.asString
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

@Composable
internal fun Modifier.cardModifier(
    padding: PaddingValues = PaddingValues(16.dp)
) = this
    .border(
        width = 1.dp,
        color = B2Theme.colors.outline,
        shape = B2Theme.shapes.medium,
    )
    .padding(padding)

@Composable
internal fun B2TraderPositionDetailsScreen(
    state: B2TraderPositionDetailsUiState,
    onReloadClick: () -> Unit,
    onBackClick: () -> Unit,
    onTabClick: (tab: TabUiState.Type) -> Unit,
    onCloseBtnClick: () -> Unit,
    onTradesItemClick: (index: Int) -> Unit,
    onTradesListScroll: (index: Int) -> Unit,
    onReloadTradesPageClick: () -> Unit,
    onEditTriggersClick: () -> Unit,
    onTriggerInfoClick: (triggerInfo: TriggerInfo) -> Unit,
    onTriggerInfoDismiss: () -> Unit,
    onReasonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    B2NestedScaffold(
        modifier = modifier,
        topBar = {
            B2TopBar(
                testTag = B2TraderPositionDetailsScreenTestTags.TOOLBAR,
                title = stringResource(CoreStrings.b2trader_position_details_title),
                onBackClick = onBackClick,
            )
        },
        bottomBar = {
            if (state.hasButtonClosePosition && state.tab.loadingState != LoadingState.ERROR && state.tab !is TabUiState.Trades) {
                B2Button(
                    modifier = Modifier
                        .testTag(B2TraderPositionDetailsScreenTestTags.BUTTON_CLOSE_POSITION)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    text = stringResource(CoreStrings.b2trader_position_details_btn_close),
                    onClick = onCloseBtnClick,
                    enabled = state.tab.loadingState != LoadingState.LOADING,
                )
            }
        },
    ) { innerPaddings ->
        Box(
            modifier = Modifier
                .padding(innerPaddings)
                .fillMaxSize(),
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                val tabs = TabUiState.Type.entries.map { tab ->
                    B2SegmentedButtonItem(
                        testTag = when (tab) {
                            TabUiState.Type.INFO -> B2TraderPositionDetailsScreenTestTags.TAB_INFO
                            TabUiState.Type.TRADES -> B2TraderPositionDetailsScreenTestTags.TAB_TRADES
                        },
                        text = when (tab) {
                            TabUiState.Type.INFO -> stringResource(CoreStrings.b2trader_position_details_tab_title_info)
                            TabUiState.Type.TRADES -> stringResource(CoreStrings.b2trader_position_details_tab_title_trades)
                        },
                    )
                }
                B2SegmentedButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp, horizontal = 16.dp),
                    itemsList = tabs.toImmutableList(),
                    selectedIndex = TabUiState.Type.entries.indexOf(state.tab.type),
                    onClick = { onTabClick(TabUiState.Type.entries[it]) },
                    size = B2SegmentedButtonSize.LARGE,
                )
                Content(
                    state = state,
                    onReloadClick = onReloadClick,
                    onTradesItemClick = onTradesItemClick,
                    onTradesListScroll = onTradesListScroll,
                    onReloadTradesPageClick = onReloadTradesPageClick,
                    onEditTriggersClick = onEditTriggersClick,
                    onTriggerInfoClick = onTriggerInfoClick,
                    onReasonClick = onReasonClick,
                )
            }

            val triggerInfo = (state.tab as? TabUiState.PositionInfo)?.triggerInfo
            if (triggerInfo != null) {
                TriggersInfoBottomSheet(
                    onDismissRequest = { onTriggerInfoDismiss() },
                    triggerInfo = triggerInfo,
                )
            }

            if (state.tab.reasonShown) {
                ReasonBottomSheet(onDismissRequest = onReasonClick)
            }
        }
    }
}

@Composable
private fun Content(
    state: B2TraderPositionDetailsUiState,
    onReloadClick: () -> Unit,
    onTradesItemClick: (index: Int) -> Unit,
    onTradesListScroll: (index: Int) -> Unit,
    onReloadTradesPageClick: () -> Unit,
    onEditTriggersClick: () -> Unit,
    onTriggerInfoClick: (triggerInfo: TriggerInfo) -> Unit,
    onReasonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.fillMaxSize()) {
        when (state.tab.loadingState) {
            LoadingState.LOADING -> when (state.tab) {
                is TabUiState.PositionInfo -> B2TraderPositionDetailsInfoLoading(
                    modifier = Modifier
                        .testTag(B2TraderPositionDetailsScreenTestTags.STATE_LOADING_INFO)
                        .padding(top = 16.dp)
                        .padding(horizontal = 16.dp),
                    itemsCount = 5,
                )

                is TabUiState.Trades -> B2TraderPositionDetailsTradesLoading(
                    modifier = Modifier
                        .testTag(B2TraderPositionDetailsScreenTestTags.STATE_LOADING_TRADES)
                        .padding(16.dp),
                )
            }

            LoadingState.ERROR -> B2TraderErrorState(
                modifier = Modifier
                    .testTag(B2TraderPositionDetailsScreenTestTags.STATE_ERROR)
                    .fillMaxSize(),
                onReloadClick = onReloadClick,
            )

            LoadingState.COMPLETED -> StateCompleted(
                state = state,
                onTradesItemClick = onTradesItemClick,
                onTradesListScroll = onTradesListScroll,
                onReloadTradesPageClick = onReloadTradesPageClick,
                onEditTriggersClick = onEditTriggersClick,
                onTriggerInfoClick = onTriggerInfoClick,
                onReasonClick = onReasonClick,
            )
        }
    }
}

@Composable
private fun StateCompleted(
    state: B2TraderPositionDetailsUiState,
    onTradesItemClick: (index: Int) -> Unit,
    onTradesListScroll: (index: Int) -> Unit,
    onReloadTradesPageClick: () -> Unit,
    onEditTriggersClick: () -> Unit,
    onTriggerInfoClick: (triggerInfo: TriggerInfo) -> Unit,
    onReasonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val (arrangement, spacerVertical) = when (state.tab) {
        is TabUiState.PositionInfo -> 12.dp to 4.dp
        is TabUiState.Trades -> 8.dp to 8.dp
    }
    val tradesListState = rememberLazyListState()
    val infoListState = rememberLazyListState()
    val scrollListener by rememberUpdatedState(onTradesListScroll)
    LaunchedEffect(tradesListState) {
        snapshotFlow {
            val layoutInfo = tradesListState.layoutInfo
            tradesListState.firstVisibleItemIndex + layoutInfo.visibleItemsInfo.size
        }
            .distinctUntilChanged()
            .collect(scrollListener)
    }
    LazyColumn(
        modifier = modifier
            .testTag(B2TraderPositionDetailsScreenTestTags.LIST)
            .padding(horizontal = 16.dp),
        state = when (state.tab) {
            is TabUiState.PositionInfo -> infoListState
            is TabUiState.Trades -> tradesListState
        },
        verticalArrangement = Arrangement.spacedBy(arrangement),
    ) {
        item(key = "SpacerTop") { Spacer(Modifier.size(spacerVertical)) }
        when (state.tab) {
            is TabUiState.PositionInfo -> {
                item(key = "MarketInfo") {
                    MarketInfo(
                        modifier = Modifier.semantics {
                            listItemPosition = B2TraderPositionDetailsScreenTestTags.POSITION_MARKET
                        },
                        state = state.tab,
                    )
                }

                if (state.tab.order == null) {
                    item(key = "PositionTriggers") {
                        PositionTriggers(
                            modifier = Modifier.semantics {
                                listItemPosition = B2TraderPositionDetailsScreenTestTags.POSITION_TRIGGERS
                            },
                            stopLoss = state.tab.stopLoss,
                            takeProfit = state.tab.takeProfit,
                            baseAssetId = state.tab.baseAssetId,
                            onEditTriggersClick = onEditTriggersClick,
                            onTriggerInfoClick = onTriggerInfoClick,
                        )
                    }
                }

                item(key = "Volume") {
                    Volume(
                        modifier = Modifier.semantics {
                            listItemPosition = B2TraderPositionDetailsScreenTestTags.POSITION_VOLUME
                        },
                        state = state.tab,
                    )
                }
                item(key = "Price") {
                    Price(
                        modifier = Modifier.semantics {
                            listItemPosition = B2TraderPositionDetailsScreenTestTags.POSITION_PRICE
                        },
                        state = state.tab,
                    )
                }
                item(key = "Info") {
                    Info(
                        modifier = Modifier.semantics {
                            listItemPosition = B2TraderPositionDetailsScreenTestTags.POSITION_INFO
                        },
                        state = state.tab,
                        onReasonClick = onReasonClick,
                    )
                }
            }

            is TabUiState.Trades -> {
                itemsIndexed(
                    items = state.tab.items,
                    key = { _, item -> item.tradeId },
                ) { index, item ->
                    TradesItem(
                        modifier = Modifier.semantics {
                            listItemPosition = index
                        },
                        item = item,
                        opened = index == state.tab.openedIndex,
                        onItemClick = { onTradesItemClick(index) },
                        onReasonClick = onReasonClick,
                    )
                }
                item {
                    when (state.tab.pagerLoadingState) {
                        PagerLoadingState.Loading -> B2TraderPositionDetailsTradesLoadingItem(
                            modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_LOADING),
                        )

                        is PagerLoadingState.Error -> B2LoadingError(
                            onReloadClick = onReloadTradesPageClick,
                            modifier = Modifier
                                .testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_ERROR)
                                .fillMaxWidth()
                                .padding(16.dp),
                            text = CoreStrings.common_try_again_paging,
                        )

                        PagerLoadingState.Refreshing, PagerLoadingState.NotLoading -> Unit
                    }
                }
            }
        }
        item(key = "SpacerBot") { Spacer(Modifier.size(spacerVertical)) }
    }
}

@Composable
private fun MarketInfo(
    state: TabUiState.PositionInfo,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.cardModifier()) {
        B2Text(
            modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.CARD_TITLE_MARKET),
            text = stringResource(CoreStrings.b2trader_position_details_info_title_market_info),
            style = B2Theme.typography.headlineSmall,
            color = B2Theme.colors.onSurface,
        )
        Spacer(modifier = Modifier.size(16.dp))
        TextRow(
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_display_name),
            value = state.displayName,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_DISPLAY_NAME,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_DISPLAY_NAME,
        )
        Spacer(modifier = Modifier.size(8.dp))
        TextRow(
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_full_name),
            value = state.fullName,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_FULL_NAME,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_FULL_NAME,
        )
        Spacer(modifier = Modifier.size(8.dp))
        SideRow(side = state.side)
    }
}

@Composable
private fun Volume(
    state: TabUiState.PositionInfo,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.cardModifier()) {
        B2Text(
            modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.CARD_TITLE_VOLUME),
            text = stringResource(CoreStrings.b2trader_position_details_info_title_volume),
            style = B2Theme.typography.headlineSmall,
            color = B2Theme.colors.onSurface,
        )
        Spacer(modifier = Modifier.size(16.dp))
        if (state.positionSizeLot != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_size_lot),
                value = state.positionSizeLot,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_POSITION_SIZE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_POSITION_SIZE,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (state.positionCloseSizeLot != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_close_size_lot),
                value = state.positionCloseSizeLot,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_CLOSE_POSITION_SIZE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_CLOSE_POSITION_SIZE,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (state.usedMargin != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_used_margin, state.currencyCode),
                value = state.usedMargin,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_USED_MARGIN,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_USED_MARGIN,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (state.leverage != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_leverage),
                value = state.leverage,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_LEVERAGE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_LEVERAGE,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        TextRow(
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_notional, state.currencyCode),
            value = state.notional,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_NOTIONAL,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_NOTIONAL,
        )
        Spacer(modifier = Modifier.size(8.dp))
        if (state.rate != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_rate, state.currencyCode),
                value = state.rate,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_RATE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_RATE,
            )
        }
    }
}

@Composable
private fun Price(
    state: TabUiState.PositionInfo,
    modifier: Modifier = Modifier,
) {
    val unrealisedPnLDay = state.unrealisedPnLDay
    val unrealisedPnLDayPercent = state.unrealisedPnLDayPercent
    val unrealisedPnLTotal = state.unrealisedPnLTotal
    val unrealisedPnLTotalPercent = state.unrealisedPnLTotalPercent
    val realisedPnL = state.realisedPnL
    Column(modifier = modifier.cardModifier()) {
        B2Text(
            modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.CARD_TITLE_PRICE),
            text = stringResource(CoreStrings.b2trader_position_details_info_title_price),
            style = B2Theme.typography.headlineSmall,
            color = B2Theme.colors.onSurface,
        )
        Spacer(modifier = Modifier.size(16.dp))
        TextRow(
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_open_price),
            value = state.openPrice,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_OPEN_PRICE,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_OPEN_PRICE,
        )
        Spacer(modifier = Modifier.size(8.dp))
        if (state.currentPrice != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_current_price),
                value = state.currentPrice,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_CURRENT_PRICE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_CURRENT_PRICE,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (state.closePrice != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_close_price),
                value = state.closePrice,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_CLOSE_PRICE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_CLOSE_PRICE,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (unrealisedPnLDay != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_unrealized_pnl_day, state.currencyCode),
                value = unrealisedPnLDay.value.asString(),
                color = unrealisedPnLDay.polarity.getTextColor(),
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_UNREALIZED_PNL_DAY,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_UNREALIZED_PNL_DAY,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (unrealisedPnLDayPercent != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_unrealized_pnl_day_percent),
                value = unrealisedPnLDayPercent.value.asString(),
                color = unrealisedPnLDayPercent.polarity.getTextColor(),
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_UNREALIZED_PNL_DAY_PERCENT,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_UNREALIZED_PNL_DAY_PERCENT,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (unrealisedPnLTotal != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_unrealized_pnl_total, state.currencyCode),
                value = unrealisedPnLTotal.value.asString(),
                color = unrealisedPnLTotal.polarity.getTextColor(),
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_UNREALIZED_PNL_TOTAL,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_UNREALIZED_PNL_TOTAL,
            )
            Spacer(modifier = Modifier.size(8.dp))
        }
        if (unrealisedPnLTotalPercent != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_unrealized_pnl_total_percent),
                value = unrealisedPnLTotalPercent.value.asString(),
                color = unrealisedPnLTotalPercent.polarity.getTextColor(),
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_UNREALIZED_PNL_TOTAL_PERCENT,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_UNREALIZED_PNL_TOTAL_PERCENT,
            )
        }
        if (realisedPnL != null) {
            TextRow(
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_realized_pnl, state.currencyCode),
                value = realisedPnL.value.asString(),
                color = realisedPnL.polarity.getTextColor(),
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_REALIZED_PNL,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_REALIZED_PNL,
            )
        }
    }
}

@Composable
private fun Info(
    state: TabUiState.PositionInfo,
    onReasonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val cardPadding = PaddingValues(vertical = 16.dp)
    Column(modifier = modifier.cardModifier(cardPadding)) {
        B2Text(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .testTag(B2TraderPositionDetailsScreenTestTags.CARD_TITLE_INFO),
            text = stringResource(CoreStrings.b2trader_position_details_info_title_info),
            style = B2Theme.typography.headlineSmall,
            color = B2Theme.colors.onSurface,
        )

        Spacer(modifier = Modifier.size(4.dp))

        ClipboardTextRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_position_id),
            value = state.positionId,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_POSITION_ID,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_POSITION_ID,
            testTagCopy = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_POSITION_ID_COPY,
        )
        TextRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_open_date),
            value = state.openDate,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_OPEN_DATE,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_OPEN_DATE,
        )
        if (state.updateDate != null) {
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_update_date),
                value = state.updateDate,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_UPDATE_DATE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_UPDATE_DATE,
            )
        }
        if (state.closeDate != null) {
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_close_date),
                value = state.closeDate,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_CLOSE_DATE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_CLOSE_DATE,
            )
        }

        TextRow(
            modifier = Modifier
                .clickable(onClick = onReasonClick)
                .padding(horizontal = 16.dp, vertical = 4.dp),
            name = stringResource(CoreStrings.b2trader_order_details_reason_label),
            value = stringResource(state.reason?.stringRes ?: CoreStrings.common_dash),
            isInfoIconVisible = true,
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_NAME,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_VALUE,
            testTagIcon = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_ICON,
        )

        if (state.isTradingSessionActive != null) {
            IndicatorTextRow(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
                name = stringResource(CoreStrings.b2trader_position_details_info_entry_session_status),
                value = stringResource(
                    if (state.isTradingSessionActive) {
                        CoreStrings.b2trader_position_details_info_entry_session_status_active
                    } else {
                        CoreStrings.b2trader_position_details_info_entry_session_status_inactive
                    },
                ),
                positive = state.isTradingSessionActive,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_SESSION_STATUS,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_SESSION_STATUS,
            )
        }
    }
}

@Composable
private fun TradesItem(
    onItemClick: () -> Unit,
    onReasonClick: () -> Unit,
    opened: Boolean,
    item: TradesItem,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_ROOT)
            .clickable(
                onClick = onItemClick,
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
            )
            .cardModifier(
                padding = PaddingValues(vertical = 16.dp),
            ),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            B2Text(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_SIDE),
                text = stringResource(item.side.stringRes),
                style = B2Theme.typography.bodyLarge,
                color = when (item.side) {
                    OrderSide.BUY -> B2Theme.colors.positive
                    OrderSide.SELL -> B2Theme.colors.negative
                },
            )
            Spacer(modifier = Modifier.size(8.dp))
            B2TraderSellBuyIcon(
                modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_SIDE_ICON),
                positive = item.side == OrderSide.BUY,
            )
            Spacer(modifier = Modifier.size(8.dp))
            B2Text(
                modifier = Modifier.testTag(B2TraderPositionDetailsScreenTestTags.TRADES_ITEM_HEADER),
                text = item.header,
                style = B2Theme.typography.bodyLarge,
                color = B2Theme.colors.onSurface,
            )
            Spacer(modifier = Modifier.weight(1f))
            B2IconArrowDown(
                modifier = Modifier.padding(end = 16.dp),
                isRotated = opened,
                tint = B2Theme.colors.onSurface,
            )
        }
        if (opened) {
            Box(
                modifier = Modifier
                    .padding(top = 16.dp, bottom = 8.dp)
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(color = B2Theme.colors.outline),
            )
            ClipboardTextRow(
                modifier = Modifier
                    .padding(top = 8.dp, bottom = 4.dp)
                    .padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_trade_id),
                value = item.tradeId,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_TRADE_ID,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_TRADE_ID,
                testTagCopy = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_TRADER_ID_COPY,
            )
            Spacer(modifier = Modifier.size(4.dp))
            SideRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                side = OrderSide.BUY,
            )
            Spacer(modifier = Modifier.size(4.dp))
            ClipboardTextRow(
                modifier = Modifier
                    .padding(top = 4.dp, bottom = 4.dp)
                    .padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_order_id),
                value = item.orderId,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_ORDER_ID,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_ORDER_ID,
                testTagCopy = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_ORDER_ID_COPY,
            )
            TextRow(
                modifier = Modifier
                    .clickable(onClick = onReasonClick)
                    .padding(vertical = 4.dp, horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_reason),
                value = item.reason,
                isInfoIconVisible = true,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_NAME,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_VALUE,
                testTagIcon = B2TraderPositionDetailsScreenTestTags.CARD_ROW_REASON_ICON,
            )
            Spacer(modifier = Modifier.size(4.dp))
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_info_title_volume),
                value = item.volume,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_VOLUME,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_VOLUME,
            )
            Spacer(modifier = Modifier.size(8.dp))
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_execution_price),
                value = item.executionPrice,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_EXECUTION_PRICE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_EXECUTION_PRICE,
            )
            Spacer(modifier = Modifier.size(8.dp))
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_fee, item.currencyCode),
                value = item.fee,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_FEE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_FEE,
            )
            Spacer(modifier = Modifier.size(8.dp))
            TextRow(
                modifier = Modifier.padding(horizontal = 16.dp),
                name = stringResource(CoreStrings.b2trader_position_details_trades_entry_trade_date),
                value = item.date,
                testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_TRADE_DATE,
                testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_TRADE_DATE,
            )
        }
    }
}

@Composable
private fun SideRow(
    side: OrderSide,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TextRow(
            modifier = Modifier.weight(1f),
            name = stringResource(CoreStrings.b2trader_position_details_info_entry_side),
            value = stringResource(side.stringRes),
            color = when (side) {
                OrderSide.BUY -> B2Theme.colors.positive
                OrderSide.SELL -> B2Theme.colors.negative
            },
            testTagName = B2TraderPositionDetailsScreenTestTags.CARD_ROW_NAME_SIDE,
            testTagValue = B2TraderPositionDetailsScreenTestTags.CARD_ROW_VALUE_SIDE,
        )
        Spacer(modifier = Modifier.size(8.dp))
        B2TraderSellBuyIcon(positive = side == OrderSide.BUY)
    }
}

@Composable
private fun ClipboardTextRow(
    name: String,
    value: String,
    testTagCopy: String,
    testTagName: String,
    testTagValue: String,
    modifier: Modifier = Modifier,
) {
    val clipboard = LocalClipboard.current
    val tooltipState = rememberTooltipState(isPersistent = true)
    val coroutineScope = rememberCoroutineScope()
    Row(
        modifier = Modifier
            .clickable(
                onClick = {
                    tooltipState.showWithDelay(coroutineScope)
                    coroutineScope.launch {
                        val clipEntry = ClipData.newPlainText(value, value).toClipEntry()
                        clipboard.setClipEntry(clipEntry)
                    }
                },
            )
            .then(modifier),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TextRow(
            modifier = Modifier.weight(1f),
            name = name,
            value = value,
            testTagName = testTagName,
            testTagValue = testTagValue,
        )
        Spacer(modifier = Modifier.size(4.dp))
        B2Clipboard(
            testTag = testTagCopy,
            tooltipState = tooltipState,
            onClick = {
                coroutineScope.launch {
                    val clipEntry = ClipData.newPlainText(value, value).toClipEntry()
                    clipboard.setClipEntry(clipEntry)
                }
            },
        )
    }
}

@Composable
private fun TextRow(
    name: String,
    value: String,
    testTagName: String,
    testTagValue: String,
    modifier: Modifier = Modifier,
    testTagIcon: String? = null,
    isInfoIconVisible: Boolean = false,
    color: Color = B2Theme.colors.onSurface,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        B2Text(
            modifier = Modifier.testTag(testTagName),
            text = name,
            style = B2Theme.typography.bodyLarge,
            color = B2Theme.colors.onSurfaceVariant,
        )

        if (isInfoIconVisible && testTagIcon != null) {
            Spacer(modifier = Modifier.size(4.dp))

            B2Icon(
                testTag = testTagIcon,
                iconResId = CoreDrawable.ic_info_16,
                tint = B2Theme.colors.onSurfaceVariant,
            )
        }

        Spacer(modifier = Modifier.size(16.dp))
        B2Text(
            modifier = Modifier
                .testTag(testTagValue)
                .weight(1f),
            text = value,
            style = B2Theme.typography.bodyLarge,
            color = color,
            maxLines = 1,
            textAlign = TextAlign.End,
        )
    }
}

@Composable
private fun IndicatorTextRow(
    name: String,
    value: String,
    testTagName: String,
    testTagValue: String,
    positive: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TextRow(
            modifier = Modifier.weight(1f),
            name = name,
            value = value,
            testTagName = testTagName,
            testTagValue = testTagValue,
        )
        Spacer(modifier = Modifier.size(8.dp))
        Box(
            modifier = Modifier
                .size(10.dp)
                .background(
                    color = if (positive) {
                        B2Theme.colors.positive
                    } else {
                        B2Theme.colors.negative
                    },
                    shape = CircleShape,
                ),
        )
    }
}

@PreviewLightDark
@Composable
private fun Preview() = PreviewContainer {
    B2TraderPositionDetailsScreen(
        state = B2TraderPositionDetailsUiState.fixture(
            tab = TabUiState.PositionInfo.fixture(
                order = null,
            ),
        ),
        onReloadClick = {},
        onBackClick = {},
        onTabClick = {},
        onCloseBtnClick = {},
        onTradesItemClick = {},
        onTradesListScroll = {},
        onReloadTradesPageClick = {},
        onEditTriggersClick = {},
        onTriggerInfoDismiss = {},
        onTriggerInfoClick = {},
        onReasonClick = {},
    )
}

object B2TraderPositionDetailsScreenTestTags {
    const val POSITION_MARKET = 0
    const val POSITION_TRIGGERS = 1
    const val POSITION_VOLUME = 2
    const val POSITION_PRICE = 3
    const val POSITION_INFO = 4

    const val TOOLBAR = "B2TraderPositionDetailsScreenToolbar"
    const val LIST = "B2TraderPositionDetailsScreenList"

    const val STATE_ERROR = "ErrorState"
    const val STATE_LOADING_INFO = "LoadingStateInfo"
    const val STATE_LOADING_TRADES = "LoadingStateTrades"

    const val TAB_INFO = "TabInfo"
    const val TAB_TRADES = "TabTrades"

    const val BUTTON_CLOSE_POSITION = "ButtonClosePosition"

    const val TRADES_ITEM_ROOT = "TradesItemRoot"
    const val TRADES_ITEM_LOADING = "TradesItemLoading"
    const val TRADES_ITEM_ERROR = "TradesItemError"
    const val TRADES_ITEM_SIDE = "TextTradesItemSide"
    const val TRADES_ITEM_SIDE_ICON = "IconTradesItemSide"
    const val TRADES_ITEM_HEADER = "TextTradesItemHeader"

    const val CARD_TITLE_MARKET = "TextCardTitleMarket"
    const val CARD_TITLE_VOLUME = "TextCardTitleVolume"
    const val CARD_TITLE_PRICE = "TextCardTitlePrice"
    const val CARD_TITLE_INFO = "TextCardTitleInfo"

    const val CARD_ROW_NAME_DISPLAY_NAME = "TextCardRowNameDisplayName"
    const val CARD_ROW_NAME_POSITION_SIZE = "TextCardRowNamePositionSize"
    const val CARD_ROW_NAME_FULL_NAME = "TextCardRowNameFullName"
    const val CARD_ROW_NAME_CLOSE_POSITION_SIZE = "TextCardRowNameClosePositionSize"
    const val CARD_ROW_NAME_LEVERAGE = "TextCardRowNameLeverage"
    const val CARD_ROW_NAME_RATE = "TextCardRowNameRate"
    const val CARD_ROW_NAME_NOTIONAL = "TextCardRowNameNotional"
    const val CARD_ROW_NAME_EXECUTION_PRICE = "TextCardRowNameExecutionPrice"
    const val CARD_ROW_NAME_OPEN_PRICE = "TextCardRowNameOpenPrice"
    const val CARD_ROW_NAME_CURRENT_PRICE = "TextCardRowNameCurrentPrice"
    const val CARD_ROW_NAME_CLOSE_PRICE = "TextCardRowNameClosePrice"
    const val CARD_ROW_NAME_USED_MARGIN = "TextCardRowNameUsedMargin"
    const val CARD_ROW_NAME_OPEN_DATE = "TextCardRowNameOpenDate"
    const val CARD_ROW_NAME_UPDATE_DATE = "TextCardRowNameUpdateDate"
    const val CARD_ROW_NAME_CLOSE_DATE = "TextCardRowNameCloseDate"
    const val CARD_ROW_NAME_TRADE_DATE = "TextCardRowNameTradeDate"
    const val CARD_ROW_NAME_FEE = "TextCardRowNameFee"
    const val CARD_ROW_NAME_VOLUME = "TextCardRowNameVolume"
    const val CARD_ROW_NAME_SIDE = "TextCardRowNameSide"
    const val CARD_ROW_NAME_UNREALIZED_PNL_DAY = "TextCardRowNameUnrealizedPnLDay"
    const val CARD_ROW_NAME_UNREALIZED_PNL_DAY_PERCENT = "TextCardRowNameUnrealizedPnLDayPercent"
    const val CARD_ROW_NAME_UNREALIZED_PNL_TOTAL = "TextCardRowNameUnrealizedPnLTotal"
    const val CARD_ROW_NAME_UNREALIZED_PNL_TOTAL_PERCENT = "TextCardRowNameUnrealizedPnLTotalPercent"
    const val CARD_ROW_NAME_REALIZED_PNL = "TextCardRowNameRealizedPnL"
    const val CARD_ROW_NAME_TRADE_ID = "TextCardRowNameTradeId"
    const val CARD_ROW_NAME_ORDER_ID = "TextCardRowNameOrderId"
    const val CARD_ROW_NAME_POSITION_ID = "TextCardRowNamePositionId"
    const val CARD_ROW_NAME_SESSION_STATUS = "TextCardRowNameSessionStatus"

    const val CARD_ROW_VALUE_DISPLAY_NAME = "TextCardRowValueDisplayName"
    const val CARD_ROW_VALUE_FULL_NAME = "TextCardRowValueFullName"
    const val CARD_ROW_VALUE_POSITION_SIZE = "TextCardRowValuePositionSize"
    const val CARD_ROW_VALUE_CLOSE_POSITION_SIZE = "TextCardRowValueClosePositionSize"
    const val CARD_ROW_VALUE_LEVERAGE = "TextCardRowValueLeverage"
    const val CARD_ROW_VALUE_RATE = "TextCardRowValueRate"
    const val CARD_ROW_VALUE_NOTIONAL = "TextCardRowValueNotional"
    const val CARD_ROW_VALUE_USED_MARGIN = "TextCardRowValueUsedMargin"
    const val CARD_ROW_VALUE_EXECUTION_PRICE = "TextCardRowValueExecutionPrice"
    const val CARD_ROW_VALUE_OPEN_PRICE = "TextCardRowValueOpenPrice"
    const val CARD_ROW_VALUE_CURRENT_PRICE = "TextCardRowValueCurrentPrice"
    const val CARD_ROW_VALUE_CLOSE_PRICE = "TextCardRowValueClosePrice"
    const val CARD_ROW_VALUE_OPEN_DATE = "TextCardRowValueOpenDate"
    const val CARD_ROW_VALUE_UPDATE_DATE = "TextCardRowValueUpdateDate"
    const val CARD_ROW_VALUE_CLOSE_DATE = "TextCardRowValueCloseDate"
    const val CARD_ROW_VALUE_TRADE_DATE = "TextCardRowValueTradeDate"
    const val CARD_ROW_VALUE_FEE = "TextCardRowValueFee"
    const val CARD_ROW_VALUE_VOLUME = "TextCardRowValueVolume"
    const val CARD_ROW_VALUE_SIDE = "TextCardRowValueSide"
    const val CARD_ROW_VALUE_UNREALIZED_PNL_DAY = "TextCardRowValueUnrealizedPnLDay"
    const val CARD_ROW_VALUE_UNREALIZED_PNL_DAY_PERCENT = "TextCardRowValueUnrealizedPnLDayPercent"
    const val CARD_ROW_VALUE_UNREALIZED_PNL_TOTAL = "TextCardRowValueUnrealizedPnLTotal"
    const val CARD_ROW_VALUE_UNREALIZED_PNL_TOTAL_PERCENT = "TextCardRowValueUnrealizedPnLTotalPercent"
    const val CARD_ROW_VALUE_REALIZED_PNL = "TextCardRowValueRealizedPnL"
    const val CARD_ROW_VALUE_TRADE_ID = "TextCardRowValueTradeId"
    const val CARD_ROW_VALUE_ORDER_ID = "TextCardRowValueOrderId"
    const val CARD_ROW_VALUE_POSITION_ID = "TextCardRowValuePositionId"
    const val CARD_ROW_VALUE_SESSION_STATUS = "TextCardRowValueSessionStatus"

    const val CARD_ROW_VALUE_ORDER_ID_COPY = "TextCardRowValueOrderIdIconCopy"
    const val CARD_ROW_VALUE_TRADER_ID_COPY = "TextCardRowValueTradeIDIconCopy"
    const val CARD_ROW_VALUE_POSITION_ID_COPY = "TextCardRowValuePositionIdIconCopy"

    const val CARD_ROW_REASON_NAME = "TextCardRowReasonName"
    const val CARD_ROW_REASON_VALUE = "TextCardRowReasonValue"
    const val CARD_ROW_REASON_ICON = "TextCardRowReasonIcon"
}
