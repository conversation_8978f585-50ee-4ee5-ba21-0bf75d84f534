package com.b2broker.b2core.b2trader.details.order.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.components.B2TraderSellBuyIcon
import com.b2broker.b2core.b2trader.components.tabs.stoporders.components.MarketTypeIcon
import com.b2broker.b2core.b2trader.details.order.B2TraderOrderDetailsScreenTestTags
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.model.b2trader.common.B2TraderReason
import com.b2broker.b2core.model.b2trader.common.B2TraderReason.Companion.stringRes
import com.b2broker.b2core.model.b2trader.orders.OrderSide
import com.b2broker.b2core.model.b2trader.orders.OrderSide.Companion.stringRes
import com.b2broker.b2core.model.b2trader.orders.OrderStatus
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.CANCELLED
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.COMPLETED
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.EXPIRED
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.PENDING
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.REJECTED
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.STARTED
import com.b2broker.b2core.model.b2trader.orders.OrderStatus.WORKING
import com.b2broker.b2core.model.b2trader.orders.OrderType
import com.b2broker.b2core.model.b2trader.orders.TimeInForce
import com.b2broker.b2core.model.b2trader.orders.TimeInForce.Companion.stringRes
import com.b2broker.b2core.text.CoreStrings

@Composable
internal fun OrderDetailsOrder(
    side: OrderSide?,
    orderType: OrderType?,
    timeInForce: TimeInForce?,
    status: OrderStatus,
    reason: B2TraderReason?,
    onReasonClick: () -> Unit,
) {
    ItemContainerColumn {
        Header(
            text = CoreStrings.b2trader_order_details_order_label,
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_HEADER,
        )
        TextItem(
            label = CoreStrings.b2trader_order_details_side_label,
            value = side?.let { stringResource(it.stringRes).sentenceCaseOrNull() },
            valueColor = if (side == OrderSide.BUY) {
                B2Theme.colors.positive
            } else {
                B2Theme.colors.negative
            },
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_SIDE,
        ) {
            B2TraderSellBuyIcon(
                modifier = Modifier.testTag(B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_SIDE_ICON),
                positive = side == OrderSide.BUY,
            )
        }

        TextItem(
            label = CoreStrings.b2trader_order_details_order_type_label,
            value = orderType?.name?.sentenceCaseOrNull(),
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_TYPE,
        ) {
            orderType?.let { type -> MarketTypeIcon(type) }
        }

        TextItem(
            label = CoreStrings.b2trader_order_time_in_force_label,
            value = timeInForce?.let { tif -> stringResource(tif.stringRes) },
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_TIME_IN_FORCE,
        )

        val statusColor = when (status) {
            STARTED -> B2Theme.colors.primary
            EXPIRED -> B2Theme.colors.onSurfaceVariant
            COMPLETED -> B2Theme.colors.positive
            PENDING, WORKING -> B2Theme.colors.medium
            REJECTED, CANCELLED -> B2Theme.colors.negative
        }
        TextItem(
            label = CoreStrings.b2trader_order_details_status_label,
            value = status.name.sentenceCaseOrNull(),
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_STATUS,
        ) {
            Spacer(
                modifier = Modifier
                    .size(10.dp)
                    .background(statusColor, CircleShape)
                    .testTag(B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_STATUS_ICON),
            )
        }

        TextItem(
            label = stringResource(CoreStrings.b2trader_order_details_reason_label),
            value = reason?.let { stringResource(it.stringRes) },
            testTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_REASON,
            isInfoIconVisible = true,
            infoIconTestTag = B2TraderOrderDetailsScreenTestTags.ORDER_DETAILS_ORDER_REASON_INFO_ICON,
            onClick = onReasonClick,
        )
    }
}

private fun String?.sentenceCaseOrNull() = this?.lowercase()?.replaceFirstChar { it.uppercase() }

@PreviewLightDark
@Composable
private fun OrderDetailsOrderPreview() = PreviewContainer {
    OrderDetailsOrder(
        side = OrderSide.BUY,
        orderType = OrderType.MARKET,
        timeInForce = TimeInForce.DAY,
        status = STARTED,
        reason = B2TraderReason.TRADER,
        onReasonClick = {},
    )
}
