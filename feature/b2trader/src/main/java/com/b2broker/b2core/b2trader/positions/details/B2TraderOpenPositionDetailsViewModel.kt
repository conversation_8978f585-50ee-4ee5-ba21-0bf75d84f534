package com.b2broker.b2core.b2trader.positions.details

import androidx.lifecycle.viewModelScope
import com.b2broker.b2core.b2trader.LoadingState
import com.b2broker.b2core.b2trader.analytics.B2TraderAnalytics
import com.b2broker.b2core.coroutines.onFirst
import com.b2broker.b2core.domain.b2trader.assets.GetRootAssetUseCase
import com.b2broker.b2core.domain.b2trader.markets.CheckTradingAvailabilityUseCase
import com.b2broker.b2core.domain.b2trader.markets.GetB2TraderMarketDetailsUseCase
import com.b2broker.b2core.domain.b2trader.positions.GetPositionExecutionsListUseCase
import com.b2broker.b2core.domain.b2trader.positions.ObserveOpenPositionsUseCase
import com.b2broker.b2core.formatter.DateFormatter
import com.b2broker.b2core.formatter.formattedWithScaleDecimals
import com.b2broker.b2core.model.b2trader.assets.Asset
import com.b2broker.b2core.model.b2trader.markets.details.B2TraderMarketDetails
import com.b2broker.b2core.model.b2trader.positions.B2TraderOpenPosition
import com.b2broker.b2core.navigation.args.B2TraderPositionDetailsArgs
import com.b2broker.b2core.paging.DataSource
import com.b2broker.b2core.paging.model.Page
import com.b2broker.b2core.presentation.b2trader.common.B2TraderPolarity
import com.b2broker.b2core.presentation.b2trader.common.B2TraderPolarityTextValue
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.state.UiStateHolder
import com.b2broker.b2core.text.asTextResource
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import java.util.Locale

@HiltViewModel(assistedFactory = B2TraderOpenPositionDetailsViewModel.Factory::class)
internal class B2TraderOpenPositionDetailsViewModel
@AssistedInject constructor(
    uiStateHolder: UiStateHolder<B2TraderPositionDetailsUiState>,
    eventDispatcher: EventDispatcher,
    getB2TraderMarketDetailsUseCase: GetB2TraderMarketDetailsUseCase,
    getRootAssetUseCase: GetRootAssetUseCase,
    private val observeOpenPositionsUseCase: ObserveOpenPositionsUseCase,
    private val getPositionExecutionsListUseCase: GetPositionExecutionsListUseCase,
    private val checkTradingAvailabilityUseCase: CheckTradingAvailabilityUseCase,
    private val dateFormatter: DateFormatter,
    private val analytics: B2TraderAnalytics,
    @Assisted private val args: B2TraderPositionDetailsArgs.OpenPosition,
) : B2TraderBasePositionDetailsViewModel(
    uiStateHolder = uiStateHolder,
    eventDispatcher = eventDispatcher,
    args = B2TraderBasePositionDetailsArgs(args.accountId, args.positionId, args.marketId),
    dateFormatter = dateFormatter,
    getRootAssetUseCase = getRootAssetUseCase,
    getB2TraderMarketDetailsUseCase = getB2TraderMarketDetailsUseCase,
    analytics = analytics,
) {
    init {
        updateState { B2TraderPositionDetailsUiState(hasButtonClosePosition = true) }
        reload()
    }

    override fun getDataSource() = DataSource { params ->
        val offset = params.anchor ?: 0
        val response = getPositionExecutionsListUseCase(
            accountId = args.accountId,
            positionId = args.positionId,
            limit = params.limit,
            offset = offset,
        )
        response.map { Page(it.executions, offset + it.executions.size) }
    }

    override fun loadInfo(marketDetails: B2TraderMarketDetails, rootAsset: Asset) {
        positionInfoJobs += checkTradingAvailabilityUseCase.invoke(marketDetails.calendar)
            .onEach { isAvailable ->
                positionInfoState.update { it.copy(isTradingSessionActive = isAvailable) }
            }
            .onFirst {
                positionInfoJobs += observeOpenPositionsUseCase.invoke(args.accountId)
                    .onEach { response ->
                        response
                            .onLeft { onInfoError() }
                            .onRight { data ->
                                val position = data.find { it.positionId == args.positionId }
                                position?.let { onPositionDataReceived(it, marketDetails, rootAsset) }
                            }
                    }
                    .launchIn(viewModelScope)
            }
            .launchIn(viewModelScope)
    }

    override fun onTabClick(tab: TabUiState.Type) {
        if (tab == TabUiState.Type.TRADES) analytics.openPositionTrades()
        super.onTabClick(tab)
    }

    private suspend fun onPositionDataReceived(
        position: B2TraderOpenPosition,
        marketDetails: B2TraderMarketDetails,
        rootAsset: Asset,
    ) = positionInfoState.update { state ->
        val unrealizedPnlDayInRAT = position.unrealizedPnlDayInRAT
        val unrealizedPnlDayPercent = position.unrealizedPnlDayPercent.movePointRight(2)
        val unrealizedPnlTotalInRAT = position.unrealizedPnlTotalInRAT
        val unrealizedPnlTotalPercent = position.unrealizedPnlTotalPercent.movePointRight(2)
        state.copy(
            loadingState = LoadingState.COMPLETED,
            currencyCode = rootAsset.id,
            displayName = marketDetails.displayName,
            side = position.side,
            fullName = marketDetails.fullName,
            positionSizeLot = position.positionLotAmount,
            usedMargin = position.usedMarginInRAT.formattedWithScaleDecimals(),
            leverage = position.leverage,
            notional = position.positionPriceInRAT.formattedWithScaleDecimals(),
            rate = position.rateToRAT,
            openPrice = position.openPrice.formattedWithScaleDecimals(),
            currentPrice = position.currentMarketPrice.formattedWithScaleDecimals(),
            unrealisedPnLDay = B2TraderPolarityTextValue(
                value = unrealizedPnlDayInRAT.formattedWithScaleDecimals().asTextResource(),
                polarity = B2TraderPolarity.fromNumber(unrealizedPnlDayInRAT),
            ),
            unrealisedPnLDayPercent = B2TraderPolarityTextValue(
                value = unrealizedPnlDayPercent.formattedWithScaleDecimals().asTextResource(),
                polarity = B2TraderPolarity.fromNumber(unrealizedPnlDayPercent),
            ),
            unrealisedPnLTotal = B2TraderPolarityTextValue(
                value = unrealizedPnlTotalInRAT.formattedWithScaleDecimals().asTextResource(),
                polarity = B2TraderPolarity.fromNumber(unrealizedPnlTotalInRAT),
            ),
            unrealisedPnLTotalPercent = B2TraderPolarityTextValue(
                value = unrealizedPnlTotalPercent.formattedWithScaleDecimals().asTextResource(),
                polarity = B2TraderPolarity.fromNumber(unrealizedPnlTotalPercent),
            ),
            positionId = position.positionId,
            openDate = dateFormatter.formatDate(position.createdAt, DateFormatter.DATE_FORMAT_DAY_SHORT_MONTH_YEAR_TIME),
            updateDate = dateFormatter.formatDate(position.updatedAt, DateFormatter.DATE_FORMAT_DAY_SHORT_MONTH_YEAR_TIME),
            takeProfit = position.takeProfit?.price
                .formattedWithScaleDecimals(),
            stopLoss = position.stopLoss?.price
                .formattedWithScaleDecimals(),
            baseAssetId = marketDetails.quoteAssetId.uppercase(Locale.US),
            reason = position.reason,
        )
    }

    @AssistedFactory
    internal interface Factory {
        fun create(args: B2TraderPositionDetailsArgs.OpenPosition): B2TraderOpenPositionDetailsViewModel
    }
}
