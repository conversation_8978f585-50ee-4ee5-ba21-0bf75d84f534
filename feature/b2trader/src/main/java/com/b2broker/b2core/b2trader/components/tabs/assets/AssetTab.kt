package com.b2broker.b2core.b2trader.components.tabs.assets

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.LoadingState
import com.b2broker.b2core.b2trader.components.tabs.TabSpacer
import com.b2broker.b2core.b2trader.tabs.TraderPlatformCfdTabUiState
import com.b2broker.b2core.b2trader.tabs.tab
import com.b2broker.b2core.designsystem.component.listitem.listItemPosition
import com.b2broker.b2core.designsystem.preview.PreviewContainer

internal fun LazyListScope.assetTab(
    content: TraderPlatformCfdTabUiState.Assets,
) {
    if (content.loadingState == LoadingState.LOADING) {
        item {
            AssetTabTabLoadingState()
        }
    } else {
        itemsIndexed(
            items = content.items,
            key = { _, item -> item.currencyInfo.currencyCode },
        ) { index, item ->
            AssetTabItem(
                modifier = Modifier
                    .testTag(AssetTabTestTags.CFD_CONTENT_ITEM_ASSET)
                    .semantics { listItemPosition = index }
                    .padding(start = 8.dp, end = 16.dp),
                item = item,
            )
        }
        item {
            TabSpacer(content.tab, content.items.size)
        }
    }
}

object AssetTabTestTags {
    const val CFD_CONTENT_ITEM_ASSET = "TraderPlatformCfdContentTabItemAsset"
}

@PreviewLightDark
@Composable
private fun TraderPlatformCfdContentPreview() = PreviewContainer {
    LazyColumn {
        assetTab(content = TraderPlatformCfdTabUiState.Assets.fixture())
    }
}
