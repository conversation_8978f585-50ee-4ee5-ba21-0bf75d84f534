package com.b2broker.b2core.b2trader.details.order.components

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.CoreDrawable
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.component.Spacer
import com.b2broker.b2core.designsystem.componentV2.B2Icon
import com.b2broker.b2core.designsystem.extras.clickableIfNonNull
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme

@Composable
internal fun TextItem(
    label: String,
    value: String?,
    testTag: String,
    modifier: Modifier = Modifier,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp, vertical = 4.dp),
    valueColor: Color = B2Theme.colors.onSurface,
    isInfoIconVisible: Boolean = false,
    infoIconTestTag: String? = null,
    onClick: (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickableIfNonNull(onClick)
            .padding(paddingValues)
            .testTag(testTag)
            .then(modifier),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TextItemLabel(label = label, testTag = "${testTag}Label")

        if (isInfoIconVisible && infoIconTestTag != null) {
            Spacer(width = 4.dp)

            B2Icon(
                testTag = infoIconTestTag,
                iconResId = CoreDrawable.ic_info_16,
                tint = B2Theme.colors.onSurfaceVariant,
            )
        }

        Spacer(width = 4.dp)

        TextItemValue(
            text = value ?: "-",
            color = valueColor,
            modifier = Modifier.weight(1f),
            testTag = "${testTag}Value",
        )

        trailingIcon?.let { icon ->
            Spacer(width = 8.dp)
            icon()
        }
    }
}

@Composable
internal fun TextItem(
    @StringRes label: Int,
    value: String?,
    testTag: String,
    modifier: Modifier = Modifier,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp, vertical = 4.dp),
    valueColor: Color = B2Theme.colors.onSurface,
    onClick: (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null
) {
    TextItem(
        modifier = modifier,
        paddingValues = paddingValues,
        label = stringResource(label),
        value = value,
        valueColor = valueColor,
        testTag = testTag,
        trailingIcon = trailingIcon,
        onClick = onClick,
    )
}

@Composable
internal fun TextItemLabel(
    label: String,
    testTag: String,
) {
    B2Text(
        modifier = Modifier.testTag(testTag),
        text = label,
        style = B2Theme.typography.bodyLarge,
        color = B2Theme.colors.onSurfaceVariant,
        maxLines = 1,
    )
}

@Composable
internal fun TextItemValue(
    text: String,
    testTag: String,
    modifier: Modifier = Modifier,
    color: Color = B2Theme.colors.onSurface,
) {
    B2Text(
        modifier = modifier.testTag(testTag),
        text = text,
        style = B2Theme.typography.bodyLarge,
        color = color,
        maxLines = 1,
        textAlign = TextAlign.End,
    )
}

@PreviewLightDark
@Composable
private fun TextItemPreview() = PreviewContainer {
    TextItem(
        label = "Label",
        value = "Value",
        testTag = "testTag",
        isInfoIconVisible = true,
        infoIconTestTag = "infoIconTestTag",
        onClick = {},
    ) {
        B2Text(text = "Trailing")
    }
}
