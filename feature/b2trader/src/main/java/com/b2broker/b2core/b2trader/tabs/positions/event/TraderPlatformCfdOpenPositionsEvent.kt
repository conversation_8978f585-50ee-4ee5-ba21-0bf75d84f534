package com.b2broker.b2core.b2trader.tabs.positions.event

import com.b2broker.b2core.presentation.event.Event

internal sealed interface TraderPlatformCfdOpenPositionsEvent : Event {

    data class OpenPositionDetails(
        val accountId: String,
        val positionId: String,
        val marketId: String
    ) : TraderPlatformCfdOpenPositionsEvent

    data class OpenClosePosition(
        val accountId: String,
        val positionId: String,
        val marketId: String
    ) : TraderPlatformCfdOpenPositionsEvent

    data class OpenPositionTriggers(
        val accountId: String,
        val positionId: String,
        val marketId: String,
        val stopLoss: String,
        val takeProfit: String,
        val rootAsset: String,
    ) : TraderPlatformCfdOpenPositionsEvent
}
