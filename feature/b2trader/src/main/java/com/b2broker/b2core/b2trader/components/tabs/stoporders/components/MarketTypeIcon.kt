package com.b2broker.b2core.b2trader.components.tabs.stoporders.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.components.tabs.stoporders.components.MarketTypeIconTestKeys.TAB_ITEM_TYPE_ICON
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.model.b2trader.orders.OrderType

@Composable
internal fun MarketTypeIcon(type: OrderType) {
    B2Text(
        modifier = Modifier
            .size(17.dp, 16.dp)
            .background(
                color = B2Theme.colors.surfaceContainerHigh,
                shape = B2Theme.shapes.small,
            )
            .testTag(TAB_ITEM_TYPE_ICON),
        text = type.name
            .first()
            .toString(),
        style = B2Theme.typography.labelSmall,
        color = B2Theme.colors.primary,
        textAlign = TextAlign.Center,
    )
}

internal object MarketTypeIconTestKeys {
    const val TAB_ITEM_TYPE_ICON = "StopOrdersTabItemOrderTypeIcon"
}
