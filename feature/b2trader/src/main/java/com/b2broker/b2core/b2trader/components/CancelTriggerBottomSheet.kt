package com.b2broker.b2core.b2trader.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.SheetState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.chart.MarketChartBottomSheet.CancelTrigger.TriggerType
import com.b2broker.b2core.b2trader.chart.MarketChartBottomSheet.CancelTrigger.TriggerType.STOP_LOSS
import com.b2broker.b2core.b2trader.chart.MarketChartBottomSheet.CancelTrigger.TriggerType.TAKE_PROFIT
import com.b2broker.b2core.designsystem.component.bottomsheet.B2BottomSheet
import com.b2broker.b2core.designsystem.component.buttons.B2Button
import com.b2broker.b2core.text.CoreStrings

@Composable
internal fun CancelTriggerBottomSheet(
    type: TriggerType,
    onDismissCancelTrigger: () -> Unit,
    state: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    onApproveCancelTrigger: () -> Unit
) {
    B2BottomSheet(
        onDismissRequest = onDismissCancelTrigger,
        sheetState = state,
        header = {
            val triggerName = when (type) {
                TAKE_PROFIT -> stringResource(CoreStrings.b2trader_take_profit)
                STOP_LOSS -> stringResource(CoreStrings.b2trader_stop_loss)
            }

            BottomSheetHeader(
                text = stringResource(CoreStrings.b2trader_market_chart_cancel_tpsl_title, triggerName)
            )
        },
    ) {
        Row(
            modifier = Modifier
                .padding(top = 24.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            B2Button(
                modifier = Modifier
                    .weight(1f)
                    .testTag(B2TraderMarketChartContentTestTags.CANCEL_TRIGGER_BOTTOM_SHEET_CANCEL_BUTTON),
                text = stringResource(CoreStrings.common_cancel),
                onClick = onDismissCancelTrigger,
                type = B2Button.Type.SECONDARY,
                size = B2Button.Size.L,
            )

            B2Button(
                modifier = Modifier
                    .weight(1f)
                    .testTag(B2TraderMarketChartContentTestTags.CANCEL_TRIGGER_BOTTOM_SHEET_REMOVE_BUTTON),
                text = stringResource(CoreStrings.common_remove),
                onClick = onApproveCancelTrigger,
                type = B2Button.Type.PRIMARY,
                size = B2Button.Size.L,
            )
        }
    }
}
