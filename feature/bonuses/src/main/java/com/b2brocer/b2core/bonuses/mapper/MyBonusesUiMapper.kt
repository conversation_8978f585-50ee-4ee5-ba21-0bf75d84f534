package com.b2brocer.b2core.bonuses.mapper

import com.b2brocer.b2core.bonuses.models.MyBonusUI
import com.b2broker.b2core.common.mapper.AsyncMapper
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.formatter.CurrentTimeProvider
import com.b2broker.b2core.formatter.formatted
import com.b2broker.b2core.model.bonus.MyBonus
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject

internal class MyBonusesUiMapper @Inject constructor(
    private val currentTimeProvider: CurrentTimeProvider,
    private val timePeriodFormatter: TimePeriodFormatter,
    @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
) : AsyncMapper<MyBonus, MyBonusUI>(defaultDispatcher) {
    override suspend fun internalMap(from: MyBonus): MyBonusUI = MyBonusUI(
        id = from.id,
        name = from.name?.let { TextResource(it) } ?: TextResource(CoreStrings.bonuses_no_value),
        accountType = from
            .platform
            .type
            .name,
        value = from.amount.formatted(),
        currency = from.currency.name,
        tradeLots = "${from.volumeComplete.formatted()}/${from.volumeThreshold.formatted()}",
        timeLeft = timePeriodFormatter.getTimeLeft(
            activatedAt = from.activatedAt,
            lifetime = from.lifetime,
            today = currentTimeProvider.getTime(),
        ),
        accountNumber = from.accountNumber,
        status = from.status,
        isDemo = from.platform.isDemo,
        accountName = from.accountName?.let { TextResource(it) } ?: TextResource(CoreStrings.bonuses_no_value),
    )
}
