package com.b2broker.b2core.wallets.searchwallet

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.domain.account.ObserveAccountsChangesUseCase
import com.b2broker.b2core.domain.account.SetAccountFavoriteStateUseCase
import com.b2broker.b2core.domain.wallets.GetWalletsInfoUseCase
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.model.UnknownError
import com.b2broker.b2core.model.accounts.Account
import com.b2broker.b2core.model.accounts.AccountGroup
import com.b2broker.b2core.model.accounts.AccountStatement
import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.model.wallets.Wallet
import com.b2broker.b2core.model.wallets.WalletsInfo
import com.b2broker.b2core.presentation.event.default.SnackbarMessageEvent
import com.b2broker.b2core.presentation.event.dispatcher.QueueEventDispatcher
import com.b2broker.b2core.presentation.search.ui.B2SearchUiState
import com.b2broker.b2core.presentation.state.DefaultUiStateHolder
import com.b2broker.b2core.test.ext.MainCoroutineUnconfinedExtension
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import com.b2broker.b2core.text.asTextResource
import com.b2broker.b2core.wallets.searchwallet.searchdelegate.SearchWalletSearchDelegate
import com.b2broker.b2core.wallets.ui.WalletsAccountUiItem
import com.b2broker.b2core.wallets.ui.WalletsAccountUiItemMapper
import com.google.common.truth.Truth
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal

@ExtendWith(MainCoroutineUnconfinedExtension::class)
internal class SearchWalletViewModelTest {

    private val searchDelegate = mockk<SearchWalletSearchDelegate>(relaxed = true)
    private val walletsAccountUiItemMapper = mockk<WalletsAccountUiItemMapper>()
    private val getWalletsInfoUseCase = mockk<GetWalletsInfoUseCase>()
    private val observeAccountsChangesUseCase = mockk<ObserveAccountsChangesUseCase>()
    private val setAccountFavoriteStateUseCase = mockk<SetAccountFavoriteStateUseCase>()

    private lateinit var viewModel: SearchWalletViewModel

    @BeforeEach
    fun setup() {
        coEvery {
            walletsAccountUiItemMapper.map(
                wallet = any(),
                totalBalanceCurrency = any(),
                isBalanceHidden = any(),
            )
        } returns WalletsAccountUiItem.fixture()

        every { observeAccountsChangesUseCase() } returns emptyFlow()
        coEvery { setAccountFavoriteStateUseCase(any(), any()) } returns Unit.right()
    }

    @Test
    fun `GIVEN viewmodel WHEN init THEN setItems called`() {
        // GIVEN
        val info = WalletsInfo(emptyList(), testCurrency, emptySet())
        coEvery { getWalletsInfoUseCase.getWalletsInfo(testCurrency, any()) } returns info.right()

        viewModel = createViewModel()

        // THEN
        verify(exactly = 1) { searchDelegate.setItems(any()) }
    }

    @Test
    fun `GIVEN error result WHEN init THEN setItems not called`() = runTest {
        // GIVEN
        coEvery { getWalletsInfoUseCase.getWalletsInfo(testCurrency, any()) } returns DefaultError("Error".asTextResource()).left()

        // WHEN
        viewModel = createViewModel()

        // THEN
        verify(exactly = 0) { searchDelegate.setItems(any()) }
    }

    @Test
    fun `GIVEN two accounts(coins and fiat) WHEN onFavoriteClick THEN change favorite state`() = runTest {
        // GIVEN
        val wallets = listOf(
            Wallet(
                account = createZeroBalanceAccount(id = "3", groupCoins, isFavorite = true),
                converted = zeroBalanceStatement,
            ),
        )
        val info = WalletsInfo(wallets, testCurrency, emptySet())
        coEvery { getWalletsInfoUseCase.getWalletsInfo(testCurrency, any()) } returns info.right()

        // WHEN
        val viewModel = createViewModel()
        viewModel.onFavoriteClick(createZeroBalanceAccountUiItem(id = "3", groupCoins))

        // THEN
        coVerify(exactly = 1) {
            setAccountFavoriteStateUseCase(
                account = createZeroBalanceAccount(id = "3", groupCoins, isFavorite = true),
                newState = false,
            )
        }
    }

    @Test
    fun `GIVEN error on set favorite WHEN onFavoriteClick THEN dispatch messageEvent`() = runTest {
        // GIVEN
        val wallets = listOf(
            Wallet(
                account = createZeroBalanceAccount(id = "3", groupCoins, isFavorite = true),
                converted = zeroBalanceStatement,
            ),
        )
        val info = WalletsInfo(wallets, testCurrency, emptySet())
        coEvery { getWalletsInfoUseCase.getWalletsInfo(testCurrency, any()) } returns info.right()
        coEvery { setAccountFavoriteStateUseCase(any(), any()) } returns UnknownError().left()

        // WHEN
        val viewModel = createViewModel(searchDelegate = createSearchDelegate())
        viewModel.onFavoriteClick(createZeroBalanceAccountUiItem(id = "3", groupCoins))

        // THEN
        val expectedMessage = TextResource(CoreStrings.wallets_favorites_error)
        Truth.assertThat(viewModel.events.first()).isEqualTo(SnackbarMessageEvent(expectedMessage))
    }

    private fun createZeroBalanceAccountUiItem(
        id: String,
        group: AccountGroup,
        isFavorite: Boolean = false
    ): WalletsAccountUiItem {
        return WalletsAccountUiItem(
            id = id,
            currencyName = "USD",
            number = id,
            balanceCurrencyAlphabeticCode = testCurrency.alphabeticCode,
            balance = "0",
            balanceConverted = "0 USD".asTextResource(),
            balanceOnHold = "0",
            groupName = group.name,
            isFavorite = isFavorite,
        )
    }

    private fun createZeroBalanceAccount(
        id: String,
        group: AccountGroup,
        isFavorite: Boolean = false
    ): Account {
        return Account.fixture(
            id = id,
            number = id,
            group = group,
            statement = zeroBalanceStatement,
            favourite = isFavorite,
        )
    }

    private fun createViewModel(
        searchDelegate: SearchWalletSearchDelegate = this.searchDelegate
    ) = SearchWalletViewModel(
        isBalanceHidden = false,
        searchDelegate = searchDelegate,
        walletsAccountUiItemMapper = walletsAccountUiItemMapper,
        getWalletsInfoUseCase = getWalletsInfoUseCase,
        observeAccountsChangesUseCase = observeAccountsChangesUseCase,
        setAccountFavoriteStateUseCase = setAccountFavoriteStateUseCase,
    )

    private fun createSearchDelegate() = SearchWalletSearchDelegate(
        eventDispatcher = QueueEventDispatcher(),
        defaultDispatcher = UnconfinedTestDispatcher(),
        uiStateHolder = DefaultUiStateHolder(B2SearchUiState()),
    )

    companion object {
        private val testCurrency = Currency.USD
        private const val GROUP_NAME_COINS = "Coins"
        private val zeroBalanceStatement = AccountStatement.fixture(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)
        private val groupCoins = AccountGroup.fixture(id = "4", GROUP_NAME_COINS)
    }
}
