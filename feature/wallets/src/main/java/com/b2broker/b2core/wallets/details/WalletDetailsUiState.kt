package com.b2broker.b2core.wallets.details

import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color
import com.b2broker.b2core.model.transaction.Transaction
import com.b2broker.b2core.model.transaction.TransactionStatus
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import com.b2broker.b2core.text.asTextResource
import com.b2broker.b2core.wallets.LoadingState
import com.b2broker.b2core.wallets.ui.WalletsOperationButtonUiItem
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Immutable
internal data class WalletDetailsUiState(
    val loadingState: LoadingState = LoadingState.LOADING,
    val isFavorite: Boolean = false,
    val headerState: WalletDetailsHeaderUiState = WalletDetailsHeaderUiState(),
    val operations: ImmutableList<WalletsOperationButtonUiItem> = persistentListOf(),
    val transactions: ImmutableList<WalletDetailsTransactionItemUiState> = persistentListOf(),
    val seeMoreTransactionsAvailable: Boolean = false,
) {
    val isSeeMoreVisible: Boolean
        get() = seeMoreTransactionsAvailable && transactions.isNotEmpty()

    companion object {
        fun fixture(
            loadingState: LoadingState = LoadingState.COMPLETED,
            isFavorite: Boolean = false,
            header: WalletDetailsHeaderUiState = WalletDetailsHeaderUiState(
                accountNumber = "236666",
                currencyAlphabeticCode = "ETH",
                balance = "432.********",
                convertedBalance = "500.00 USD".asTextResource(),
                availableBalance = "422.********",
                onHoldBalance = "102.********",
            ),
            operations: ImmutableList<WalletsOperationButtonUiItem> = persistentListOf(
                WalletsOperationButtonUiItem("Deposit".asTextResource()) {},
                WalletsOperationButtonUiItem("Withdraw".asTextResource()) {},
                WalletsOperationButtonUiItem("Transfer".asTextResource()) {},
                WalletsOperationButtonUiItem("Internal Transfer".asTextResource()) {},
                WalletsOperationButtonUiItem("Exchange".asTextResource()) {},
            ),
            transactions: ImmutableList<WalletDetailsTransactionItemUiState> = persistentListOf(
                WalletDetailsTransactionItemUiState(
                    id = 0,
                    mainInfo = persistentListOf(
                        WalletDetailsTransactionInfoToken(text = "1,123.0000005 ".asTextResource()),
                        WalletDetailsTransactionInfoToken(text = "ETF".asTextResource(), isColorSecondary = true),
                    ),
                    type = "Withdraw".asTextResource(),
                    time = "2 hours ago".asTextResource(),
                    status = TransactionStatus.DONE,
                ),
                WalletDetailsTransactionItemUiState(
                    id = 1,
                    mainInfo = persistentListOf(
                        WalletDetailsTransactionInfoToken(text = "0.00324045 ".asTextResource()),
                        WalletDetailsTransactionInfoToken(text = "ETH".asTextResource(), isColorSecondary = true),
                        WalletDetailsTransactionInfoToken(TextResource(CoreStrings.wallets_details_transactions_main_info_exchange_divider), isColorSecondary = true),
                        WalletDetailsTransactionInfoToken(text = "0.00324045 ".asTextResource()),
                        WalletDetailsTransactionInfoToken(text = "BUSD".asTextResource(), isColorSecondary = true),
                    ),
                    type = "Exchange".asTextResource(),
                    time = "21/05/24 12:00".asTextResource(),
                    status = TransactionStatus.PENDING,
                ),
            ),
            seeMoreTransactionAvailable: Boolean = false,
        ) = WalletDetailsUiState(
            loadingState = loadingState,
            isFavorite = isFavorite,
            headerState = header,
            operations = operations,
            transactions = transactions,
            seeMoreTransactionsAvailable = seeMoreTransactionAvailable,
        )
    }
}

@Immutable
internal data class WalletDetailsTransactionItemUiState(
    val id: Long,
    val mainInfo: ImmutableList<WalletDetailsTransactionInfoToken> = persistentListOf(),
    val type: TextResource = "".asTextResource(),
    val time: TextResource? = null,
    val status: TransactionStatus = TransactionStatus.UNKNOWN,
    val transaction: Transaction? = null,
)

@Immutable
internal data class WalletDetailsHeaderUiState(
    val accountNumber: String = "",
    val currencyAlphabeticCode: String = "",
    val balance: String = "",
    val convertedBalance: TextResource = "".asTextResource(),
    val availableBalance: String = "",
    val onHoldBalance: String = "",
    val isClipboardManagerAvailable: Boolean = true
)

@Immutable
internal data class WalletDetailsTransactionInfoToken(
    val text: TextResource = "".asTextResource(),
    val isColorSecondary: Boolean = false,
)

@Immutable
internal data class WalletDetailsTransactionStatusUiState(
    val name: TextResource = "".asTextResource(),
    val color: Color = Color.Unspecified,
)
