package com.b2broker.b2core.wallets.searchwallet.event

import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.navigation.SharedRoute
import com.b2broker.b2core.navigation.push
import com.b2broker.b2core.navigation.result.contract.WalletDetailsContract
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.handler.EventHandler
import com.b2broker.b2core.presentation.navigator.rootNavigator
import com.b2broker.b2core.wallets.WalletsEvent
import javax.inject.Inject

internal class SearchWalletEventHandler @Inject constructor(
    private val navigator: Navigator,
) : EventHandler() {

    override fun handle(event: Event) {
        val rootNavigator = navigator.rootNavigator()
        when (event) {
            is WalletsEvent.WalletDetails -> {
                val main = navigator.items.first()
                val request = WalletDetailsContract.Request(
                    accountId = event.args.accountId,
                    screenKey = main.key,
                )
                rootNavigator.push(SharedRoute.WalletDetails(request))
            }
            else -> super.handle(event)
        }
    }
}
