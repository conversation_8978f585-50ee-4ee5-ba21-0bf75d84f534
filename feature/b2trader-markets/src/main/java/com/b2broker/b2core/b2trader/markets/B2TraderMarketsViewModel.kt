package com.b2broker.b2core.b2trader.markets

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.b2broker.b2core.b2trader.markets.event.B2TraderMarketsEvent
import com.b2broker.b2core.b2trader.markets.model.SearchState
import com.b2broker.b2core.coroutines.di.DefaultDispatcher
import com.b2broker.b2core.domain.b2trader.acounts.ObserveCurrentB2TraderAccountIdUseCase
import com.b2broker.b2core.domain.b2trader.markets.GetB2TraderFilteredMarketsUseCase
import com.b2broker.b2core.domain.b2trader.markets.GetB2TraderMarketsByCategoryUseCase
import com.b2broker.b2core.domain.b2trader.markets.GetB2TraderMarketsExtendedUseCase
import com.b2broker.b2core.domain.b2trader.markets.GetB2TraderUpdatedMarketsExtendedUseCase
import com.b2broker.b2core.domain.b2trader.markets.ObserveB2TraderTradingDataUseCase
import com.b2broker.b2core.domain.b2trader.markets.SaveMarketIdUseCase
import com.b2broker.b2core.domain.b2trader.markets.mapper.B2TraderMarketExtendedMapper
import com.b2broker.b2core.model.b2trader.markets.B2TraderMarketExtended
import com.b2broker.b2core.model.b2trader.markets.B2TraderTradingData
import com.b2broker.b2core.model.b2trader.markets.MarketCategory
import com.b2broker.b2core.model.b2trader.markets.MarketSelectionType.COMMON_MARKET_SELECTION
import com.b2broker.b2core.model.b2trader.markets.MarketSelectionType.LOCAL_MARKET_SELECTION
import com.b2broker.b2core.navigation.args.B2TraderMarketsArgs
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.state.UiStateHolder
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toImmutableSet
import kotlinx.collections.immutable.toPersistentSet
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.math.min

@HiltViewModel(assistedFactory = B2TraderMarketsViewModel.Factory::class)
internal class B2TraderMarketsViewModel @AssistedInject constructor(
    private val uiStateHolder: UiStateHolder<B2TraderMarketsUiState>,
    private val eventDispatcher: EventDispatcher,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    private val getFilteredMarketsUseCase: GetB2TraderFilteredMarketsUseCase,
    private val getB2TraderMarketsExtendedUseCase: GetB2TraderMarketsExtendedUseCase,
    private val observeB2TraderTradingDataUseCase: ObserveB2TraderTradingDataUseCase,
    private val getB2TraderMarketsByCategoryUseCase: GetB2TraderMarketsByCategoryUseCase,
    private val getB2TraderUpdatedMarketsExtendedUseCase: GetB2TraderUpdatedMarketsExtendedUseCase,
    private val observeCurrentB2TraderAccountIdUseCase: ObserveCurrentB2TraderAccountIdUseCase,
    private val saveMarketIdUseCase: SaveMarketIdUseCase,
    private val b2TraderMarketExtendedMapper: B2TraderMarketExtendedMapper,
    @Assisted private val args: B2TraderMarketsArgs,
) : ViewModel(),
    UiStateHolder<B2TraderMarketsUiState> by uiStateHolder,
    EventDispatcher by eventDispatcher {

    @AssistedFactory
    interface Factory {
        fun create(@Assisted args: B2TraderMarketsArgs): B2TraderMarketsViewModel
    }

    private var marketDataObserverJob: Job? = null
    private var marketExtendedObserverJob: Job? = null

    private var observedMarketsIndexes = 0 to 0
    private var accountId: String? = null

    private val filteringFlow = uiStateFlow
        .distinctUntilChanged { old, new ->
            old.searchQuery == new.searchQuery && old.selectedCategory == new.selectedCategory
        }
        .map { it.searchQuery }
        .shareIn(viewModelScope, SharingStarted.WhileSubscribed(), replay = 1)

    init {
        accountId = args.accountId
        loadData()

        observeAccountId()
    }

    private fun observeAccountId() {
        viewModelScope.launch {
            observeCurrentB2TraderAccountIdUseCase.invoke()
                .collect { newAccountId ->
                    if (accountId == newAccountId) return@collect

                    newAccountId?.let { id ->
                        accountId = id
                        loadData()
                    }
                }
        }
    }

    private fun loadData() {
        updateState { copy(isInProgress = true, loadingError = false) }

        marketExtendedObserverJob?.cancel()
        marketExtendedObserverJob = viewModelScope.launch {
            val id = accountId ?: return@launch

            getB2TraderMarketsExtendedUseCase(id)
                .onLeft { updateState { copy(isInProgress = false, loadingError = true) } }
                .onRight { marketsExtended ->
                    val markets = marketsExtended.markets
                        .map { market -> b2TraderMarketExtendedMapper.map(market to null) }
                        .toImmutableList()

                    updateState {
                        copy(
                            isInProgress = false,
                            allMarkets = markets,
                            filteredMarkets = markets,
                            categories = marketsExtended.categories.toImmutableList(),
                            inProgressMarkets = markets.map { it.marketId }.toPersistentSet(),
                            rootAsset = args.rootAsset,
                            marketId = args.marketId,
                        )
                    }

                    observeFilters(markets)
                }
        }
    }

    private fun observeFilters(markets: List<B2TraderMarketExtended>) {
        viewModelScope.launch {
            filteringFlow
                .collectLatest { filter ->
                    val filteredMarkets = getFilteredMarketsUseCase(
                        markets = markets,
                        searchQuery = filter,
                        categorizedMarkets = uiState.selectedCategoryMarkets,
                        isCategorySelected = uiState.isCategorySelected,
                        excludedMarkets = args.excludedMarkets,
                    )
                    updateState {
                        copy(
                            filteredMarkets = filteredMarkets.toImmutableList(),
                            inProgressMarkets = filteredMarkets.map { it.marketId }.toImmutableSet(),
                        )
                    }

                    if (uiState.filteredMarkets.isNotEmpty()) {
                        observeFilteredMarketData(0, min(MARKET_DATA_OBSERVATION_LIMIT, uiState.filteredMarkets.size))
                    }
                }
        }
    }

    fun onScroll(firstVisibleIndex: Int, lastVisibleIndex: Int) {
        if (isObservationUpdateRequired(firstVisibleIndex, lastVisibleIndex)) {
            // requesting (MARKET_DATA_PRELOAD_LIMIT / 2) elements before and after the middle visible element
            val midVisibleIndex = firstVisibleIndex + (lastVisibleIndex - firstVisibleIndex) / 2
            val start = max(midVisibleIndex - MARKET_DATA_OBSERVATION_LIMIT / 2, 0)
            val end = min(start + MARKET_DATA_OBSERVATION_LIMIT - 1, uiState.filteredMarkets.size)

            observeFilteredMarketData(start, end)
        }
    }

    private fun observeFilteredMarketData(startIndex: Int, endIndex: Int) {
        marketDataObserverJob?.cancel()
        marketDataObserverJob = viewModelScope.launch(defaultDispatcher) {
            val accountId = accountId ?: return@launch

            observedMarketsIndexes = startIndex to endIndex

            val ids = uiState.filteredMarkets.subList(startIndex, endIndex)
                .map { it.marketId }

            observeB2TraderTradingDataUseCase.invoke(accountId, ids)
                .collectLatest { either ->
                    either
                        .onRight { tradingData ->
                            updateState {
                                copy(inProgressMarkets = (uiState.inProgressMarkets - ids.toSet()).toPersistentSet())
                            }
                            handleMarketDataUpdate(tradingData)
                        }
                }
        }
    }

    private fun isObservationUpdateRequired(firstVisibleIndex: Int, lastVisibleIndex: Int): Boolean {
        val (firstObservedIndex, lastObservedIndex) = observedMarketsIndexes

        val topThresholdReached = firstVisibleIndex > MARKET_DATA_OBSERVATION_UPDATE_THRESHOLD && firstVisibleIndex < firstObservedIndex + MARKET_DATA_OBSERVATION_UPDATE_THRESHOLD
        val bottomThresholdReached = lastVisibleIndex > lastObservedIndex - MARKET_DATA_OBSERVATION_UPDATE_THRESHOLD

        return topThresholdReached || bottomThresholdReached
    }

    private suspend fun handleMarketDataUpdate(tradingData: B2TraderTradingData) {
        val allMarkets = getB2TraderUpdatedMarketsExtendedUseCase.invoke(
            uiState.allMarkets,
            tradingData.markets,
        ).toImmutableList()

        val filteredMarkets = getFilteredMarketsUseCase.invoke(
            markets = allMarkets,
            searchQuery = uiState.searchQuery,
            categorizedMarkets = uiState.selectedCategoryMarkets,
            isCategorySelected = uiState.isCategorySelected,
            excludedMarkets = args.excludedMarkets,
        )

        updateState {
            copy(
                allMarkets = allMarkets.toImmutableList(),
                filteredMarkets = filteredMarkets.toImmutableList(),
                isInProgress = false,
                loadingError = false,
            )
        }
    }

    fun onReloadClick() {
        loadData()
    }

    fun onSearchQueryChange(newQuery: String) {
        updateState { copy(searchQuery = newQuery) }
    }

    fun onMarketClick(market: B2TraderMarketExtended) {
        viewModelScope.launch {
            when (args.marketSelectionType) {
                COMMON_MARKET_SELECTION -> saveMarketIdUseCase(market.marketId).onRight {
                    dispatch(B2TraderMarketsEvent.SelectB2TraderMarket)
                }

                LOCAL_MARKET_SELECTION -> dispatch(
                    B2TraderMarketsEvent.SelectLocalMarket(market.marketId.id),
                )
            }
        }
    }

    fun onCategoryClick(category: MarketCategory) {
        if (category == uiState.selectedCategory && category.parentId == null) {
            updateState {
                copy(
                    selectedCategory = null,
                    selectedCategoryMarkets = persistentSetOf(),
                )
            }
            return
        }

        val requestedCategory = if (category == uiState.selectedCategory && category.parentId != null) {
            category.parentId?.let { uiState.categoryMap[it] } ?: category
        } else {
            category
        }

        updateMarketsByCategory(requestedCategory)
    }

    fun onClearCategoryClick() {
        updateState {
            copy(
                selectedCategory = null,
                selectedCategoryMarkets = persistentSetOf(),
            )
        }
    }

    fun onSearchClick() {
        updateState {
            copy(searchState = SearchState.Active)
        }
    }

    fun onCloseSearchClick() {
        updateState {
            copy(searchState = SearchState.NotActive)
        }
    }

    private fun updateMarketsByCategory(requestedCategory: MarketCategory) {
        viewModelScope.launch {
            val accountId = accountId ?: return@launch

            updateState {
                copy(
                    loadingError = false,
                    isCategoryLoading = true,
                )
            }

            getB2TraderMarketsByCategoryUseCase(requestedCategory.categoryId, accountId)
                .onLeft {
                    updateState {
                        copy(
                            loadingError = true,
                            isCategoryLoading = false,
                        )
                    }
                }
                .onRight { markets ->
                    observedMarketsIndexes = 0 to 0
                    updateState {
                        copy(
                            isCategoryLoading = false,
                            selectedCategory = requestedCategory,
                            selectedCategoryMarkets = markets.toPersistentSet(),
                        )
                    }
                }
        }
    }

    companion object {
        private const val MARKET_DATA_OBSERVATION_UPDATE_THRESHOLD = 10
        private const val MARKET_DATA_OBSERVATION_LIMIT = 99
    }
}
