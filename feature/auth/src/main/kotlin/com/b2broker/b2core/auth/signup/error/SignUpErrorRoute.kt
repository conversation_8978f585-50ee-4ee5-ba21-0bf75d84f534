package com.b2broker.b2core.auth.signup.error

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import cafe.adriel.voyager.core.screen.ScreenKey
import cafe.adriel.voyager.navigator.LocalNavigator
import cafe.adriel.voyager.navigator.currentOrThrow
import com.b2broker.b2core.auth.signup.event.SignUpEventHandler
import com.b2broker.b2core.presentation.Route
import com.b2broker.b2core.presentation.event.handler.rememberEventHandler
import com.b2broker.b2core.presentation.getEventfullViewModel
import com.b2broker.b2core.presentation.uniqueScreenKey
import kotlinx.parcelize.Parcelize

@Parcelize
internal class SignUpErrorRoute(
    override val key: ScreenKey = uniqueScreenKey(),
) : Route {

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.currentOrThrow
        val eventHandler = rememberEventHandler { SignUpEventHandler(navigator) }
        val viewModel = getEventfullViewModel<SignUpErrorViewModel>(
            eventHandler = eventHandler,
        )

        SignUpErrorScreen(
            onBackClick = viewModel::onBackClick,
            modifier = Modifier
                .fillMaxSize()
                .safeDrawingPadding(),
        )
    }
}
