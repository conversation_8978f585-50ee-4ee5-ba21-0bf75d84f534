package com.b2broker.b2core.auth.twofactor.google

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import com.b2broker.b2core.designsystem.appspecific.basescreen.action.B2ActionBaseScreen
import com.b2broker.b2core.designsystem.appspecific.basescreen.action.B2ActionButtonUiState
import com.b2broker.b2core.designsystem.appspecific.code.B2CodeSection
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import com.b2broker.b2core.text.asString

@Composable
internal fun TfaGoogleScreen(
    uiState: TfaGoogleUiState,
    onCodeChange: (String) -> Unit,
    onSubmitClick: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    B2ActionBaseScreen(
        title = stringResource(id = CoreStrings.two_fa_google_title),
        bodyTitle = stringResource(id = CoreStrings.two_fa_google_message),
        bodyDescription = stringResource(id = CoreStrings.two_fa_google_message_description),
        bodyContent = {
            B2CodeSection(
                code = uiState.code,
                codeLength = 6,
                onCodeChange = onCodeChange,
                onKeyboardAction = onSubmitClick,
                codeError = uiState.codeError?.asString(),
                enabled = !uiState.isInProgress,
            )
        },
        onBackClick = onBackClick,
        positiveButton = B2ActionButtonUiState(
            onClick = { onSubmitClick() },
            isEnabled = uiState.isSubmitEnabled,
            isLoading = uiState.isInProgress,
            text = stringResource(id = CoreStrings.two_fa_btn_submit),
        ),
        modifier = modifier,
    )
}

@PreviewLightDark
@Composable
private fun ScreenPreview() = PreviewContainer {
    TfaGoogleScreen(
        uiState = TfaGoogleUiState(
            code = "1234",
            isInProgress = false,
            codeError = TextResource("Code is invalid"),
        ),
        onCodeChange = {},
        onSubmitClick = {},
        onBackClick = {},
    )
}
