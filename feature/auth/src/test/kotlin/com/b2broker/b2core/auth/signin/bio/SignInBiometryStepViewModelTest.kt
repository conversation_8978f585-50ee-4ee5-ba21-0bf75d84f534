package com.b2broker.b2core.auth.signin.bio

import arrow.core.left
import arrow.core.right
import com.b2broker.b2core.auth.signin.event.SignInEvent
import com.b2broker.b2core.domain.auth.SwitchBiometricAuthUseCase
import com.b2broker.b2core.model.DefaultError
import com.b2broker.b2core.presentation.event.default.SnackbarMessageEvent
import com.b2broker.b2core.presentation.event.dispatcher.QueueEventDispatcher
import com.b2broker.b2core.test.ext.MainCoroutineUnconfinedExtension
import com.google.common.truth.Truth.assertThat
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MainCoroutineUnconfinedExtension::class)
internal class SignInBiometryStepViewModelTest {

    private lateinit var viewModel: SignInBiometryStepViewModel
    private val switchBiometricUseCase: SwitchBiometricAuthUseCase = mockk()

    @BeforeEach
    fun setUp() {
        viewModel = SignInBiometryStepViewModel(
            eventDispatcher = QueueEventDispatcher(),
            switchBiometricUseCase = switchBiometricUseCase,
        )
    }

    @Test
    fun `GIVEN current vm WHEN next THEN BackEvent`() = runTest {
        // WHEN
        viewModel.next()

        // THEN
        assertThat(viewModel.events.first()).isInstanceOf(SignInEvent.Success::class.java)
    }

    @Test
    fun `GIVEN switchBiometricUseCase success WHEN enableBio THEN BackEvent`() = runTest {
        // WHEN
        coEvery { switchBiometricUseCase(true) } returns Unit.right()
        viewModel.enableBio()

        // THEN
        assertThat(viewModel.events.first()).isInstanceOf(SignInEvent.Success::class.java)
    }

    @Test
    fun `GIVEN switchBiometricUseCase error WHEN enableBio THEN show message`() = runTest {
        // WHEN
        val error = DefaultError("Oh no!")
        coEvery { switchBiometricUseCase(true) } returns error.left()
        viewModel.enableBio()

        // THEN
        assertThat(viewModel.events.first()).isInstanceOf(SnackbarMessageEvent::class.java)
    }
}
