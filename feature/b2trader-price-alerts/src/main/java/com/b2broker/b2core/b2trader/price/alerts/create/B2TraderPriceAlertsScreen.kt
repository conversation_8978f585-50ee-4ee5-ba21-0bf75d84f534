package com.b2broker.b2core.b2trader.price.alerts.create

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.b2trader.price.alerts.components.AllAlertsTab
import com.b2broker.b2core.b2trader.price.alerts.components.CurrentMarketTab
import com.b2broker.b2core.b2trader.price.alerts.components.PriceAlertsTabs
import com.b2broker.b2core.b2trader.price.alerts.create.model.LoadingState
import com.b2broker.b2core.b2trader.price.alerts.create.model.PriceAlertsInputCallbacks
import com.b2broker.b2core.b2trader.price.alerts.create.model.PriceAlertsTabType
import com.b2broker.b2core.b2trader.price.alerts.delegate.CurrentMarketState
import com.b2broker.b2core.designsystem.component.B2NestedScaffold
import com.b2broker.b2core.designsystem.component.topbar.B2TopBar
import com.b2broker.b2core.designsystem.componentV2.B2TextButton
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.model.b2trader.markets.MarketType
import com.b2broker.b2core.model.b2trader.notification.ExtendedMarketPriceSubscription
import com.b2broker.b2core.model.b2trader.notification.MarketPriceSubscriptionItem
import com.b2broker.b2core.text.CoreStrings
import kotlinx.collections.immutable.persistentListOf
import java.math.BigDecimal

@Composable
internal fun B2TraderPriceAlertsScreen(
    state: B2TraderPriceAlertsUiState,
    inputCallbacks: PriceAlertsInputCallbacks,
    onBackClick: () -> Unit,
    onTabClick: (PriceAlertsTabType) -> Unit,
    onAllAlertsReloadPageClick: () -> Unit,
    onAllAlertsTabScrolling: (index: Int) -> Unit,
    onItemClick: (MarketPriceSubscriptionItem) -> Unit,
    onItemRemoveClick: (MarketPriceSubscriptionItem) -> Unit,
    onReloadClick: () -> Unit,
    onAddMarketClick: () -> Unit,
    onResetClick: () -> Unit,
    onRemoveClick: (ExtendedMarketPriceSubscription) -> Unit,
    onAddAlertClick: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    B2NestedScaffold(
        modifier = modifier,
        topBar = {
            B2TopBar(
                title = stringResource(CoreStrings.b2trader_price_alerts_price_control),
                onBackClick = onBackClick,
            ) {
                if (state.isAddMarketVisible) {
                    B2TextButton(
                        innerPadding = PaddingValues(horizontal = 16.dp),
                        onClick = onAddMarketClick,
                        text = stringResource(CoreStrings.b2trader_price_alerts_add_market),
                        testTag = "",
                    )
                }
            }
        },
    ) { innerPaddings ->
        Column(
            modifier = Modifier
                .padding(innerPaddings)
                .fillMaxSize(),
        ) {
            PriceAlertsTabs(
                selectedTab = state.selectedTab,
                onTabClick = onTabClick,
            )

            when (state.selectedTab) {
                PriceAlertsTabType.CURRENT_MARKET -> CurrentMarketTab(
                    state = state.currentMarketState,
                    currentMarket = state.currentMarket,
                    isSaveButtonEnabled = state.isSaveButtonEnabled,
                    loadingState = state.loadingState,
                    onItemClick = onItemClick,
                    onItemRemoveClick = onItemRemoveClick,
                    onReloadClick = onReloadClick,
                    onPriceEdit = inputCallbacks.onPriceEdit,
                    onPriceRefresh = inputCallbacks.onPriceRefresh,
                    onPercentEdit = inputCallbacks.onPercentEdit,
                    onPriceClick = inputCallbacks.onPriceClick,
                    onPercentRefresh = inputCallbacks.onPercentRefresh,
                    onSaveClick = inputCallbacks.onSaveClick,
                    onResetClick = onResetClick,
                    onEditClick = inputCallbacks.onEditClick,
                    onValidatePrice = inputCallbacks.onValidatePrice,
                )

                PriceAlertsTabType.ALL_ALERTS -> AllAlertsTab(
                    state = state.allAlertsState,
                    loadingState = state.loadingState,
                    markets = state.markets,
                    onReloadPageClick = onAllAlertsReloadPageClick,
                    onTabScrolling = onAllAlertsTabScrolling,
                    onItemClick = onItemClick,
                    onItemRemoveClick = onItemRemoveClick,
                    onAddMarketClick = onAddMarketClick,
                    onRemoveClick = onRemoveClick,
                    onAddAlertClick = onAddAlertClick,
                    onReloadClick = onReloadClick,
                )
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun B2TraderPriceAlertsScreenPreview() = PreviewContainer {
    B2TraderPriceAlertsScreen(
        state = B2TraderPriceAlertsUiState.fixture(
            loadingState = LoadingState.COMPLETED,
            markets = persistentListOf(
                ExtendedMarketPriceSubscription.fixture(
                    marketId = "BTC/USDT",
                    displayName = "BTC/USDT",
                    type = MarketType.SPOT,
                    subscriptions = persistentListOf(
                        MarketPriceSubscriptionItem.fixture(price = BigDecimal("10000.00")),
                        MarketPriceSubscriptionItem.fixture(price = BigDecimal("10000.00")),
                        MarketPriceSubscriptionItem.fixture(price = BigDecimal("10000.00")),
                        MarketPriceSubscriptionItem.fixture(price = BigDecimal("10000.00")),
                    ),
                ),
            ),
            currentMarketState = CurrentMarketState.fixture(
                marketId = "BTC/USDT",
                displayName = "BTC/USDT",
                currentPrice = BigDecimal("9999.99"),
            ),
        ),
        inputCallbacks = PriceAlertsInputCallbacks.fixture(),
        onBackClick = {},
        onTabClick = {},
        onAllAlertsTabScrolling = {},
        onAllAlertsReloadPageClick = {},
        onItemClick = {},
        onItemRemoveClick = {},
        onReloadClick = {},
        onAddMarketClick = {},
        onResetClick = {},
        onRemoveClick = {},
        onAddAlertClick = {},
    )
}
