package com.b2broker.b2core.b2trader.price.alerts.components.loading

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.componentV2.placeholder.B2Skeleton
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme

private const val MARKET_CONTENT_COUNT = 3

@Composable
internal fun MarketContentLoading(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
    ) {
        Row(
            modifier = Modifier.padding(bottom = 9.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            B2Skeleton(
                modifier = Modifier.size(24.dp),
                shape = B2Theme.shapes.small,
            )

            Spacer(modifier = Modifier.width(8.dp))

            B2Skeleton(
                modifier = Modifier
                    .height(16.dp)
                    .width(112.dp),
            )
        }

        Spacer(modifier = Modifier.height(6.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
        ) {
            repeat(MARKET_CONTENT_COUNT) {
                B2Skeleton(
                    modifier = Modifier
                        .width(72.dp)
                        .height(32.dp),
                )

                Spacer(modifier = Modifier.width(8.dp))
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview() = PreviewContainer {
    MarketContentLoading()
}
