package com.b2broker.b2core.b2trader.price.alerts.editor.event

import cafe.adriel.voyager.navigator.Navigator
import com.b2broker.b2core.b2trader.price.alerts.editor.event.B2TraderPriceAlertEditorEvent.SaveResult
import com.b2broker.b2core.navigation.result.contract.B2TraderPriceAlertEditorContract
import com.b2broker.b2core.navigation.result.setResult
import com.b2broker.b2core.presentation.event.Event
import com.b2broker.b2core.presentation.event.default.BackEvent
import com.b2broker.b2core.presentation.event.handler.EventHandler
import com.b2broker.b2core.presentation.navigator.rootNavigator

internal class B2TraderPriceAlertEditorEventHandler(
    private val navigator: Navigator
) : EventHandler() {

    override fun handle(event: Event) {
        val rootNavigator = navigator.rootNavigator()

        when (event) {
            is BackEvent -> navigator.pop()

            is SaveResult -> {
                rootNavigator.setResult(event.request, B2TraderPriceAlertEditorContract.Result)
                navigator.pop()
            }

            else -> super.handle(event)
        }
    }
}
