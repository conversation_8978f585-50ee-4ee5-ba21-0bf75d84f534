package com.b2broker.b2core.profile

import com.b2broker.b2core.designsystem.CoreDrawable

internal enum class ProfileMenuUiItemType {
    PROFILE_INFO,
    NOTIFICATION,
    SECURITY,
    WITHDRAWAL_PRESETS,
    SUPPORT_CENTER,
}

internal val ProfileMenuUiItemType.iconResId
    get() = when (this) {
        ProfileMenuUiItemType.PROFILE_INFO -> CoreDrawable.ic_user_24
        ProfileMenuUiItemType.NOTIFICATION -> CoreDrawable.ic_notification_settings_24
        ProfileMenuUiItemType.SECURITY -> CoreDrawable.ic_authentication_24
        ProfileMenuUiItemType.WITHDRAWAL_PRESETS -> CoreDrawable.ic_document_24
        ProfileMenuUiItemType.SUPPORT_CENTER -> CoreDrawable.ic_support_24
    }
