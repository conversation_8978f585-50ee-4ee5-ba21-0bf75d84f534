package com.b2broker.b2core.b2trader.accounts.edit

import android.os.Parcelable
import androidx.compose.runtime.Immutable
import com.b2broker.b2core.presentation.HasProgress
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
@Immutable
internal data class EditAccountUiState(
    val isInProgress: Boolean = false,
    val accountId: String = "",
    val accountName: String = "",
) : Parcelable, HasProgress<EditAccountUiState> {

    @IgnoredOnParcel
    val isSaveEnabled: Boolean = accountName.isNotBlank() && accountName.length <= NAME_LENGTH_MAX

    override fun updateProgress(isInProgress: Boolean): EditAccountUiState {
        return copy(isInProgress = isInProgress)
    }

    companion object {
        const val NAME_LENGTH_MAX = 30

        fun fixture(
            isInProgress: Boolean = false,
            accountId: String = "654",
            accountName: String = "accountName_2f7d",
        ) = EditAccountUiState(
            isInProgress = isInProgress,
            accountId = accountId,
            accountName = accountName,
        )
    }
}
