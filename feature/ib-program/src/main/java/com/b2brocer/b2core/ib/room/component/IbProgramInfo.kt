package com.b2brocer.b2core.ib.room.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2brocer.b2core.ib.room.IbFullInfoState
import com.b2brocer.b2core.ib.room.IbProgramRoomScreenTags
import com.b2broker.b2core.designsystem.R
import com.b2broker.b2core.designsystem.component.B2Icon
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.component.Spacer
import com.b2broker.b2core.designsystem.component.buttons.B2Button
import com.b2broker.b2core.designsystem.component.card.B2FilledCard
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.model.ibprogram.IbActiveProgramFull
import com.b2broker.b2core.text.CorePlurals
import com.b2broker.b2core.text.CoreStrings

@Composable
internal fun IbProgramInfo(
    state: IbFullInfoState.Content,
    isWithdrawEnable: Boolean,
    onAllClients: () -> Unit,
    onAdvancedLink: () -> Unit,
    onSharePartnerLink: () -> Unit,
    onRewardsClick: () -> Unit,
    onWithdrawClick: () -> Unit,
) {
    Column {
        IbProgramClients(
            clientsCount = state.info.countOfClients,
            onAllClients = onAllClients,
            modifier = Modifier.fillMaxWidth(),
        )
        Spacer(height = 16.dp)
        ReferralLink(
            onAdvancedLink = onAdvancedLink,
            onSharePartnerLink = onSharePartnerLink,
            modifier = Modifier.fillMaxWidth(),
        )
        Spacer(height = 16.dp)
        IbRoomWallet(
            isWithdrawEnable = isWithdrawEnable,
            wallet = state.info.wallet,
            onRewardsClick = onRewardsClick,
            onWithdrawClick = onWithdrawClick,
        )
    }
}

@Composable
private fun ReferralLink(
    onAdvancedLink: () -> Unit,
    onSharePartnerLink: () -> Unit,
    modifier: Modifier = Modifier,
) = B2FilledCard(
    modifier = modifier,
    contentPaddings = PaddingValues(0.dp),
) {
    Column {
        Row(
            modifier = Modifier.padding(
                top = 8.dp,
                start = 16.dp,
                end = 8.dp,
            ),
        ) {
            B2Text(
                modifier = Modifier
                    .padding(vertical = 8.dp)
                    .weight(1f),
                text = stringResource(CoreStrings.ib_program_room_partner_link_title),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = B2Theme.typography.headlineSmall,
                color = B2Theme.colors.onSurface,
            )
            B2Text(
                text = stringResource(CoreStrings.ib_program_room_show_advanced_link),
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .clickable { onAdvancedLink() }
                    .padding(horizontal = 8.dp, vertical = 4.dp)
                    .wrapContentWidth(Alignment.End)
                    .testTag(IbProgramRoomScreenTags.ADVANCED_LINK_BUTTON),
                style = B2Theme.typography.bodyLarge,
                color = B2Theme.colors.primary,
            )
        }
        Spacer(height = 8.dp)
        B2Button(
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .fillMaxWidth()
                .testTag(IbProgramRoomScreenTags.SHARE_BUTTON),
            text = stringResource(CoreStrings.ib_program_room_share),
            type = B2Button.Type.SECONDARY,
            icon = {
                B2Icon(
                    modifier = Modifier.size(24.dp),
                    drawableResId = R.drawable.ic_share_24,
                )
            },
            size = B2Button.Size.M,
            onClick = onSharePartnerLink,
        )
    }
}

@Composable
private fun IbProgramClients(
    clientsCount: Int,
    onAllClients: () -> Unit,
    modifier: Modifier = Modifier
) = B2FilledCard(
    modifier = modifier.testTag(IbProgramRoomScreenTags.CLIENTS_ROW),
    contentPaddings = PaddingValues(0.dp),
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(
            top = 8.dp,
            start = 16.dp,
            end = 8.dp,
            bottom = 8.dp,
        ),
    ) {
        if (clientsCount > 0) {
            B2Text(
                modifier = Modifier
                    .padding(vertical = 8.dp)
                    .testTag(IbProgramRoomScreenTags.CLIENT_TEXT),
                text = pluralStringResource(CorePlurals.ib_program_room_clients, clientsCount),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = B2Theme.typography.headlineSmall,
                color = B2Theme.colors.onSurface,
            )

            B2Text(
                modifier = Modifier
                    .padding(start = 6.dp)
                    .weight(1f)
                    .testTag(IbProgramRoomScreenTags.CLIENTS_AMOUNT),
                text = clientsCount.toString(),
                style = B2Theme.typography.headlineSmall,
                color = B2Theme.colors.onSurfaceVariant,
            )
            B2Text(
                text = stringResource(CoreStrings.ib_program_room_show_clients_action),
                modifier = Modifier
                    .testTag(IbProgramRoomScreenTags.SHOW_BUTTON)
                    .clickable { onAllClients() }
                    .padding(horizontal = 8.dp, vertical = 4.dp)
                    .wrapContentWidth(Alignment.End),
                style = B2Theme.typography.bodyLarge,
                color = B2Theme.colors.primary,
            )
        } else {
            B2Text(
                modifier = Modifier
                    .padding(vertical = 8.dp)
                    .testTag(IbProgramRoomScreenTags.NO_CLIENT_TEXT),
                text = stringResource(CoreStrings.ib_program_room_no_clients),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = B2Theme.typography.headlineSmall,
                color = B2Theme.colors.onSurface,
            )
        }
    }
}

@PreviewLightDark
@Composable
private fun IbProgramInfoPreview() = PreviewContainer {
    IbProgramInfo(
        state = IbFullInfoState.Content(
            IbActiveProgramFull.fixture(),
        ),
        isWithdrawEnable = true,
        onAllClients = {},
        onAdvancedLink = {},
        onSharePartnerLink = {},
        onRewardsClick = {},
        onWithdrawClick = {},
    )
}
