package com.b2brocer.b2core.ib.room

import com.b2broker.b2core.model.ibprogram.IbActiveProgram
import com.b2broker.b2core.model.ibprogram.IbActiveProgramFull
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

internal sealed interface IbProgramRoomUiState {
    data object Loading : IbProgramRoomUiState
    data object Error : IbProgramRoomUiState

    data class Content(
        val currentIbRoomProgram: IbRoomProgram = IbRoomProgram.fixture(),
        val programs: ImmutableList<IbActiveProgram>,
        val isWithdrawEnable: Boolean = false,
    ) : IbProgramRoomUiState {
        companion object {
            fun fixture(
                currentIbRoomProgram: IbRoomProgram = IbRoomProgram.fixture(),
                programs: ImmutableList<IbActiveProgram> = persistentListOf(
                    IbActiveProgram.fixture(),
                ),
                isWithdrawEnable: Boolean = false,
            ) = Content(
                currentIbRoomProgram = currentIbRoomProgram,
                programs = programs,
                isWithdrawEnable = isWithdrawEnable,
            )
        }
    }
}

internal data class IbRoomProgram(
    val activeProgram: IbActiveProgram,
    val infoState: IbFullInfoState
) {
    companion object {
        fun fixture(
            activeProgram: IbActiveProgram = IbActiveProgram.fixture(),
            infoState: IbFullInfoState = IbFullInfoState.Content(
                IbActiveProgramFull.fixture(),
            ),
        ) = IbRoomProgram(
            activeProgram = activeProgram,
            infoState = infoState,
        )
    }
}

internal sealed interface IbFullInfoState {
    data object Loading : IbFullInfoState
    data object Error : IbFullInfoState
    data class Content(
        val info: IbActiveProgramFull
    ) : IbFullInfoState
}
