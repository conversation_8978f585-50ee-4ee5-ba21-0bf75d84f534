package com.b2broker.b2core.verification.verification.sections

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.b2broker.b2core.designsystem.component.B2Text
import com.b2broker.b2core.designsystem.component.Spacer
import com.b2broker.b2core.designsystem.component.avatar.B2Avatar
import com.b2broker.b2core.designsystem.component.avatar.B2AvatarState.Medium
import com.b2broker.b2core.designsystem.component.buttons.B2Button
import com.b2broker.b2core.designsystem.component.card.B2FilledCard
import com.b2broker.b2core.designsystem.component.chart.B2LevelHorizontalBar
import com.b2broker.b2core.designsystem.preview.PreviewContainer
import com.b2broker.b2core.designsystem.theme.B2Theme
import com.b2broker.b2core.formatter.CurrencyFormatter
import com.b2broker.b2core.model.currency.Currency
import com.b2broker.b2core.model.verification.VerificationLimitsType
import com.b2broker.b2core.model.verification.VerificationLimitsType.Actual
import com.b2broker.b2core.model.verification.VerificationLimitsType.Blocked
import com.b2broker.b2core.model.verification.VerificationLimitsType.Unlimited
import com.b2broker.b2core.text.CoreStrings
import com.b2broker.b2core.text.TextResource
import com.b2broker.b2core.text.asString
import com.b2broker.b2core.verification.verification.component.LeadingIconType
import com.b2broker.b2core.verification.verification.component.StatusCell
import java.math.BigDecimal

@Composable
internal fun VerificationLevelSection(
    avatarUrl: String?,
    currentLevel: Int,
    maxLevel: Int,
    currency: Currency,
    dailyDeposit: VerificationLimitsType,
    dailyWithdraw: VerificationLimitsType,
    transferMin: VerificationLimitsType,
    autoWithdraw: VerificationLimitsType,
    isUpgradeAvailable: Boolean,
    isUpgradeInProgress: Boolean,
    isUnderReview: Boolean,
    isUpgradeEnabled: Boolean,
    onUpgradeClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    B2FilledCard(
        modifier = modifier,
    ) {
        Column {
            LevelHeader(
                currentLevel = currentLevel,
                levelAmount = maxLevel,
                avatarUrl = avatarUrl,
            )

            Spacer(height = 24.dp)

            B2Text(
                text = stringResource(id = CoreStrings.verification_level_limits),
                style = B2Theme.typography.headlineSmall,
                color = B2Theme.colors.onSurface,
            )

            Spacer(height = 16.dp)

            StatusCell(
                key = stringResource(id = CoreStrings.verification_daily_deposit),
                value = dailyDeposit.provideFormattedLimit(currency).asString(),
                leadingIconType = dailyDeposit.provideLimitIndicator(),
            )

            Spacer(height = 8.dp)

            StatusCell(
                key = stringResource(id = CoreStrings.verification_daily_withdraw),
                value = dailyWithdraw.provideFormattedLimit(currency).asString(),
                leadingIconType = dailyWithdraw.provideLimitIndicator(),
            )

            Spacer(height = 8.dp)

            StatusCell(
                key = stringResource(id = CoreStrings.verification_transfer_min),
                value = transferMin.provideFormattedLimit(currency).asString(),
                leadingIconType = transferMin.provideLimitIndicator(),
            )

            Spacer(height = 8.dp)

            StatusCell(
                key = stringResource(id = CoreStrings.verification_auto_withdraw),
                value = autoWithdraw.provideFormattedLimit(currency).asString(),
                leadingIconType = autoWithdraw.provideLimitIndicator(),
            )

            if (isUpgradeAvailable) {
                Spacer(height = 24.dp)

                B2Button(
                    onClick = onUpgradeClick,
                    loading = isUpgradeInProgress,
                    enabled = isUpgradeEnabled,
                    text = stringResource(
                        id = if (isUnderReview) CoreStrings.verification_upgrade_under_review_button else CoreStrings.verification_upgrade_button,
                    ),
                    modifier = Modifier.testTag(VerificationLevelSectionTestTags.VERIFICATION_UPGRADE_BUTTON),
                )
            }
        }
    }
}

@Composable
private fun LevelHeader(
    currentLevel: Int,
    levelAmount: Int,
    avatarUrl: String?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier.fillMaxWidth(),
    ) {
        B2Avatar(
            modifier = Modifier.testTag(VerificationLevelSectionTestTags.VERIFICATION_AVATAR),
            state = if (avatarUrl != null) Medium.Filled(avatarUri = avatarUrl) else Medium.Empty,
        )

        Column(
            modifier = Modifier
                .padding(start = 16.dp, top = 2.dp, bottom = 2.dp),
        ) {
            B2Text(
                text = stringResource(id = CoreStrings.verification_your_level),
                style = B2Theme.typography.bodyMedium,
                color = B2Theme.colors.onSurfaceVariant,
            )
            B2Text(
                modifier = Modifier
                    .padding(top = 8.dp)
                    .testTag(VerificationLevelSectionTestTags.VERIFICATION_LEVEL),
                text = stringResource(id = CoreStrings.verification_level, currentLevel, levelAmount),
                style = B2Theme.typography.headlineMedium,
                color = B2Theme.colors.onSurface,
            )

            B2LevelHorizontalBar(
                modifier = Modifier.padding(top = 12.dp, bottom = 14.dp),
                totalLevels = levelAmount,
                currentLevel = currentLevel,
            )
        }
    }
}

private fun VerificationLimitsType.provideFormattedLimit(currency: Currency): TextResource {
    return when (this) {
        is Blocked -> TextResource(CoreStrings.verification_limit_blocked)
        is Unlimited -> TextResource(CoreStrings.verification_limit_unlimited)
        is Actual -> {
            val value = CurrencyFormatter.format(
                value = this.value,
                maxFractionDigits = currency.minorUnit,
                minFractionDigits = currency.minorUnit,
            )

            TextResource("$value ${currency.alphabeticCode}")
        }
    }
}

private fun VerificationLimitsType.provideLimitIndicator(): LeadingIconType? {
    return when (this) {
        is Blocked -> LeadingIconType.RED_DOT
        is Unlimited -> LeadingIconType.GREEN_DOT
        is Actual -> null
    }
}

@PreviewLightDark
@Composable
private fun VerificationLevelSectionPreview() = PreviewContainer {
    VerificationLevelSection(
        currentLevel = 2,
        maxLevel = 4,
        avatarUrl = "https://www.b2broker.net/",
        currency = Currency.USD,
        dailyDeposit = Actual(BigDecimal(100.0)),
        dailyWithdraw = Unlimited,
        transferMin = Blocked,
        autoWithdraw = Actual(BigDecimal(100.0)),
        isUpgradeAvailable = true,
        isUpgradeInProgress = false,
        isUnderReview = false,
        isUpgradeEnabled = true,
        onUpgradeClick = {},
    )
}

@PreviewLightDark
@Composable
private fun VerificationLevelSectionUpgradeUnavailablePreview() = PreviewContainer {
    VerificationLevelSection(
        currentLevel = 4,
        maxLevel = 4,
        avatarUrl = "https://www.b2broker.net/",
        currency = Currency.USD,
        dailyDeposit = Actual(BigDecimal(100.0)),
        dailyWithdraw = Unlimited,
        transferMin = Blocked,
        autoWithdraw = Actual(BigDecimal(100.0)),
        isUpgradeAvailable = false,
        isUpgradeInProgress = false,
        isUnderReview = false,
        isUpgradeEnabled = false,
        onUpgradeClick = {},
    )
}

object VerificationLevelSectionTestTags {
    const val VERIFICATION_AVATAR = "VerificationAvatar"
    const val VERIFICATION_UPGRADE_BUTTON = "VerificationUpgradeButton"
    const val VERIFICATION_LEVEL = "VerificationLevelText"
}
