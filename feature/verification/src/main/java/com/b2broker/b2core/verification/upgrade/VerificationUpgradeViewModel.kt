package com.b2broker.b2core.verification.upgrade

import android.net.Uri
import android.webkit.ValueCallback
import androidx.lifecycle.ViewModel
import com.b2broker.b2core.model.verification.VerificationUrl
import com.b2broker.b2core.presentation.event.dispatcher.EventDispatcher
import com.b2broker.b2core.presentation.state.UiStateHolder
import com.b2broker.b2core.verification.upgrade.event.VerificationUpgradeEvent.Upgraded
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel(assistedFactory = VerificationUpgradeViewModel.Factory::class)
internal class VerificationUpgradeViewModel
@AssistedInject constructor(
    private val uiStateHolder: UiStateHolder<VerificationUpgradeUiState>,
    eventDispatcher: EventDispatcher,
    @Assisted val verificationUrl: VerificationUrl,
) : ViewModel(),
    UiStateHolder<VerificationUpgradeUiState> by uiStateHolder,
    EventDispatcher by eventDispatcher {

    var filePathCallback: ValueCallback<Array<Uri>>? = null
        private set

    @AssistedFactory
    interface Factory {
        fun create(url: VerificationUrl): VerificationUpgradeViewModel
    }

    init {
        updateState {
            copy(
                url = verificationUrl,
                isInProgress = true,
            )
        }
    }

    fun onPageLoaded() {
        updateState {
            copy(
                isInProgress = false,
            )
        }
    }

    fun onFilePathCallback(callback: ValueCallback<Array<Uri>>?) {
        filePathCallback = callback
    }

    fun onFileReceived(uri: Uri?) {
        filePathCallback?.onReceiveValue(
            uri?.let { arrayOf(uri) },
        )
        onFilePathCallback(null)
    }

    fun onRefresh() {
        updateState {
            copy(
                isInProgress = false,
                isRefreshing = true,
            )
        }
    }

    fun onUpgraded() {
        dispatch(Upgraded)
    }
}
