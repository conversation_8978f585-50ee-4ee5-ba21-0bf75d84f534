# TabAboutContent E2E Tests

Этот пакет содержит комплексные E2E тесты для функциональности TabAboutContent в B2Trader Market Info экране.

## Структура тестов

### 1. TabAboutContentFlowTests.kt
Основные функциональные тесты для TabAboutContent:

- **testAssertAboutTabSpotMarket** - Проверка отображения полей для SPOT рынков
- **testAssertAboutTabCfdMarket** - Проверка отображения полей для CFD рынков  
- **testAssertAboutTabPerpetualMarket** - Проверка отображения полей для PERPETUAL рынков
- **testAboutTabFieldClickOpensBottomSheet** - Проверка открытия bottom sheet при клике на поля
- **testAboutTabCfdFieldsClickable** - Проверка кликабельности всех полей CFD рынка
- **testAboutTabLoadingState** - Проверка состояния загрузки
- **testAboutTabNavigationBetweenMarketTypes** - Проверка навигации между разными типами рынков
- **testAboutTabContentScrolling** - Проверка прокрутки контента
- **testAboutTabErrorStateHandling** - Проверка обработки ошибок
- **testAboutTabDataConsistencyAcrossTabSwitches** - Проверка консистентности данных при переключении табов

### 2. TabAboutContentInteractionTests.kt
Тесты взаимодействий и пользовательского опыта:

- **testAboutTabBottomSheetInteractionsAllFields** - Проверка взаимодействий с bottom sheet для всех полей
- **testAboutTabBottomSheetDismissMethods** - Проверка различных способов закрытия bottom sheet
- **testAboutTabFieldValuesFormatting** - Проверка форматирования значений полей
- **testAboutTabAccessibilityAndUsability** - Проверка доступности и удобства использования
- **testAboutTabPerformanceRapidInteractions** - Проверка производительности при быстрых взаимодействиях
- **testAboutTabStatePreservationOrientationChanges** - Проверка сохранения состояния при изменении ориентации
- **testAboutTabEdgeCases** - Проверка граничных случаев
- **testAboutTabBottomSheetContentValidation** - Проверка содержимого bottom sheet
- **testAboutTabMemoryAndResourceManagement** - Проверка управления памятью и ресурсами
- **testAboutTabConcurrentInteractions** - Проверка одновременных взаимодействий

### 3. TabAboutContentIntegrationTests.kt
Интеграционные тесты и тесты совместимости:

- **testAboutTabIntegrationWithMarketSelection** - Интеграция с процессом выбора рынка
- **testAboutTabDataConsistencyWithTrading** - Консистентность данных с торговой функциональностью
- **testAboutTabWithRealTimeUpdates** - Поведение при обновлениях данных в реальном времени
- **testAboutTabWithAccountSwitching** - Поведение при переключении аккаунтов
- **testAboutTabWithNetworkIssues** - Поведение при проблемах с сетью
- **testAboutTabWithDifferentPermissions** - Поведение с разными правами пользователя
- **testAboutTabWithMarketStatusChanges** - Поведение при изменении статуса рынка
- **testAboutTabDeepLinking** - Прямая навигация и deep linking
- **testAboutTabWithConcurrentRequests** - Поведение при одновременных запросах
- **testAboutTabMemoryManagementMultipleMarkets** - Управление памятью при работе с несколькими рынками

## Паттерны тестирования

### Наследование от B2TraderBaseTest
Все тесты наследуются от `B2TraderBaseTest`, который предоставляет:
- Настройку mock сервера
- Аутентификацию пользователя
- WebSocket соединение для B2Trader
- Базовые константы (ACCOUNT_ID, MOCK_ROOT_ASSET_CODE)

### Навигация
Стандартный паттерн навигации к TabAboutContent:
```kotlin
private fun navigateToAboutTab(marketName: String): TabAboutTestScreen {
    B2TraderPlatformTestScreen()
        .openB2Trader(ACCOUNT_ID)
        .assertDisplayed()
        .openMarkets()
        .searchAndSelectMarket(marketName)

    hasTestTag(MarketInfoTestTags.MARKET_NAME).click()

    return MarketInfoTestScreen()
        .assertDisplayed()
        .selectAboutTab()
        .assertDisplayed()
}
```

### Проверка полей
Используется метод `assertField(label, value)` для проверки отображения полей:
```kotlin
aboutTabScreen.assertField("Type", "SPOT")
aboutTabScreen.assertField("Base asset", "BTC")
```

### Проверка Bottom Sheet
Стандартный паттерн для проверки bottom sheet:
```kotlin
val bottomSheetScreen = aboutTabScreen.clickOnField("Price scale")
bottomSheetScreen
    .assertDisplayed()
    .assertTitle("Price scale")
    .assertDescription("Number of decimal places")
    .dismiss()
    .assertDisplayed()
```

## Типы рынков

Тесты покрывают все типы рынков:

### SPOT рынки
- Поля: Type, Base asset, Quote asset, Full name, Min. amount, Amount scale, Price scale, Price deviation, Slippage rate
- Пример: "BTC/USDT"

### CFD рынки  
- Поля: Type, Subtype, Base asset, Quote asset, Full name, Price scale, Tick size, Price deviation, Lot size, Min/Max order size, Lot step, Max leverage, Custom leverage, Swap type, Swap volumes
- Пример: "CFD ETH/EUR"

### PERPETUAL рынки
- Поля: Аналогично CFD, но с Type = "PERPETUAL" и специфичными для perpetual контрактов полями
- Пример: "PERP BTC/USDT"

## Allure отчетность

Все тесты аннотированы для Allure отчетов:
- `@Epic("B2TRADER")`
- `@Feature("Market Info")` 
- `@Story("About Tab")`
- `@AllureId("70001")` - уникальные ID для каждого теста
- `@DisplayName("...")` - описательные названия

## Запуск тестов

Тесты можно запускать:
- Все тесты: `./gradlew cAT`
- Только mock тесты: `./gradlew cAT -Pandroid.testInstrumentationRunnerArguments.annotation=com.b2broker.b2core.testdevice.Mock`
- Конкретный класс: `./gradlew cAT -Pandroid.testInstrumentationRunnerArguments.class=com.b2broker.b2core.tests.b2trader.marketinfo.TabAboutContentFlowTests`

## Зависимости

Тесты используют:
- Hilt для dependency injection
- Ultron для UI тестирования
- Allure для отчетности
- Mock сервер для данных
- WebSocket соединения для real-time данных
